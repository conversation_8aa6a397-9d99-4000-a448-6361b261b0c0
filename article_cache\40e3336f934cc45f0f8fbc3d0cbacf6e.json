{"title": "一手实测首款时尚Agent Gensmo，我要把衣柜清空了", "author": "卡尔的AI沃茨", "publish_time": "2025年07月21日 21:39", "content_html": "<div class=\"rich_media_content js_underline_content autoTypeSetting24psection\" id=\"js_content\" style=\"\"><section class=\"js_darkmode__0\" data-author=\"dui!\" data-pm-slice=\"0 0 []\" style='font-weight: 400; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; margin: 0px; padding: 0px; background-color: rgb(255, 255, 255); border-radius: 12px; visibility: visible;'><section class=\"js_darkmode__1\" data-author=\"dui!\" data-pm-slice=\"0 0 []\" style='font-weight: 400; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", <PERSON><PERSON>, sans-serif; margin: 0px; padding: 0px; background-color: rgb(255, 255, 255); border-radius: 12px; visibility: visible;'><p class=\"js_darkmode__2\" style=\"font-weight: 400; font-family: inherit; word-break: break-all; min-height: 20px; color: rgb(27, 27, 27); font-size: 16px; line-height: 1em; margin: 0px 0px 16px; visibility: visible;\"><span style='color: inherit; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">承认吧，AI 已经彻底卷进了我们的生活。</span></span></span></p><p class=\"js_darkmode__3\" style=\"font-weight: 400; font-family: inherit; word-break: break-all; min-height: 20px; color: rgb(27, 27, 27); font-size: 16px; line-height: 1.8; margin: 16px 0px; visibility: visible;\"><span style='color: inherit; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">最近我试了设计Agent、办公Agent、视频Agent、故事Agent等等等等各种Agent，</span></span></span></p><section style=\"text-align: center !important; margin-bottom: 12px; visibility: visible;\"><section class=\"js_darkmode__4\" nodeleaf=\"\" style=\"display: inline-block; margin: 0px; box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px; border-radius: 5px; line-height: 0px; visibility: visible;\"></section></section><p class=\"js_darkmode__5\" style=\"font-weight: 400; font-family: inherit; word-break: break-all; min-height: 20px; color: rgb(27, 27, 27); font-size: 16px; line-height: 1.8; margin: 16px 0px; visibility: visible;\"><span style='color: inherit; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">但有一种是你绝对想不到的，非常好玩的一种，</span></span></span></p><p class=\"js_darkmode__6\" style=\"font-weight: 400; font-family: inherit; word-break: break-all; min-height: 20px; color: rgb(27, 27, 27); font-size: 16px; line-height: 1.8; margin: 16px 0px; visibility: visible;\"><span style='color: inherit; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">那就是</span><span style=\"letter-spacing: 1px; font-weight: bold; visibility: visible;\" textstyle=\"\">时尚Agent「Gensmo」</span></span></span></p><p class=\"js_darkmode__7\" style=\"font-weight: 400; font-family: inherit; word-break: break-all; min-height: 20px; color: rgb(27, 27, 27); font-size: 16px; line-height: 1.8; margin: 16px 0px; visibility: visible;\"><span style='color: inherit; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">音译过来就是</span></span></span><span class=\"js_darkmode__8\" style='font-weight: bold; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(0, 0, 0); visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">真时髦，</span><span style=\"letter-spacing: 1px; font-weight: normal; visibility: visible;\" textstyle=\"\">先看看他们为啥要做时尚Agent。</span></span></span></p><p class=\"js_darkmode__9\" style=\"font-weight: 400; font-family: inherit; word-break: break-all; min-height: 20px; color: rgb(27, 27, 27); font-size: 16px; line-height: 1.8; margin: 16px 0px; visibility: visible;\"><span style='color: inherit; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">我刚发现它的时候就觉得这名字取得太好了，一下子就知道它是个做啥的，而且非常想要试试。在体验了几天之后，我愿称之为它就是真实世界的</span></span></span><span class=\"js_darkmode__10\" style='font-weight: bold; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(0, 0, 0); visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">AI QQ秀！</span></span></span></p><p class=\"js_darkmode__11\" style=\"font-weight: 400; font-family: inherit; word-break: break-all; min-height: 20px; color: rgb(27, 27, 27); font-size: 16px; line-height: 1.8; margin: 16px 0px; visibility: visible;\"><span style='color: inherit; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; font-weight: bold; visibility: visible;\" textstyle=\"\">不是因为它“会生成”，而是因为它真的帮我“装上了”。</span></span></span></p><p class=\"js_darkmode__12\" style=\"font-weight: 400; font-family: inherit; word-break: break-all; min-height: 20px; color: rgb(27, 27, 27); font-size: 16px; line-height: 1.8; margin: 16px 0px; visibility: visible;\"><span style='color: inherit; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"letter-spacing: 1px; visibility: visible;\" textstyle=\"\">一个 AI 能用一句话把我从灵感、试衣间一路带到下单页面，甚至能够帮我挑选一些我没尝试过的风格，看它给我搭的的这几套衣服，这比我自己挑的都好。</span></span></span></p><section style=\"margin-bottom: 18px; display: flex; justify-content: space-between; flex-shrink: 0; visibility: visible;\"><section key=\"256\" style='display: inline-block; flex: 1 1 0%; color: inherit; font-size: 16px; font-family: \"SF Pro SC\", \"SF Pro Text\", \"SF Pro Icons\", PingFangSC-Light, \"PingFang SC\", \"Helvetica Neue\", Helvetica, Arial, sans-serif; line-height: 2em; margin: 0px; padding: 0px 4px; visibility: visible;'><section style=\"visibility: visible;\"><section style=\"text-align: center !important; margin-bottom: 12px; visibility: visible;\"><section class=\"js_darkmode__13\" nodeleaf=\"\" style=\"display: inline-block; margin: 0px; box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px; border-radius: 5px; line-height: 0px; visibility: visible;\"></section></section></section></section><section key=\"257\" style='display: inline-block; flex: 1 1 0%; color: inherit; font-size: 16px; font-family: \"SF Pro SC\", \"SF Pro Text\", \"SF Pro Icons\", PingFangSC-Light, \"PingFang SC\", \"Helvetica Neue\", Helvetica, Arial, sans-serif; line-height: 2em; margin: 0px; padding: 0px 4px; visibility: visible;'><section style=\"visibility: visible;\"><section style=\"text-align: center !important; margin-bottom: 12px; visibility: visible;\"><section class=\"js_darkmode__14\" nodeleaf=\"\" style=\"display: inline-block; margin: 0px; box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px; border-radius: 5px; line-height: 0px; visibility: visible;\"></section></section></section></section><section key=\"260\" style='display: inline-block; flex: 1 1 0%; color: inherit; font-size: 16px; font-family: \"SF Pro SC\", \"SF Pro Text\", \"SF Pro Icons\", PingFangSC-Light, \"PingFang SC\", \"Helvetica Neue\", Helvetica, Arial, sans-serif; line-height: 2em; margin: 0px; padding: 0px 4px; visibility: visible;'><section style=\"visibility: visible;\"><section style=\"text-align: center !important; margin-bottom: 12px; visibility: visible;\"><section class=\"js_darkmode__15\" nodeleaf=\"\" style=\"display: inline-block; margin: 0px; box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px; border-radius: 5px; line-height: 0px; visibility: visible;\"></section></section></section></section></section><p class=\"js_darkmode__16\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">而且Gensmo不只是可以换衣服那么简单，它还带来了一个 AI Stylist，能听懂你的每一个念头、为你排满一周造型、风格不重样、尺码不出错，甚至还能直跳转去买。</span></span></span></p><p class=\"js_darkmode__17\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">这种体验太独特了，有一种我直接把商场和导购都搬回家了的即视感，接下来我们一起来看看我的AI穿搭试衣助理 Gensmo 都能做些啥吧。</span></span></span></p><h3 class=\"js_darkmode__19\" style=\"font-weight: bold;color: inherit;font-size: 20px;line-height: undefined;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;padding-bottom: 12px;color: #000000;font-size: 24px;\"><span style=\"color: inherit;author: 7231935989239840796;font-size: inherit;color: inherit;font-weight: bold;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span class=\"js_darkmode__20\" style=\"font-size: 20px;letter-spacing: 1px;background-color: rgb(0, 0, 0);color: rgb(255, 255, 255);\" textstyle=\"\"> 01｜一键AI分身 </span></span></span></h3><p class=\"js_darkmode__21\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">一个换装Agent最重要的就是“穿衣服”了，而且是要把衣服穿在我身上才行。</span></span></span></p><p class=\"js_darkmode__22\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">以往我们玩的很多这种制作分身然后生成写真照片的工具，基本都是工具内已经设定的好服装造型，自由度比较低。</span></span></span></p><p class=\"js_darkmode__23\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">这次在Gensmo里，我用一张自拍照就制作了出自己的分身，这个脸部特征保持的还是很一致的。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"130\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__24\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"132\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__25\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__26\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">我在买衣服的时候最关注的两个事，</span><span style=\"letter-spacing: 1px;font-weight: bold;\" textstyle=\"\">一个是脸，另一个就是身材</span><span style=\"letter-spacing: 1px;\" textstyle=\"\">，总而言之，我做出来的这个分身脸要和我像，身材也要和我像，我才能看到这套衣服穿在我身上的效果。</span></span></span></p><p class=\"js_darkmode__27\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">所以Gensmo在制作分身的过程中，可以通过提示语说明自己的身高体重和体型特征，</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"136\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__28\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"137\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__29\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"140\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__30\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__31\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">然后获得和自己相似的体型，这里我分别输入了65公斤、70公斤和100公斤体重的区别。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"145\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__32\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"146\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__33\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"150\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__34\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__35\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">这真的很贴心了，大大提升了我的线上试穿体验，至少我能知道这件衣服穿在我身上的大概效果。</span></span></span></p><p class=\"js_darkmode__36\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">然后，我就可以在首页找到自己想要的搭配，直接点击“try on”，就可以完成试穿，制作分身+试穿效果生成也不过就几分钟，几乎没有什么等待的感觉。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"158\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__37\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"159\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__38\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__39\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">这几套衣服还真是我没想过自己穿上会是这样的效果。和我选择的图片中衣服细节确实一致，同时生成后的图片没有很强的AI感，衣服的褶皱和服帖程度都比较自然。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"165\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__40\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"166\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__41\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"167\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__42\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__43\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">而且我还能无痛尝试很多我自己平时想都没想过的风格，要知道我这个大i人，这种衣服在商场里我是不敢试的（穿上会有点潮人羞耻感，属于是有贼心没贼胆）但我在Gensmo里，这把总算是试了个够。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"173\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__44\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"174\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__45\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"175\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__46\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p><span leaf=\"\">同样的设置放在女生上也非常合适：</span></p><section nodeleaf=\"\" style=\"text-align: center;\"></section><h3 class=\"js_darkmode__48\" style='font-weight: bold;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;padding-bottom: 12px;color: rgb(0, 0, 0);font-size: 24px;'><span leaf=\"\" style='font-size: inherit;color: inherit;font-weight: bold;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;'><span class=\"js_darkmode__49\" style=\"font-size: 20px;letter-spacing: 1px;background-color: rgb(0, 0, 0);color: rgb(255, 255, 255);\" textstyle=\"\"> 02｜所见即所得 </span></span></h3><p class=\"js_darkmode__50\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">当然，Gensmo能做的绝对不止试衣服这么简单。我花了这么久时间试衣服，终于试到一套喜欢的衣服，可不是为了就看看照片就完事了。</span></span></span></p><p class=\"js_darkmode__51\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;font-weight: bold;\" textstyle=\"\">我！要！买！到！它！立刻！马上！</span></span></span></p><p class=\"js_darkmode__52\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">这件事在Gensmo里也是可以的，上面我们做过的所有穿搭单品，实际上都是链接了购买网站的，</span></span></span></p><section nodeleaf=\"\"><span class=\"video_iframe rich_pages\" data-cover=\"http%3A%2F%2Fmmbiz.qpic.cn%2Fmmbiz_jpg%2FYEhakvKZjXlDx11O66TTRD6fvyNsEqic9XgiaIKqiaaoiaDShy1e7ia2g1pWAawagRfib9wNTKllGKVmhRtn6jfDLrew%2F0%3Fwx_fmt%3Djpeg\" data-mpvid=\"wxv_4085334284796428291\" data-src=\"https://mp.weixin.qq.com/mp/readtemplate?t=pages/video_player_tmpl&amp;auto=0&amp;vid=wxv_4085334284796428291\" data-vh=\"378.5625\" data-vidtype=\"2\" data-vw=\"673\" height=\"395\" id=\"js_mp_video_container_0\" scrolling=\"no\" style=\"width: 677px !important; height: 395px !important; overflow: hidden;\" vid=\"wxv_4085334284796428291\" width=\"677\"><div id=\"page-content\"> <!--S 全屏播放 full_screen_mv--> <div id=\"js_mpvedio_wrapper_wxv_4085334284796428291\" style=\"position:relative;height:100%\">  <div class=\"feed-wrapper\"><div aria-hidden=\"true\" aria-modal=\"true\" class=\"wx_bottom_modal_wrp player_relate_video_dialog weui-half-screen-dialog_fold\" hidewhensideslip=\"\" role=\"dialog\" style=\"visibility: hidden; display: none;\" tabindex=\"0\"><div class=\"weui-half-screen-dialog wx_bottom_modal\" style=\"max-height: none;\"><div class=\"wx_bottom_modal_group_container\" style=\"transform: translateX(calc(0% + 0px)); max-height: none;\"><div aria-hidden=\"false\" class=\"wx_bottom_modal_group\" style=\"left: 0%; max-height: none;\"><div class=\"weui-half-screen-dialog__hd__wrp\"><div class=\"weui-half-screen-dialog__hd\"><div class=\"weui-half-screen-dialog__hd__side\"><button class=\"weui-btn_icon weui-wa-hotarea\">关闭</button></div><div class=\"weui-half-screen-dialog__hd__main\"><strong class=\"weui-half-screen-dialog__title\">观看更多</strong></div><div class=\"weui-half-screen-dialog__hd__side\"><!-- --><button class=\"weui-btn_icon weui-wa-hotarea\" style=\"display: none;\">更多</button></div></div></div><!-- --></div></div></div></div><div class=\"infinity-list__wrapper\" style=\"height: 379px;\"><div class=\"\" style=\"height: 379px; overflow: visible;\"><div class=\"infinity-list__page destory-enter-to\" data-key=\"wxv_4085334284796428291\" infinity-idx=\"0\" style=\"height: 379px; position: absolute; top: 0px; opacity: 1;\"><div class=\"mp-video-player\" data-v-1639cf84=\"\" style=\"height: 100%;\"><div class=\"js_mpvedio page_video_wrapper\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" id=\"js_mpvedio_1753550653602_1747410842955\"><div class=\"js_page_video page_video ratio_primary align_upper_center page_video_without-control page_video_skin-normal\" data-v-823ba74e=\"\" style=\"display: block; width: 100%; height: 379px;\"><div class=\"full_screen_opr wx_video_play_opr\" data-v-823ba74e=\"\" style=\"\"><button class=\"mid_play_box reset_btn\" data-v-823ba74e=\"\" type=\"button\"><span class=\"aria_hidden_abs\" data-v-823ba74e=\"\">，时长</span><span class=\"video_length\" data-v-823ba74e=\"\">00:20</span></button></div><!-- --><div class=\"mid_opr fast_pre_next\" data-v-823ba74e=\"\" style=\"display: none;\"><p class=\"video_length\" data-v-823ba74e=\"\"><span class=\"played_time js_forward_play_time\" data-v-823ba74e=\"\">0</span><span class=\"js_forward_seperator\" data-v-823ba74e=\"\">/</span><span class=\"total_time js_forward_total_time\" data-v-823ba74e=\"\">0</span></p></div><div class=\"wx_video_progress_msg full_screen_opr\" data-v-823ba74e=\"\" role=\"alert\" style=\"display: none;\"><div class=\"wx_video_progress_msg_inner\" data-v-823ba74e=\"\"><span class=\"wx_video_progress_current\" data-v-823ba74e=\"\">00:00</span><span class=\"wx_video_progress_gap\" data-v-823ba74e=\"\">/</span><span class=\"wx_video_progress_total\" data-v-823ba74e=\"\">00:20</span></div></div><div class=\"video_screen_mode_switch\" data-v-823ba74e=\"\" style=\"bottom: calc(50% - 336px); display: none;\"><button class=\"reset_btn video_screen_mode_switch_btn weui-wa-hotarea\" data-v-823ba74e=\"\" type=\"button\"> 切换到横屏模式 </button></div><div class=\"full_screen_opr wx_video_pause_full_mod\" data-v-823ba74e=\"\" style=\"display: none;\"><button class=\"reset_btn wx_video_pause_full_btn\" data-v-823ba74e=\"\" type=\"button\">继续播放</button></div><div class=\"js_control video_opr video_opr_sns\" data-v-823ba74e=\"\" style=\"bottom: 0px; display: none;\"><div class=\"opr_inner\" data-v-823ba74e=\"\"><div class=\"opr_inner_fl\" data-v-823ba74e=\"\"><div class=\"js_switch weui-wa-hotarea switch switch_on\" data-v-823ba74e=\"\"><a class=\"btn_opr\" data-v-823ba74e=\"\" href=\"javascript:;\" role=\"button\">播放</a></div><div data-v-823ba74e=\"\" role=\"option\"><div class=\"played_time js_now_play_time\" data-v-823ba74e=\"\">00:00</div><span data-v-823ba74e=\"\">/</span><div class=\"total_time js_total_time\" data-v-823ba74e=\"\">00:20</div></div><!-- --><div class=\"total_time js_total_time\" data-v-823ba74e=\"\" role=\"option\" style=\"display: none;\">00:20</div></div><div class=\"opr_inner_fr\" data-v-823ba74e=\"\"><!-- --><!-- --><!-- --><div class=\"weui-wa-hotarea js_full_screen_control screenSize_control full\" data-v-823ba74e=\"\" role=\"button\"><i class=\"icon_control\" data-v-823ba74e=\"\">全屏</i></div></div></div></div><div class=\"full_screen_opr video_quick_play_context\" data-v-823ba74e=\"\" role=\"alert\" style=\"display: none;\"><div class=\"video_quick_play_msg\" data-v-823ba74e=\"\"> 倍速播放中 </div></div><div class=\"js_sub_setting video_full-screen__footer video_full-screen__footer__sub-setting hide\" data-v-823ba74e=\"\"><div class=\"video_full-screen__sub-setting video_full-screen__sub-setting__speed js_playback_mode_select\" data-v-823ba74e=\"\" style=\"display: none;\"><a class=\"video_full-screen__sub-setting__item js_playback_0\" data-v-823ba74e=\"\" href=\"javascript:;\"> 0.5倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_1\" data-v-823ba74e=\"\" href=\"javascript:;\"> 0.75倍 </a><a class=\"video_full-screen__sub-setting__item current js_playback_2\" data-v-823ba74e=\"\" href=\"javascript:;\"> 1.0倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_3\" data-v-823ba74e=\"\" href=\"javascript:;\"> 1.5倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_4\" data-v-823ba74e=\"\" href=\"javascript:;\"> 2.0倍 </a></div><div class=\"video_full-screen__sub-setting video_full-screen__sub-setting__ratio js_play_mode_select\" data-v-823ba74e=\"\" style=\"display: none;\"><a class=\"video_full-screen__sub-setting__item current js_resolution_0\" data-v-823ba74e=\"\" href=\"javascript:;\"> 超清 </a><a class=\"video_full-screen__sub-setting__item js_resolution_1\" data-v-823ba74e=\"\" href=\"javascript:;\"> 流畅 </a></div></div><div class=\"js_inner inner not_fullscreen\" data-v-823ba74e=\"\"><div class=\"js_video_poster video_poster\" data-v-823ba74e=\"\" style=\"display: none;\"><video class=\"\" controlslist=\"nodownload\" crossorigin=\"anonymous\" data-v-823ba74e=\"\" playsinline=\"isiPhoneShowPlaysinline\" poster=\"http://mmbiz.qpic.cn/mmbiz_jpg/YEhakvKZjXlDx11O66TTRD6fvyNsEqic9XgiaIKqiaaoiaDShy1e7ia2g1pWAawagRfib9wNTKllGKVmhRtn6jfDLrew/0?wx_fmt=jpeg&amp;wxfrom=16\" preload=\"metadata\" src=\"https://mpvideo.qpic.cn/0b2egabbqaac4eadxyt445ufemgddayaegaa.f10002.mp4?dis_k=d074b49359340d97ac83d48546fc55f3&amp;dis_t=1753550649&amp;play_scene=10120&amp;auth_info=dPmL/nwfBzD0nsSmAlxxSnQiZQw2SEE0FXM+IUNfNz5KKw5OFnVuRD0eTSUiTg==&amp;auth_key=0a8a6654911a6c9fa63d619a4ee9835e&amp;vid=wxv_4085334284796428291&amp;format_id=10002&amp;support_redirect=0&amp;mmversion=false\" webkit-playsinline=\"isiPhoneShowPlaysinline\"> 您的浏览器不支持 video 标签 </video></div><div class=\"video_poster__info\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"display: none;\"><p class=\"video_poster__info__title\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"font-size: 17px;\">继续观看</p><p class=\"video_poster__info__desc\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"font-size: 12px;\"> 一手实测首款时尚Agent Gensmo，我要把衣柜清空了 </p></div></div><div class=\"video_profile_area\" data-v-823ba74e=\"\" style=\"display: none;\"><div data-v-823ba74e=\"\"><button class=\"reset_btn video_profile_relate_video_btn js_wx_tap_highlight wx_tap_link\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"display: none;\">观看更多</button></div><div data-v-823ba74e=\"\" role=\"link\" style=\"width: fit-content; max-width: 100%;\" tabindex=\"0\"><div class=\"weui-wa-hotarea video_profile_desc_wrp\" data-v-823ba74e=\"\" role=\"option\"><div class=\"weui-hidden_abs\" data-v-823ba74e=\"\">,</div><div class=\"video_profile_desc\" data-v-823ba74e=\"\">一手实测首款时尚Agent Gensmo，我要把衣柜清空了</div></div></div><div class=\"video_profile_wrp weui-flex\" data-v-823ba74e=\"\"><div class=\"video_profile weui-flex weui-flex__item\" data-v-823ba74e=\"\"><span class=\"video_profile_nickname weui-wa-hotarea\" data-v-823ba74e=\"\">卡尔的AI沃茨</span><button class=\"reset_btn video_profile_follow_btn weui-wa-hotarea\" data-v-823ba74e=\"\" style=\"display: none;\" type=\"button\">已关注</button></div><div class=\"video_sns_context\" data-v-823ba74e=\"\" style=\"display: none;\"><button class=\"video_sns_btn video_sns_btn_praise\" data-v-823ba74e=\"\" title=\"\" type=\"button\"><span class=\"video_sns_num\" data-v-823ba74e=\"\">点赞</span></button><button class=\"video_sns_btn video_sns_btn_love\" data-v-823ba74e=\"\" title=\"\" type=\"button\"><span class=\"video_sns_num\" data-v-823ba74e=\"\">在看</span></button></div></div></div></div></div></div></div></div></div><!-- --></div> </div> <!--E 视频播放器--> <!-- S 视频社交--> <div class=\"interact_video\" id=\"bottom_bar\" style=\"display:none;height: 35px;\"> <div class=\"inter_opr\"> <a class=\"access_original\" href=\"javascript:;\" id=\"video_detail_btn\" target=\"_blank\">         视频详情       </a> </div> </div> </div></span></section><p class=\"js_darkmode__53\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">也就是说，我可以一个流程完成：</span></span></span></p><p class=\"js_darkmode__54\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span class=\"js_darkmode__55\" style='font-weight: bold; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(0, 0, 0);'><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">创建自己的分身 -&gt; 穿搭灵感搜索 -&gt; 一键试穿 -&gt; 购买 -&gt; 发布社区</span></span></span></p><p class=\"js_darkmode__56\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">而且甚至我可以因为喜欢一件单品，让Gensmo帮我一键搭配一整套都可以购买到的穿搭。就比如我喜欢这件外套，可以告诉它</span><span style=\"letter-spacing: 1px;font-weight: bold;\" textstyle=\"\">“Complete the look”</span><span style=\"letter-spacing: 1px;\" textstyle=\"\">，Gensmo就可以给我好几套含有这件单品的穿搭，颜色搭配都挺和谐的。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"194\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__57\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__58\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"195\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__59\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__60\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__61\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">这事可做的太全面了，就是目前Gensmo还是在海外使用阶段，不敢想象这要是在国内能直接用得多舒服。</span></span></span></p><h3 class=\"js_darkmode__63\" style='font-weight: bold;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;padding-bottom: 12px;color: rgb(0, 0, 0);font-size: 24px;'><span leaf=\"\" style='font-size: inherit;color: inherit;font-weight: bold;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;'><span class=\"js_darkmode__64\" style=\"font-size: 20px;letter-spacing: 1px;background-color: rgb(0, 0, 0);color: rgb(255, 255, 255);\" textstyle=\"\"> 03｜实用场景 </span></span></h3><p class=\"js_darkmode__65\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">平时想要购买衣服的时候，我还会有一种情况：</span></span></span></p><p class=\"js_darkmode__66\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span class=\"js_darkmode__67\" style='font-weight: bold; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(0, 0, 0);'><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">我有一个特殊的场合需要一套相应风格的服装，但我不知道我具体想要什么。</span></span></span></p><p class=\"js_darkmode__68\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">这种情况下，如果我们在线下商场里，一般就是找导购推荐了。现在在Gensmo中，就是直接在它内置的Agent对话框中提问就可以了。就比如：</span></span></span></p><section class=\"js_darkmode__69\" style=\"display: flex;place-items: flex-start;border-color: #fed4a4;background-color: rgb(255,245,235);margin-bottom: 20px;padding: 16px 14px;border: 1px solid rgb(254,212,164);border-radius: 8px;text-align: left!important;\"><section><span style=\"display: flex;align-items: center;justify-content: center;display: block;\"><span style=\"font-size: inherit;margin-right: 8px;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">❤️ </span><span style=\"font-size: 16px;letter-spacing: 1px;\" textstyle=\"\">我需要在一个时尚主题的商业晚宴中搭配一套服装，不要过分夸张显眼，要有高级感和时尚感。</span></span></span></span></section></section><p class=\"js_darkmode__70\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">然后我就能够一套合适的推荐，</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"202\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__71\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"203\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__72\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__73\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">即使其中有部分单品不够满意，也可以持续多轮对话让它修改直到我们满意为止。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"213\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__74\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"214\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__75\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__76\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">得到想要的衣服后，可以直接给我建模好的分身试穿，还可以再和Gensmo对话把我的分身放到合适的场景中，看看这套衣服放在晚宴中的氛围感是不是合适。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"227\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__77\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"228\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__78\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"230\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__79\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__80\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">也可以把看中的穿搭发给它，让它帮我搭配一套可以直接购买的相似同款。可以说是，可玩性和实用性兼具了。</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"233\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__81\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"234\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__82\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><h3 class=\"js_darkmode__84\" style='font-weight: bold;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;padding-bottom: 12px;color: rgb(0, 0, 0);font-size: 24px;'><span leaf=\"\" style='font-size: inherit;color: inherit;font-weight: bold;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;'><span class=\"js_darkmode__85\" style=\"font-size: 20px;letter-spacing: 1px;background-color: rgb(0, 0, 0);color: rgb(255, 255, 255);\" textstyle=\"\"> 04｜时尚Agent还能做什么？ </span></span></h3><p class=\"js_darkmode__86\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">如果说Gensmo定位成是一个时尚Agent的话，除了搭配衣服，它还应该可以做更多的事情，做一个人类时尚搭配师平时要花很多时间做的事情。</span></span></span></p><p class=\"js_darkmode__87\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">比如，它设置了很多穿搭专题：</span></span></span></p><section style=\"margin-bottom: 18px;display: flex;justify-content: space-between;flex-shrink: 0;\"><section key=\"248\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__88\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"244\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__89\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section><section key=\"245\" style=\"display: inline-block;flex: 1;color: inherit;font-size: 16px;font-family: SF Pro SC,SF Pro Text,SF Pro Icons,PingFangSC-Light,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif;line-height: 2em;margin: 0;minHeight: 20px;letterSpacing: auto;padding: 0 4px;\"><section><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__90\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section></section></section></section><p class=\"js_darkmode__91\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">这真是太酷了，假设说，假设说奥，我是经常有不同风格出街需求的潮人（很潮的那种），我用Gensmo真的可以帮我省下不少功夫，或者说我同样可以用它来当作我的灵感，帮我快速完成穿搭规划。</span></span></span></p><p class=\"js_darkmode__92\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">而且Gensmo站内自身就有一个类似小红书一样的社交圈，我们进入首页就能看到很多时尚达人发布自己的穿搭，不仅可以直接copy穿搭，还可以发布自己的灵感。</span></span></span></p><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__93\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section><p class=\"js_darkmode__94\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">甚至，Gensmo已经在海外Tiktok上非常火了。。。相关内容累计播放量近一亿，TT一个单条爆款视频的播放量超1400万。</span></span></span></p><section style=\"text-align: center!important;margin-bottom: 12px;\"><section class=\"js_darkmode__95\" nodeleaf=\"\" style=\"display: inline-block;margin: 0px;box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px;border-radius: 5px;line-height: 0px;\"></section></section><h1 class=\"js_darkmode__97\" style='font-weight: bold;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;padding-bottom: 12px;color: rgb(0, 0, 0);font-size: 24px;'><span leaf=\"\" style='font-size: inherit;color: inherit;font-weight: bold;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;'><span class=\"js_darkmode__98\" style=\"font-size: 20px;letter-spacing: 1px;background-color: rgb(0, 0, 0);color: rgb(255, 255, 255);\" textstyle=\"\"> 写在最后 </span></span></h1><p class=\"js_darkmode__99\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">我算是看到，有AI真的把“穿搭幻想”跟“真实购买”接上了。</span></span></span></p><p class=\"js_darkmode__100\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">不是停留在换装玩具层面，而是真正通向生活日常的方向的第一步。</span></span></span></p><p class=\"js_darkmode__101\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">甚至我已经能想象，以后我们可以像捏CG角色那样去定制一个完全符合自己体态和气质的“数字分身”，不仅能换发型、换身形，还能试穿各种风格、材质的衣服，然后在商品页上点“试一下”，真人上身，合不合适立刻知道。</span></span></span></p><p class=\"js_darkmode__102\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">那个未来不远，甚至已经来了一点点了。</span></span></span></p><p class=\"js_darkmode__103\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">我肯定会第一批用的，</span></span></span></p><p class=\"js_darkmode__104\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">不仅会用，还会疯狂安利身边所有朋友。</span></span></span></p><p class=\"js_darkmode__105\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">要知道当年我可是</span><span style=\"letter-spacing: 1px;font-weight: bold;\" textstyle=\"\">QQ秀红钻会员</span><span style=\"letter-spacing: 1px;\" textstyle=\"\">，</span></span></span></p><p class=\"js_darkmode__106\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">试衣服这事我可太懂了！</span></span></span></p><p class=\"js_darkmode__107\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">所以，Gensmo什么时候入驻国内啊？</span></span></span></p><p class=\"js_darkmode__108\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">连你中文App的slogan我都想好了：</span></span></span></p><p class=\"js_darkmode__109\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span class=\"js_darkmode__110\" style='font-weight: bold; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(0, 0, 0);'><span leaf=\"\"><span style=\"letter-spacing: 1px;\" textstyle=\"\">搞时髦，真时髦。</span></span></span></p></section><p class=\"js_darkmode__111\" style=\"font-size: 16px;line-height: 2em;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;color: inherit;margin: 0;min-height: 20px;letter-spacing: auto;color: inherit;color: inherit;font-size: inheritpx;font-family: inherit;line-height: inherit;font-size: inherit;font-weight: 400;font-family: inherit;line-height: inherit;word-break: break-all;min-height: 20px;color: #1B1B1B;font-size: 16px;line-height: 1.8;margin: 16px 0;\"><span style=\"color: inherit;author: 7231935989239840796;font-family: PingFang SC,system-ui,-apple-system,BlinkMacSystemFont,Helvetica Neue,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Arial,sans-serif;\"><span leaf=\"\"><span class=\"js_darkmode__112\" style=\"font-size: 14px;letter-spacing: 1px;color: rgb(178, 178, 178);font-weight: normal;\" textstyle=\"\">@ 作者 / 阿汤 &amp; 卡尔</span></span></span></p><p class=\"js_darkmode__114\" data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.75em;letter-spacing: 0.06em;text-align: left;text-indent: 0em;margin: 0px;padding: 8px 0px;\"><span style=\"display: block;text-align: center;color: gray;font-size: 14px;\"><span leaf=\"\"><span style=\"font-size: 15px;letter-spacing: 1px;\" textstyle=\"\">最后，感谢你看到这里👏</span></span></span><span style=\"display: block;text-align: center;color: gray;font-size: 14px;\"><span leaf=\"\"><span style=\"font-size: 15px;letter-spacing: 1px;\" textstyle=\"\">如果喜欢这篇文章，不妨顺手给我们</span></span></span><span style=\"display: block;text-align: center;color: gray;font-size: 14px;\"><em class=\"js_darkmode__115\" style=\"color: rgb(162, 140, 180);font-style: italic;background: no-repeat rgba(0, 0, 0, 0);width: auto;height: auto;margin: 0px;padding: 0px;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\"><span leaf=\"\"><span style=\"font-size: 15px;letter-spacing: 1px;\" textstyle=\"\">点赞👍｜在看👀｜转发📪｜评论📣</span></span></em></span></p><p class=\"js_darkmode__116\" data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1em;letter-spacing: 0.06em;text-align: left;text-indent: 0em;margin: 0px;padding: 8px 0px;\"><span leaf=\"\" style=\"display: block;text-align: center;color: gray;font-size: 14px;\"><span style=\"font-size: 14px;letter-spacing: 1px;\" textstyle=\"\">如果想要第一时间收到推送，不妨给我个星标</span><span style=\"font-size: 16px;letter-spacing: 1px;\" textstyle=\"\">🌟</span></span></p><p class=\"js_darkmode__117\" data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1em;letter-spacing: 0.06em;text-align: left;text-indent: 0em;margin: 0px;padding: 8px 0px;\"><span leaf=\"\" style=\"display: block;text-align: center;color: gray;font-size: 14px;\"><span style=\"font-size: 14px;letter-spacing: 1px;\" textstyle=\"\">更多的内容正在不断填坑中……</span></span></p><figure data-tool=\"mdnice编辑器\" style=\"margin: 10px 0px;padding: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"></span></figure></section></div>", "content_text": "承认吧，AI 已经彻底卷进了我们的生活。最近我试了设计Agent、办公Agent、视频Agent、故事Agent等等等等各种Agent，但有一种是你绝对想不到的，非常好玩的一种，那就是时尚Agent「Gensmo」音译过来就是真时髦，先看看他们为啥要做时尚Agent。我刚发现它的时候就觉得这名字取得太好了，一下子就知道它是个做啥的，而且非常想要试试。在体验了几天之后，我愿称之为它就是真实世界的AI QQ秀！不是因为它“会生成”，而是因为它真的帮我“装上了”。一个 AI 能用一句话把我从灵感、试衣间一路带到下单页面，甚至能够帮我挑选一些我没尝试过的风格，看它给我搭的的这几套衣服，这比我自己挑的都好。而且Gensmo不只是可以换衣服那么简单，它还带来了一个 AI Stylist，能听懂你的每一个念头、为你排满一周造型、风格不重样、尺码不出错，甚至还能直跳转去买。这种体验太独特了，有一种我直接把商场和导购都搬回家了的即视感，接下来我们一起来看看我的AI穿搭试衣助理 Gensmo 都能做些啥吧。 01｜一键AI分身 一个换装Agent最重要的就是“穿衣服”了，而且是要把衣服穿在我身上才行。以往我们玩的很多这种制作分身然后生成写真照片的工具，基本都是工具内已经设定的好服装造型，自由度比较低。这次在Gensmo里，我用一张自拍照就制作了出自己的分身，这个脸部特征保持的还是很一致的。我在买衣服的时候最关注的两个事，一个是脸，另一个就是身材，总而言之，我做出来的这个分身脸要和我像，身材也要和我像，我才能看到这套衣服穿在我身上的效果。所以Gensmo在制作分身的过程中，可以通过提示语说明自己的身高体重和体型特征，然后获得和自己相似的体型，这里我分别输入了65公斤、70公斤和100公斤体重的区别。这真的很贴心了，大大提升了我的线上试穿体验，至少我能知道这件衣服穿在我身上的大概效果。然后，我就可以在首页找到自己想要的搭配，直接点击“try on”，就可以完成试穿，制作分身+试穿效果生成也不过就几分钟，几乎没有什么等待的感觉。这几套衣服还真是我没想过自己穿上会是这样的效果。和我选择的图片中衣服细节确实一致，同时生成后的图片没有很强的AI感，衣服的褶皱和服帖程度都比较自然。而且我还能无痛尝试很多我自己平时想都没想过的风格，要知道我这个大i人，这种衣服在商场里我是不敢试的（穿上会有点潮人羞耻感，属于是有贼心没贼胆）但我在Gensmo里，这把总算是试了个够。同样的设置放在女生上也非常合适： 02｜所见即所得 当然，Gensmo能做的绝对不止试衣服这么简单。我花了这么久时间试衣服，终于试到一套喜欢的衣服，可不是为了就看看照片就完事了。我！要！买！到！它！立刻！马上！这件事在Gensmo里也是可以的，上面我们做过的所有穿搭单品，实际上都是链接了购买网站的，    关闭观看更多更多，时长00:200/000:00/00:20 切换到横屏模式 继续播放播放00:00/00:2000:20全屏 倍速播放中  0.5倍  0.75倍  1.0倍  1.5倍  2.0倍  超清  流畅  您的浏览器不支持 video 标签 继续观看 一手实测首款时尚Agent Gensmo，我要把衣柜清空了 观看更多,一手实测首款时尚Agent Gensmo，我要把衣柜清空了卡尔的AI沃茨已关注点赞在看               视频详情          也就是说，我可以一个流程完成：创建自己的分身 -> 穿搭灵感搜索 -> 一键试穿 -> 购买 -> 发布社区而且甚至我可以因为喜欢一件单品，让Gensmo帮我一键搭配一整套都可以购买到的穿搭。就比如我喜欢这件外套，可以告诉它“Complete the look”，Gensmo就可以给我好几套含有这件单品的穿搭，颜色搭配都挺和谐的。这事可做的太全面了，就是目前Gensmo还是在海外使用阶段，不敢想象这要是在国内能直接用得多舒服。 03｜实用场景 平时想要购买衣服的时候，我还会有一种情况：我有一个特殊的场合需要一套相应风格的服装，但我不知道我具体想要什么。这种情况下，如果我们在线下商场里，一般就是找导购推荐了。现在在Gensmo中，就是直接在它内置的Agent对话框中提问就可以了。就比如：❤️ 我需要在一个时尚主题的商业晚宴中搭配一套服装，不要过分夸张显眼，要有高级感和时尚感。然后我就能够一套合适的推荐，即使其中有部分单品不够满意，也可以持续多轮对话让它修改直到我们满意为止。得到想要的衣服后，可以直接给我建模好的分身试穿，还可以再和Gensmo对话把我的分身放到合适的场景中，看看这套衣服放在晚宴中的氛围感是不是合适。也可以把看中的穿搭发给它，让它帮我搭配一套可以直接购买的相似同款。可以说是，可玩性和实用性兼具了。 04｜时尚Agent还能做什么？ 如果说Gensmo定位成是一个时尚Agent的话，除了搭配衣服，它还应该可以做更多的事情，做一个人类时尚搭配师平时要花很多时间做的事情。比如，它设置了很多穿搭专题：这真是太酷了，假设说，假设说奥，我是经常有不同风格出街需求的潮人（很潮的那种），我用Gensmo真的可以帮我省下不少功夫，或者说我同样可以用它来当作我的灵感，帮我快速完成穿搭规划。而且Gensmo站内自身就有一个类似小红书一样的社交圈，我们进入首页就能看到很多时尚达人发布自己的穿搭，不仅可以直接copy穿搭，还可以发布自己的灵感。甚至，Gensmo已经在海外Tiktok上非常火了。。。相关内容累计播放量近一亿，TT一个单条爆款视频的播放量超1400万。 写在最后 我算是看到，有AI真的把“穿搭幻想”跟“真实购买”接上了。不是停留在换装玩具层面，而是真正通向生活日常的方向的第一步。甚至我已经能想象，以后我们可以像捏CG角色那样去定制一个完全符合自己体态和气质的“数字分身”，不仅能换发型、换身形，还能试穿各种风格、材质的衣服，然后在商品页上点“试一下”，真人上身，合不合适立刻知道。那个未来不远，甚至已经来了一点点了。我肯定会第一批用的，不仅会用，还会疯狂安利身边所有朋友。要知道当年我可是QQ秀红钻会员，试衣服这事我可太懂了！所以，Gensmo什么时候入驻国内啊？连你中文App的slogan我都想好了：搞时髦，真时髦。@ 作者 / 阿汤 & 卡尔最后，感谢你看到这里👏如果喜欢这篇文章，不妨顺手给我们点赞👍｜在看👀｜转发📪｜评论📣如果想要第一时间收到推送，不妨给我个星标🌟更多的内容正在不断填坑中……", "images": [], "word_count": 2604, "url": "https://mp.weixin.qq.com/s/VP1_tnlgnC3e2yr2zOb5gw", "site_type": "微信公众号", "extracted_at": "2025-07-27T01:25:12.937120"}