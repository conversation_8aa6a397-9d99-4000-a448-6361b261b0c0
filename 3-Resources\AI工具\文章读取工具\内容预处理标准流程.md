# 内容预处理标准流程

## 流程概述
**创建时间**：2025-07-19
**目标**：建立标准化的文章内容预处理和格式化流程
**适用范围**：微信公众号文章 → Coze智能体处理
**质量标准**：准确率95%+，格式一致性90%+

## 🔄 完整处理流程

### 阶段一：内容获取
```mermaid
graph LR
    A[微信文章URL] --> B[简悦插件提取]
    B --> C[原始Markdown]
    C --> D[质量检查]
    D --> E{是否完整?}
    E -->|是| F[进入预处理]
    E -->|否| G[重新提取]
    G --> B
```

#### 1.1 获取准备
- **环境检查**：确保简悦插件已安装并配置
- **网络状态**：确认网络连接稳定
- **浏览器状态**：清理缓存，关闭不必要标签页

#### 1.2 提取操作
```
1. 打开目标文章URL
2. 等待页面完全加载（3-5秒）
3. 激活简悦插件（Ctrl+Shift+S）
4. 确认进入阅读模式
5. 导出Markdown格式
6. 保存到指定文件夹
```

#### 1.3 质量检查清单
```
□ 标题提取完整
□ 作者信息存在
□ 正文内容无缺失
□ 图片链接有效
□ 段落结构清晰
□ 无多余广告内容
```

### 阶段二：内容清理

#### 2.1 格式标准化
```python
# 示例处理脚本
import re

def clean_markdown_content(content):
    """清理和标准化Markdown内容"""
    
    # 1. 移除多余空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 2. 标准化标题格式
    content = re.sub(r'^#{1,6}\s*(.+)$', lambda m: f"{'#' * min(len(m.group(0).split()[0]), 6)} {m.group(1).strip()}", content, flags=re.MULTILINE)
    
    # 3. 清理图片链接
    content = re.sub(r'!\[.*?\]\((.*?)\)', r'![image](\1)', content)
    
    # 4. 移除HTML标签残留
    content = re.sub(r'<[^>]+>', '', content)
    
    # 5. 标准化段落间距
    content = re.sub(r'\n{3,}', '\n\n', content)
    
    return content.strip()

def extract_metadata(content):
    """提取文章元数据"""
    lines = content.split('\n')
    
    metadata = {
        'title': '',
        'author': '',
        'publish_time': '',
        'word_count': 0
    }
    
    # 提取标题（通常是第一个一级标题）
    for line in lines:
        if line.startswith('# '):
            metadata['title'] = line[2:].strip()
            break
    
    # 计算字数
    text_content = re.sub(r'[#*`\[\]()!]', '', content)
    metadata['word_count'] = len(re.sub(r'\s', '', text_content))
    
    return metadata
```

#### 2.2 内容结构化
```
标准文章结构：
1. 标题（H1）
2. 作者信息
3. 发布时间
4. 正文内容
   - 段落
   - 图片
   - 列表
   - 引用
5. 结尾信息
```

#### 2.3 质量验证
```python
def validate_content_quality(content, metadata):
    """验证内容质量"""
    issues = []
    
    # 检查标题
    if not metadata['title']:
        issues.append("缺少文章标题")
    
    # 检查字数
    if metadata['word_count'] < 100:
        issues.append("文章内容过短")
    
    # 检查图片链接
    image_links = re.findall(r'!\[.*?\]\((.*?)\)', content)
    for link in image_links:
        if not link.startswith(('http', 'https')):
            issues.append(f"图片链接可能无效: {link}")
    
    # 检查段落结构
    paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
    if len(paragraphs) < 3:
        issues.append("段落数量过少，可能提取不完整")
    
    return issues
```

### 阶段三：智能体输入准备

#### 3.1 提示词模板
```markdown
# 智能体输入模板

## 处理指令
请基于以下微信公众号文章内容，按照我们的创作模板进行重新创作：

## 原文信息
- **标题**: {title}
- **作者**: {author}
- **字数**: {word_count}
- **来源**: 微信公众号

## 创作要求
1. 保持原文核心观点和信息
2. 优化文章结构和逻辑
3. 提升可读性和吸引力
4. 添加适当的情感色彩
5. 确保内容符合目标受众需求

## 原文内容
{content}

## 输出要求
请按照以下格式输出：
- 新标题（吸引人且准确）
- 引言段落（200字左右）
- 主体内容（保持原文信息完整性）
- 结尾总结（呼应主题）
```

#### 3.2 批量处理脚本
```python
import os
import json
from datetime import datetime

class ContentProcessor:
    def __init__(self, input_folder, output_folder):
        self.input_folder = input_folder
        self.output_folder = output_folder
        self.processed_log = []
    
    def process_batch(self):
        """批量处理文件夹中的Markdown文件"""
        for filename in os.listdir(self.input_folder):
            if filename.endswith('.md'):
                self.process_single_file(filename)
    
    def process_single_file(self, filename):
        """处理单个文件"""
        input_path = os.path.join(self.input_folder, filename)
        
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 清理内容
        cleaned_content = clean_markdown_content(content)
        
        # 提取元数据
        metadata = extract_metadata(cleaned_content)
        
        # 验证质量
        issues = validate_content_quality(cleaned_content, metadata)
        
        # 生成智能体输入
        ai_input = self.generate_ai_input(cleaned_content, metadata)
        
        # 保存结果
        output_filename = f"processed_{filename}"
        output_path = os.path.join(self.output_folder, output_filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(ai_input)
        
        # 记录处理日志
        log_entry = {
            'filename': filename,
            'processed_time': datetime.now().isoformat(),
            'metadata': metadata,
            'issues': issues,
            'output_file': output_filename
        }
        self.processed_log.append(log_entry)
    
    def generate_ai_input(self, content, metadata):
        """生成智能体输入格式"""
        template = """# 智能体输入模板

## 处理指令
请基于以下微信公众号文章内容，按照我们的创作模板进行重新创作：

## 原文信息
- **标题**: {title}
- **字数**: {word_count}
- **处理时间**: {process_time}

## 创作要求
1. 保持原文核心观点和信息
2. 优化文章结构和逻辑
3. 提升可读性和吸引力
4. 添加适当的情感色彩
5. 确保内容符合目标受众需求

## 原文内容
{content}

## 输出要求
请按照以下格式输出：
- 新标题（吸引人且准确）
- 引言段落（200字左右）
- 主体内容（保持原文信息完整性）
- 结尾总结（呼应主题）
"""
        
        return template.format(
            title=metadata['title'],
            word_count=metadata['word_count'],
            process_time=datetime.now().strftime('%Y-%m-%d %H:%M'),
            content=content
        )
    
    def save_log(self):
        """保存处理日志"""
        log_path = os.path.join(self.output_folder, 'processing_log.json')
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(self.processed_log, f, ensure_ascii=False, indent=2)

# 使用示例
processor = ContentProcessor('input_articles', 'processed_articles')
processor.process_batch()
processor.save_log()
```

## 📋 操作检查清单

### 每日处理清单
```
□ 检查简悦插件状态
□ 清理浏览器缓存
□ 准备文章URL列表
□ 创建当日处理文件夹
□ 执行批量提取
□ 运行预处理脚本
□ 质量检查抽样
□ 准备智能体输入
□ 记录处理日志
□ 备份原始文件
```

### 质量控制标准
```
优秀（95分+）：
- 内容完整无缺失
- 格式规范统一
- 图片链接全部有效
- 无需手动调整

良好（85-94分）：
- 内容基本完整
- 格式大部分规范
- 少量图片链接问题
- 需要轻微调整

需要改进（<85分）：
- 内容有明显缺失
- 格式问题较多
- 多个图片链接失效
- 需要重新处理
```

## 🔧 工具与脚本

### 必备工具清单
```
1. 简悦浏览器插件
2. Python 3.8+
3. 文本编辑器（VS Code推荐）
4. Markdown预览工具
5. 批处理脚本
6. 质量检查工具
```

### 自动化脚本
- **内容清理脚本**：`clean_content.py`
- **批量处理脚本**：`batch_processor.py`
- **质量检查脚本**：`quality_checker.py`
- **格式转换脚本**：`format_converter.py`

## 📊 效果监控

### 关键指标
- **处理成功率**：目标95%+
- **内容完整性**：目标90%+
- **格式一致性**：目标95%+
- **处理效率**：目标<5分钟/篇

### 改进建议
1. **定期更新提取规则**
2. **优化清理算法**
3. **扩展质量检查项目**
4. **建立反馈机制**

## 🔗 相关资源

- [[3-Resources/工具使用/简悦插件配置与使用指南]] - 插件使用
- [[3-Resources/AI工具/智能体/公众号文章读取智能体优化方案]] - 整体方案
- [[Python脚本工具集]] - 待创建
- [[质量控制标准]] - 待创建

## 标签
#内容预处理 #标准流程 #质量控制 #自动化 #Python脚本 #工作流程

---
*创建时间：2025-07-19*
*分类：3-Resources/AI工具/智能体*
*重要程度：⭐⭐⭐⭐⭐*
*状态：立即可用*
