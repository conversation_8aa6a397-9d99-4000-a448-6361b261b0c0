# 新知识体系架构设计

## 设计原则
- **PARA方法**：Projects（项目）、Areas（领域）、Resources（资源）、Archive（归档）
- **层级清晰**：不超过3层深度，便于导航
- **功能导向**：按照使用频率和功能分类
- **可扩展性**：便于后续添加新内容

## 建议的新文件夹结构

```
📁 00-系统管理/
   ├── 📄 README.md (主导航文件)
   ├── 📄 标签索引.md
   ├── 📄 模板库/
   └── 📄 配置文件/

📁 01-收集箱/
   ├── 📄 日常收集.md
   ├── 📄 待整理/
   └── 📄 临时笔记/

📁 02-活跃项目/
   ├── 📁 赚钱项目/
   ├── 📁 学习项目/
   ├── 📁 写作项目/
   └── 📁 个人项目/

📁 03-生活领域/
   ├── 📁 健康管理/
   ├── 📁 日常生活/
   ├── 📁 人际关系/
   └── 📁 财务管理/

📁 04-专业领域/
   ├── 📁 项目管理/
   ├── 📁 职业发展/
   ├── 📁 技能学习/
   └── 📁 行业知识/

📁 05-知识资源/
   ├── 📁 AI工具/
   ├── 📁 方法论/
   ├── 📁 思维模型/
   ├── 📁 工具使用/
   └── 📁 参考资料/

📁 06-个人成长/
   ├── 📁 思维提升/
   ├── 📁 习惯养成/
   ├── 📁 复盘总结/
   └── 📁 目标管理/

📁 99-归档/
   ├── 📁 已完成项目/
   ├── 📁 过期内容/
   └── 📁 备份文件/
```

## 文件迁移映射

### 当前 → 新结构
- `AI/` → `05-知识资源/AI工具/`
- `个人发展/` → `06-个人成长/` + `03-生活领域/`
- `知识体系/` → `05-知识资源/方法论/`
- `知识体系架构/` → `05-知识资源/` (分散到各子目录)
- `项目管理/` → `04-专业领域/项目管理/`
- `赚钱项目/` → `02-活跃项目/赚钱项目/`
- `日常生活/` → `03-生活领域/日常生活/`
- `面试/` → `04-专业领域/职业发展/`

## 优势
1. **数字前缀排序**：确保文件夹按重要性和使用频率排列
2. **功能明确**：每个文件夹都有明确的用途
3. **便于维护**：收集箱和归档机制保持系统整洁
4. **扩展性强**：可以轻松添加新的子分类

## 实施步骤
1. 创建新的文件夹结构
2. 逐步迁移现有文件
3. 创建导航和索引文件
4. 建立维护机制
