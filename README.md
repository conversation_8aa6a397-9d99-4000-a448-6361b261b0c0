# 📚 个人知识管理系统

> 基于PARA方法构建的数字化知识管理系统

## 🗂️ 系统结构

### 📁 [1-Projects](1-Projects) - 项目
> 有明确截止日期和具体结果的任务

- **[赚钱项目](1-Projects/赚钱项目)** - 商业项目和盈利计划
- **[学习项目](1-Projects/学习项目)** - 技能学习和课程项目
- **[写作项目](1-Projects/写作项目)** - 文章、书籍等创作项目
- **[个人项目](1-Projects/个人项目)** - 个人兴趣和改进项目

### 📁 [2-Areas](2-Areas) - 领域
> 需要持续维护的生活和工作领域

- **[健康管理](2-Areas/健康管理)** - 健身、饮食、医疗健康
- **[财务管理](2-Areas/财务管理)** - 预算、投资、理财规划
- **[人际关系](2-Areas/人际关系)** - 社交、网络、关系维护
- **[职业发展](2-Areas/职业发展)** - 技能提升、职场规划
- **[个人成长](2-Areas/个人成长)** - 习惯养成、思维提升

### 📁 [3-Resources](3-Resources) - 资源
> 未来可能有用的参考资料和知识

- **[AI工具](3-Resources/AI工具)** - AI工具使用方法和技巧
- **[方法论](3-Resources/方法论)** - 工作方法和思维框架
- **[思维模型](3-Resources/思维模型)** - 思维工具和认知模型
- **[工具使用](3-Resources/工具使用)** - 软件工具和使用技巧
- **[参考资料](3-Resources/参考资料)** - 行业报告、研究资料
- **[项目管理知识](3-Resources/项目管理知识)** - 项目管理理论和实践

### 📁 [4-Archive](4-Archive) - 归档
> 已完成或不再活跃的内容

- **[已完成项目](4-Archive/已完成项目)** - 完成的项目记录
- **[过期内容](4-Archive/过期内容)** - 过时的信息和文件
- **[备份文件](4-Archive/备份文件)** - 重要文件的备份

## 🏷️ 标签系统

### 项目标签
- `#项目/进行中` - 正在进行的项目
- `#项目/已完成` - 已完成的项目
- `#项目/暂停` - 暂停的项目

### 领域标签
- `#领域/健康` `#领域/财务` `#领域/职业` `#领域/成长`

### 资源标签
- `#资源/工具` `#资源/方法` `#资源/参考`

### 状态标签
- `#重要` `#紧急` `#待办` `#完成`

## 📋 使用指南

### 日常工作流程
1. **收集** - 新信息先记录在相应分类中
2. **处理** - 定期整理和分类内容
3. **回顾** - 每周回顾项目进展
4. **归档** - 完成的内容移至归档

### 文件命名规范
- **项目文件**：`[状态] 项目名称 - 截止日期`
- **领域文件**：`领域名称 - 子分类`
- **资源文件**：`主题 - 具体内容`

### 维护建议
- **每日**：更新项目进展，记录新想法
- **每周**：回顾项目状态，整理收集的信息
- **每月**：清理归档，评估领域状态
- **每季度**：优化系统结构，调整分类

## 🔗 快速链接

- [[PARA方法使用指南]] - 详细的PARA方法说明
- [[1-Projects/闲鱼自动化监控项目]] - AI驱动的闲鱼商品监控解决方案
- [[2-Areas/个人档案]] - 个人信息集中管理系统
- [[3-Resources/AI工具/n8n自动化工具研究项目]] - 低代码自动化平台深度研究
- [[3-Resources/AI工具/AI笔记工具选择与实践]] - AI笔记工具使用心得
- [[3-Resources/AI工具/公众号自动化]] - 公众号内容生产完整解决方案
- [[3-Resources/AI工具/文章读取工具]] - 智能文章内容提取工具
- [[3-Resources/AI工具/Lovable.dev网站生成实践案例]] - AI网站生成实战经验
- [[3-Resources/方法论/场景化营销文案写作方法]] - 场景化营销文案实战指南

## 📊 系统统计

- 活跃项目：待统计
- 维护领域：5个
- 资源分类：6个
- 归档内容：待整理

---

*最后更新：2025-07-17*  
*系统版本：PARA v1.0*
