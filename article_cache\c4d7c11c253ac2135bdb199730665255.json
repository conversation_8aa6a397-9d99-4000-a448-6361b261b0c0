{"title": "上次的“AI排版公众号魔法”，现在我让Gemini-2.5-Pro把它做成了工具！", "author": "嘉哥的AIGC", "publish_time": "2025年06月06日 17:33", "content_html": "<div class=\"rich_media_content js_underline_content autoTypeSetting24psection\" id=\"js_content\" style=\"\"><section class=\"js_darkmode__0\" data-pm-slice=\"0 0 []\" style='font-style: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; word-spacing: 0px; text-size-adjust: inherit; -webkit-text-stroke-width: 0px; text-decoration: none; color: rgb(51, 51, 51); font-size: medium; font-variant-ligatures: normal; orphans: 2; widows: 2; background-color: rgb(255, 255, 255); font-family: -apple-system-font, BlinkMacSystemFont, \"Helvetica Neue\", \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; line-height: 1.7; padding: 10px; visibility: visible; margin-bottom: 0px;'><section nodeleaf=\"\" style=\"text-align: center; visibility: visible;\"></section><p style=\"font-size: 16px; margin: 16px 0px; line-height: 1.7; letter-spacing: 0.5px; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">还记得上次那篇<a class=\"normal_text_link\" data-itemshowtype=\"0\" data-linktype=\"2\" hasload=\"1\" href=\"https://mp.weixin.qq.com/s?__biz=MzkyODk5ODI2MQ==&amp;mid=2247483776&amp;idx=1&amp;sn=ee4e9380a97e5d4a029326b32a9f58be&amp;scene=21#wechat_redirect\" linktype=\"text\" style=\"visibility: visible;\" target=\"_blank\" textvalue=\"告别公众号排版劝退症！豆包/Deepseek免费「代码级」一键排版！\">告别公众号排版劝退症！豆包/Deepseek免费「代码级」一键排版！</a>吗？</span></p><p style=\"font-size: 16px; margin: 16px 0px; line-height: 1.7; letter-spacing: 0.5px; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">文章发布后，我收到了很多朋友的私信和好评，看来大家确实都被公众号排版“虐”得不轻。那个通过一段“超级提示词”让AI变身排版专家的“魔法”，也确实帮助不少人提升了效率。</span></p><p style=\"font-size: 16px; margin: 16px 0px; line-height: 1.7; letter-spacing: 0.5px; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">但是，我也听到了另一种声音（包括我自己内心的吐槽）：</span></p><blockquote class=\"js_darkmode__1\" style=\"background-color: rgb(240, 240, 255); margin: 20px 0px; padding: 15px 20px; border-radius: 8px; border: none; visibility: visible;\"><p style=\"margin: 0px; padding: 0px; font-size: 16px; color: rgb(85, 85, 119); visibility: visible;\"><span style=\"font-weight: bold; color: rgb(98, 0, 238); margin-right: 10px; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">➤</span></span><span leaf=\"\" style=\"visibility: visible;\"> “方法是好，但每次都要打开豆包，找到那段长长的提示词，复制、粘贴……还是有点繁琐，不够‘一键’啊！”</span></p></blockquote><p style=\"font-size: 16px; margin: 16px 0px; line-height: 1.7; letter-spacing: 0.5px; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">没错，真正的效率工具，应该像自来水一样，拧开就有，用完即走。</span></p><p style=\"font-size: 16px; margin: 16px 0px; line-height: 1.7; letter-spacing: 0.5px; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">刚好，这两天Google DeepMind刚刚发布的</span><strong class=\"js_darkmode__2\" style=\"color: rgb(98, 0, 238); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">Gemini-2.5-Pro</span></strong><span leaf=\"\" style=\"visibility: visible;\">（06-05版本）！ 我把上次的“排版魔法”——那套经过验证的、兼容微信的HTML生成逻辑，用新版的</span><strong class=\"js_darkmode__3\" style=\"color: rgb(98, 0, 238); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">Gemini-2.5-Pro</span></strong><span leaf=\"\" style=\"visibility: visible;\">写成了一个简单、直观、开箱即用的</span><strong class=\"js_darkmode__4\" style=\"color: rgb(98, 0, 238); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">Web工具</span></strong><span leaf=\"\" style=\"visibility: visible;\">！</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">今天，向大家介绍这个“排版工具进化形态”：</span></p><p style=\"font-size: 24px;font-weight: bold;text-align: center;color: rgb(26, 115, 232);margin: 30px 0px;\"><span leaf=\"\">微信公众号 AI 排版工具 🚀</span></p><p style=\"font-size: 16px;margin: 16px 0px;text-align: center;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">告别复制粘贴复杂冗长提示词的操作，现在，你只需要一个网页。</span></p><p style=\"margin: 0px;padding: 0px;color: rgb(26, 115, 232);font-size: 14px;font-style: italic;\"><span leaf=\"\">(工具主界面截图，简洁直观)</span></p><section nodeleaf=\"\" style=\"text-align: center;\"></section><h2 class=\"js_darkmode__6\" style=\"margin: 40px 0px 20px;padding-bottom: 10px;border-bottom-width: 1px;border-bottom-style: solid;border-bottom-color: rgb(222, 226, 230);font-size: 20px;font-weight: bold;padding-left: 15px;border-left-width: 4px;border-left-style: solid;border-left-color: rgb(26, 115, 232);color: rgb(51, 51, 51);\"><span class=\"js_darkmode__7\" style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;color: rgb(98, 0, 238);margin-right: 5px;'><span leaf=\"\">✨</span></span><span leaf=\"\">从“魔法咒语”到“一键神器”，它进化了什么？</span></h2><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">如果说上次的方法是给了你一张“藏宝图”，那这次的工具，就是直接把“宝藏”放在了你面前。它不仅继承了之前方法的所有优点，还带来了体验上的飞跃：</span></p><p style=\"margin: 10px 0px 5px;padding: 5px 0px;font-size: 16px;line-height: 1.7;\"><span style=\"display: inline-block;background-color: rgb(26, 115, 232);color: rgb(255, 255, 255);font-weight: bold;border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;padding: 2px 8px;margin-right: 10px;\"><span leaf=\"\">1</span></span><span leaf=\"\"> 🤖 </span><strong><span leaf=\"\">AI 智能核心，始终如一</span></strong></p><p class=\"js_darkmode__8\" style=\"margin: 0px 0px 10px 40px;padding: 0px;font-size: 16px;color: rgb(85, 85, 85);\"><span leaf=\"\">它的“大脑”依然是我们信赖的大语言模型（支持Deepseek、豆包、GPT-4o、Gemini、Claude等），以及那段经过千锤百炼、洞悉微信排版规则的“系统提示词”。你无需关心背后复杂的规则，只需享受稳定的高质量输出。</span></p><p style=\"margin: 10px 0px 5px;padding: 5px 0px;font-size: 16px;line-height: 1.7;\"><span style=\"display: inline-block;background-color: rgb(26, 115, 232);color: rgb(255, 255, 255);font-weight: bold;border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;padding: 2px 8px;margin-right: 10px;\"><span leaf=\"\">2</span></span><span leaf=\"\"> 🎨 </span><strong><span leaf=\"\">告别繁琐，一句话定义你的风格</span></strong></p><p class=\"js_darkmode__9\" style=\"margin: 0px 0px 10px 40px;padding: 0px;font-size: 16px;color: rgb(85, 85, 85);\"><span leaf=\"\">还记得之前要反复调试风格吗？现在，你只需在专属的“排版风格”输入框里，用大白话告诉它你想要什么：</span></p><p class=\"js_darkmode__13\" style=\"margin: 0px 0px 10px 40px;padding: 0px;font-size: 16px;color: rgb(85, 85, 85);\"><span leaf=\"\">剩下的，交给AI。</span></p><p style=\"margin: 10px 0px 5px;padding: 5px 0px;font-size: 16px;line-height: 1.7;\"><span style=\"display: inline-block;background-color: rgb(26, 115, 232);color: rgb(255, 255, 255);font-weight: bold;border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;padding: 2px 8px;margin-right: 10px;\"><span leaf=\"\">3</span></span><span leaf=\"\"> 👁️ </span><strong><span leaf=\"\">所见即所得，无缝粘贴</span></strong></p><p class=\"js_darkmode__14\" style=\"margin: 0px 0px 10px 40px;padding: 0px;font-size: 16px;color: rgb(85, 85, 85);\"><span leaf=\"\">工具提供了清晰的左右分栏布局。左边输入，右边实时预览。排版效果满意后，点击右上角的</span><strong class=\"js_darkmode__15\" style=\"color: rgb(98, 0, 238);\"><span leaf=\"\">“复制预览”</span></strong><span leaf=\"\">按钮，然后直接去公众号后台 Ctrl+V，所有样式都会被完美保留，所见即所得！</span></p><p style=\"margin: 0px;padding: 0px;color: rgb(26, 115, 232);font-size: 14px;font-style: italic;\"><span leaf=\"\">(以下是复制到公众号的效果)</span></p><section nodeleaf=\"\" style=\"text-align: center;\"></section><h2 class=\"js_darkmode__17\" style=\"margin: 40px 0px 20px;padding-bottom: 10px;border-bottom-width: 1px;border-bottom-style: solid;border-bottom-color: rgb(222, 226, 230);font-size: 20px;font-weight: bold;padding-left: 15px;border-left-width: 4px;border-left-style: solid;border-left-color: rgb(26, 115, 232);color: rgb(51, 51, 51);\"><span class=\"js_darkmode__18\" style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;color: rgb(98, 0, 238);margin-right: 5px;'><span leaf=\"\">🎨</span></span><span leaf=\"\">进阶玩法：让AI为你“设计”无限风格</span></h2><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">这款工具最有趣的地方在于它的</span><strong class=\"js_darkmode__19\" style=\"color: rgb(98, 0, 238);\"><span leaf=\"\">高度可定制性</span></strong><span leaf=\"\">。你可以像设计师一样，通过调整“排版风格提示词”，创造出无穷无尽的专属风格。</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">工具内置的默认风格提示词是这样的：</span></p><p class=\"js_darkmode__20\" style=\"margin: 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);font-weight: bold;\"><span leaf=\"\">【内置默认风格提示词示例】</span></p><p class=\"js_darkmode__21\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">我想要一个干净、专业的科技博客风格。</span></span></p><p class=\"js_darkmode__22\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 主色调使用蓝色（例如 </span><a class=\"wx_topic_link\" data-topic=\"1\" href=\"javascript:;\" style=\"color: #576B95 !important;\" topic-id=\"mbkkt2ik-quy072\"><span style=\"font-style: italic;\" textstyle=\"\">#3A5FCD</span></a><span style=\"font-style: italic;\" textstyle=\"\">）。</span></span></p><p class=\"js_darkmode__23\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 标题要有左边框装饰。</span></span></p><p class=\"js_darkmode__24\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 引用区块使用浅灰色背景。</span></span></p><p class=\"js_darkmode__25\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 代码块使用深色背景。</span></span></p><p class=\"js_darkmode__26\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 全文行高舒适，段落间距清晰。</span></span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">你可以直接用它，但更有趣的是，你可以把它复制出来，扔给豆包、DeepSeek、Kimi或任何你顺手的大模型，让它帮你“改装”成你想要的样子！</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">下面举两个例子：</span></p><h3 style=\"font-size: 18px;font-weight: bold;margin: 30px 0px 15px;color: rgb(26, 115, 232);\"><span leaf=\"\">案例一：改装成“温馨读书笔记”风格</span></h3><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">我告诉AI：“请把上面的科技博客风格提示词，修改成一个适合个人读书笔记的、温馨柔和的风格。我喜欢用米色和棕色系，希望标题能可爱一点，比如用一些小符号。”</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">AI立刻给我返回了新的提示词：</span></p><p class=\"js_darkmode__27\" style=\"margin: 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);font-weight: bold;\"><span leaf=\"\">【读书笔记风格提示词 - AI生成】</span></p><p class=\"js_darkmode__28\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">我想要一个温馨、柔和的个人读书笔记风格。</span></span></p><p class=\"js_darkmode__29\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 主色调使用米色（如背景 </span><a class=\"wx_topic_link\" data-topic=\"1\" href=\"javascript:;\" style=\"color: #576B95 !important;\" topic-id=\"mbkktu92-fkpx4h\"><span style=\"font-style: italic;\" textstyle=\"\">#FAF3E0</span></a><span style=\"font-style: italic;\" textstyle=\"\">）和柔和的棕色（如标题 #8D6E63）。</span></span></p><p class=\"js_darkmode__30\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 标题（H2）前面可以加上一个emoji符号，比如 📖，并且使用圆角背景。</span></span></p><p class=\"js_darkmode__31\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 引用区块使用淡淡的鹅黄色背景（</span><a class=\"wx_topic_link\" data-topic=\"1\" href=\"javascript:;\" style=\"color: #576B95 !important;\" topic-id=\"mbkktzej-diiqsv\"><span style=\"font-style: italic;\" textstyle=\"\">#FFF9C4</span></a><span style=\"font-style: italic;\" textstyle=\"\">），并带有虚线左边框。</span></span></p><p class=\"js_darkmode__32\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 全文行高设置为宽松的 1.8，段落间距加大，阅读起来更放松。</span></span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">把这段新提示词粘贴回工具里，瞧，一篇充满呼吸感的读书笔记排版就诞生了！</span></p><p style=\"margin: 0px;padding: 0px;color: rgb(26, 115, 232);font-size: 14px;font-style: italic;\"><span leaf=\"\">(“温馨读书笔记”风格的排版效果图)</span></p><section nodeleaf=\"\" style=\"text-align: center;\"></section><h3 style=\"font-size: 18px;font-weight: bold;margin: 30px 0px 15px;color: rgb(26, 115, 232);\"><span leaf=\"\">案例二：改装成“严肃学术论文”风格</span></h3><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">我又对AI说：“这次，请把它改成非常严肃、正式的学术论文风格。仿照知网或学术期刊的样式，用黑白灰，强调结构清晰。”</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">AI马上理解了我的意图：</span></p><p class=\"js_darkmode__33\" style=\"margin: 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);font-weight: bold;\"><span leaf=\"\">【学术论文风格提示词 - AI生成】</span></p><p class=\"js_darkmode__34\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">我需要一个严谨、规范的学术报告风格。</span></span></p><p class=\"js_darkmode__35\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 严格使用黑、白、灰三色。正文为黑色（</span><a class=\"wx_topic_link\" data-topic=\"1\" href=\"javascript:;\" style=\"color: #576B95 !important;\" topic-id=\"mbkkvtfl-errvp9\"><span style=\"font-style: italic;\" textstyle=\"\">#333333</span></a><span style=\"font-style: italic;\" textstyle=\"\">），背景为白色。</span></span></p><p class=\"js_darkmode__36\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- H1主标题居中加粗，字号22px。</span></span></p><p class=\"js_darkmode__37\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- H2、H3标题使用 1. 和 1.1 这样的数字编号，左对齐，加粗，与正文间距明确。</span></span></p><p class=\"js_darkmode__38\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 引用部分使用稍大的左缩进，并用斜体字以示区别。</span></span></p><p class=\"js_darkmode__39\" style=\"margin: 10px 0px 0px;padding: 0px;font-size: 15px;color: rgb(68, 68, 68);line-height: 1.7;\"><span leaf=\"\"><span style=\"font-style: italic;\" textstyle=\"\">- 全文使用 line-height: 1.7，字体采用系统默认的宋体或思源宋体优先。</span></span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">这个风格下，文章立刻变得专业、规整，充满了学术气息。</span></p><p style=\"margin: 0px;padding: 0px;color: rgb(26, 115, 232);font-size: 14px;font-style: italic;\"><span leaf=\"\">(“严肃学术论文”风格的排版效果图)</span></p><section nodeleaf=\"\" style=\"text-align: center;\"></section><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">看到了吗？通过这个简单的“AI x AI”联动，你可以轻松成为自己的排版设计师。</span></p><h2 class=\"js_darkmode__41\" style=\"margin: 40px 0px 20px;padding-bottom: 10px;border-bottom-width: 1px;border-bottom-style: solid;border-bottom-color: rgb(222, 226, 230);font-size: 20px;font-weight: bold;padding-left: 15px;border-left-width: 4px;border-left-style: solid;border-left-color: rgb(26, 115, 232);color: rgb(51, 51, 51);\"><span class=\"js_darkmode__42\" style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;color: rgb(98, 0, 238);margin-right: 5px;'><span leaf=\"\">👨‍💻</span></span><span leaf=\"\">对于爱折腾的你：如何拥有属于自己的排版工具？</span></h2><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">我知道，我的读者里一定藏着不少技术爱好者。好消息是：这个工具我已经开源了！</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">这意味着，你不仅可以使用它，还可以拥有它、改造它，甚至把它部署到你自己的服务器上，变成一个完全属于你的、7x24小时待命的私人排版助理。</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">在本地运行它也非常简单（电脑需要有Node.js环境）：</span></p><p style=\"font-size: 17px;margin: 25px 0px 10px;font-weight: bold;\"><span class=\"js_darkmode__43\" style=\"color: rgb(98, 0, 238);\"><span leaf=\"\">第一步：</span></span><span leaf=\"\">下载源码</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">从 GitHub 克隆或下载项目文件。</span></p><pre class=\"js_darkmode__44\" style=\"background-color: rgb(18, 18, 18);color: rgb(224, 224, 224);padding: 20px;border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;overflow-x: auto;line-height: 1.6;margin: 15px 0px;\"><code style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;font-size: 14px;'><span style=\"color: rgb(136, 136, 136);\"><span leaf=\"\">⌘ </span></span><span style=\"color: rgb(0, 255, 255);\"><span leaf=\"\">git</span></span><span leaf=\"\"> clone https://github.com/zhgarylu/wechat-formatter</span><span leaf=\"\">.git</span></code></pre><p style=\"font-size: 17px;margin: 25px 0px 10px;font-weight: bold;\"><span class=\"js_darkmode__45\" style=\"color: rgb(98, 0, 238);\"><span leaf=\"\">第二步：</span></span><span leaf=\"\">安装依赖</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">进入项目目录，运行命令安装所需组件。</span></p><pre class=\"js_darkmode__46\" style=\"background-color: rgb(18, 18, 18);color: rgb(224, 224, 224);padding: 20px;border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;overflow-x: auto;line-height: 1.6;margin: 15px 0px;\"><code style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;font-size: 14px;'><span style=\"color: rgb(136, 136, 136);\"><span leaf=\"\">⌘ </span></span><span style=\"color: rgb(0, 255, 255);\"><span leaf=\"\">npm</span></span><span leaf=\"\"> install express openai dotenv cors</span></code></pre><p style=\"font-size: 17px;margin: 25px 0px 10px;font-weight: bold;\"><span class=\"js_darkmode__47\" style=\"color: rgb(98, 0, 238);\"><span leaf=\"\">第三步：</span></span><span leaf=\"\">配置你的AI“大脑”</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">这是最关键的一步，分为两小步：</span></p><p style=\"font-size: 16px;margin: 16px 0px 10px 20px;font-weight: bold;\"><span leaf=\"\">1. 填入你的API密钥 (API Key)</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">在项目根目录创建一个名为 </span><code class=\"js_darkmode__48\" style=\"background-color: rgb(224, 224, 224);padding: 2px 5px;border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;font-size: 14px;\"><span leaf=\"\">.env</span></code><span leaf=\"\"> 的文件，然后在里面填入你的API密钥，格式如下：</span></p><pre class=\"js_darkmode__49\" style=\"background-color: rgb(18, 18, 18);color: rgb(224, 224, 224);padding: 20px;border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;overflow-x: auto;line-height: 1.6;margin: 15px 0px;\"><code style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;font-size: 14px;'><span style=\"color: rgb(0, 255, 255);\"><span leaf=\"\">OPENAI_API_KEY</span></span><span leaf=\"\">=\"sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxx\"</span></code></pre><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">把 </span><code class=\"js_darkmode__50\" style=\"background-color: rgb(224, 224, 224);padding: 2px 5px;border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;font-size: 14px;\"><span leaf=\"\">sk-xxxxxxxx...</span></code><span leaf=\"\"> 替换成你自己的密钥。</span></p><p style=\"font-size: 16px;margin: 16px 0px 10px 20px;font-weight: bold;\"><span leaf=\"\">2. 更换大模型服务商 (API Base URL) - [核心]</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">工具默认连接了一个第三方代理服务。如果你想</span><strong class=\"js_darkmode__51\" style=\"color: rgb(98, 0, 238);\"><span leaf=\"\">换成官方API或其他大模型</span></strong><span leaf=\"\">，请按以下操作：</span></p><pre class=\"js_darkmode__53\" style=\"background-color: rgb(18, 18, 18);color: rgb(224, 224, 224);padding: 20px;border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;overflow-x: auto;line-height: 1.6;margin: 15px 0px;\"><code style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;font-size: 14px;'><span style=\"color: rgb(136, 136, 136);\"><span leaf=\"\">// ⌘ server.js </span></span></code></pre><pre class=\"js_darkmode__54\" style=\"background-color: rgb(18, 18, 18);color: rgb(224, 224, 224);padding: 20px;border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;overflow-x: auto;line-height: 1.6;margin: 15px 0px;\"><code style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;font-size: 14px;'><span style=\"color: rgb(0, 255, 255);\"><span leaf=\"\">const</span></span><span leaf=\"\"> openai = </span><span style=\"color: rgb(0, 255, 255);\"><span leaf=\"\">new</span></span><span leaf=\"\"> OpenAI({    apiKey: apiKey,    baseURL: </span><span style=\"color: rgb(153, 255, 153);\"><span leaf=\"\">\"https://api.gptgod.online/v1\"</span></span><span leaf=\"\">, </span><span style=\"color: rgb(136, 136, 136);\"><span leaf=\"\">// &lt;= 就是这里！修改或删除它</span></span><span leaf=\"\">});</span></code></pre><p style=\"font-size: 17px;margin: 25px 0px 10px;font-weight: bold;\"><span class=\"js_darkmode__58\" style=\"color: rgb(98, 0, 238);\"><span leaf=\"\">第四步：</span></span><span leaf=\"\">启动！</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">保存好所有修改，然后在终端运行：</span></p><pre class=\"js_darkmode__59\" style=\"background-color: rgb(18, 18, 18);color: rgb(224, 224, 224);padding: 20px;border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;overflow-x: auto;line-height: 1.6;margin: 15px 0px;\"><code style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;font-size: 14px;'><span style=\"color: rgb(136, 136, 136);\"><span leaf=\"\">⌘ </span></span><span style=\"color: rgb(0, 255, 255);\"><span leaf=\"\">node</span></span><span leaf=\"\"> server.js</span></code></pre><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">看到提示 </span><code class=\"js_darkmode__60\" style=\"background-color: rgb(224, 224, 224);padding: 2px 5px;border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;font-size: 14px;\"><span leaf=\"\">服务器正在运行于 http://localhost:3000</span></code><span leaf=\"\"> 后，打开浏览器访问这个地址，你的私人排版工具就正式上线了！</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">完整的项目地址和更详细的部署说明，请访问我们的 GitHub 仓库：</span></p><p style=\"text-align: center;margin: 30px 0px;\"><span leaf=\"\"> 点击这里，直达 GitHub 开源仓库：</span></p><p style=\"text-align: center;margin: 30px 0px;\"><span data-pm-slice='1 1 [\"para\",{\"tagName\":\"section\",\"attributes\":{\"style\":\"font-style: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;text-align: start;text-indent: 0px;text-transform: none;white-space: normal;word-spacing: 0px;-webkit-text-size-adjust: auto;-webkit-text-stroke-width: 0px;text-decoration: none;color: rgb(51, 51, 51);font-size: medium;font-variant-ligatures: normal;orphans: 2;widows: 2;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;font-family: -apple-system-font, BlinkMacSystemFont, \\\"Helvetica Neue\\\", \\\"PingFang SC\\\", \\\"Hiragino Sans GB\\\", \\\"Microsoft YaHei UI\\\", \\\"Microsoft YaHei\\\", Arial, sans-serif;line-height: 1.7;padding: 10px;\",\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"p\",\"attributes\":{\"style\":\"text-align: center;margin: 30px 0px;\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\">👉</span><span data-pm-slice='1 1 [\"para\",{\"tagName\":\"section\",\"attributes\":{\"style\":\"font-style: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;text-align: start;text-indent: 0px;text-transform: none;white-space: normal;word-spacing: 0px;-webkit-text-size-adjust: auto;-webkit-text-stroke-width: 0px;text-decoration: none;color: rgb(51, 51, 51);font-size: medium;font-variant-ligatures: normal;orphans: 2;widows: 2;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;font-family: -apple-system-font, BlinkMacSystemFont, \\\"Helvetica Neue\\\", \\\"PingFang SC\\\", \\\"Hiragino Sans GB\\\", \\\"Microsoft YaHei UI\\\", \\\"Microsoft YaHei\\\", Arial, sans-serif;line-height: 1.7;padding: 10px;\",\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"p\",\"attributes\":{\"style\":\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\"><span style=\"font-size: 15px;color: rgb(136, 136, 136);\" textstyle=\"\">https://github.com/zhgarylu/wechat-formatter</span></span><span leaf=\"\"> 👈</span></p><h2 class=\"js_darkmode__62\" style=\"margin: 40px 0px 20px;padding-bottom: 10px;border-bottom-width: 1px;border-bottom-style: solid;border-bottom-color: rgb(222, 226, 230);font-size: 20px;font-weight: bold;padding-left: 15px;border-left-width: 4px;border-left-style: solid;border-left-color: rgb(26, 115, 232);color: rgb(51, 51, 51);\"><span class=\"js_darkmode__63\" style='font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, Courier, monospace;color: rgb(98, 0, 238);margin-right: 5px;'><span leaf=\"\">💡</span></span><span leaf=\"\">总结：让技术真正服务于创作</span></h2><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">从一个探索性的“方法”，到一个成熟易用的“工具”，这次的升级，是我对<span style=\"font-weight: bold;\" textstyle=\"\">“一切无法落地给予实用的AI应用都是耍流氓”</span>这句话的再次践行。</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">我始终相信，技术的价值在于解决真实世界的问题，把人们从重复、低效的劳动中解放出来。</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">希望这款小工具，能成为你内容创作路上的得力助手，让你从此告别排版焦虑，把每一分热情都倾注在打磨好故事、分享好知识上。</span></p><p style=\"font-size: 16px;margin: 16px 0px;line-height: 1.7;letter-spacing: 0.5px;\"><span leaf=\"\">快去 </span><strong class=\"js_darkmode__64\" style=\"color: rgb(98, 0, 238);\"><span leaf=\"\">GitHub</span></strong><span leaf=\"\"> 获取源码，部署你自己的排版神器吧！也欢迎你提交代码、分享想法，我们一起让它变得更好！</span></p></section></div>", "content_text": "还记得上次那篇告别公众号排版劝退症！豆包/Deepseek免费「代码级」一键排版！吗？文章发布后，我收到了很多朋友的私信和好评，看来大家确实都被公众号排版“虐”得不轻。那个通过一段“超级提示词”让AI变身排版专家的“魔法”，也确实帮助不少人提升了效率。但是，我也听到了另一种声音（包括我自己内心的吐槽）：➤ “方法是好，但每次都要打开豆包，找到那段长长的提示词，复制、粘贴……还是有点繁琐，不够‘一键’啊！”没错，真正的效率工具，应该像自来水一样，拧开就有，用完即走。刚好，这两天Google DeepMind刚刚发布的Gemini-2.5-Pro（06-05版本）！ 我把上次的“排版魔法”——那套经过验证的、兼容微信的HTML生成逻辑，用新版的Gemini-2.5-Pro写成了一个简单、直观、开箱即用的Web工具！今天，向大家介绍这个“排版工具进化形态”：微信公众号 AI 排版工具 🚀告别复制粘贴复杂冗长提示词的操作，现在，你只需要一个网页。(工具主界面截图，简洁直观)✨从“魔法咒语”到“一键神器”，它进化了什么？如果说上次的方法是给了你一张“藏宝图”，那这次的工具，就是直接把“宝藏”放在了你面前。它不仅继承了之前方法的所有优点，还带来了体验上的飞跃：1 🤖 AI 智能核心，始终如一它的“大脑”依然是我们信赖的大语言模型（支持Deepseek、豆包、GPT-4o、Gemini、Claude等），以及那段经过千锤百炼、洞悉微信排版规则的“系统提示词”。你无需关心背后复杂的规则，只需享受稳定的高质量输出。2 🎨 告别繁琐，一句话定义你的风格还记得之前要反复调试风格吗？现在，你只需在专属的“排版风格”输入框里，用大白话告诉它你想要什么：剩下的，交给AI。3 👁️ 所见即所得，无缝粘贴工具提供了清晰的左右分栏布局。左边输入，右边实时预览。排版效果满意后，点击右上角的“复制预览”按钮，然后直接去公众号后台 Ctrl+V，所有样式都会被完美保留，所见即所得！(以下是复制到公众号的效果)🎨进阶玩法：让AI为你“设计”无限风格这款工具最有趣的地方在于它的高度可定制性。你可以像设计师一样，通过调整“排版风格提示词”，创造出无穷无尽的专属风格。工具内置的默认风格提示词是这样的：【内置默认风格提示词示例】我想要一个干净、专业的科技博客风格。- 主色调使用蓝色（例如 #3A5FCD）。- 标题要有左边框装饰。- 引用区块使用浅灰色背景。- 代码块使用深色背景。- 全文行高舒适，段落间距清晰。你可以直接用它，但更有趣的是，你可以把它复制出来，扔给豆包、DeepSeek、Kimi或任何你顺手的大模型，让它帮你“改装”成你想要的样子！下面举两个例子：案例一：改装成“温馨读书笔记”风格我告诉AI：“请把上面的科技博客风格提示词，修改成一个适合个人读书笔记的、温馨柔和的风格。我喜欢用米色和棕色系，希望标题能可爱一点，比如用一些小符号。”AI立刻给我返回了新的提示词：【读书笔记风格提示词 - AI生成】我想要一个温馨、柔和的个人读书笔记风格。- 主色调使用米色（如背景 #FAF3E0）和柔和的棕色（如标题 #8D6E63）。- 标题（H2）前面可以加上一个emoji符号，比如 📖，并且使用圆角背景。- 引用区块使用淡淡的鹅黄色背景（#FFF9C4），并带有虚线左边框。- 全文行高设置为宽松的 1.8，段落间距加大，阅读起来更放松。把这段新提示词粘贴回工具里，瞧，一篇充满呼吸感的读书笔记排版就诞生了！(“温馨读书笔记”风格的排版效果图)案例二：改装成“严肃学术论文”风格我又对AI说：“这次，请把它改成非常严肃、正式的学术论文风格。仿照知网或学术期刊的样式，用黑白灰，强调结构清晰。”AI马上理解了我的意图：【学术论文风格提示词 - AI生成】我需要一个严谨、规范的学术报告风格。- 严格使用黑、白、灰三色。正文为黑色（#333333），背景为白色。- H1主标题居中加粗，字号22px。- H2、H3标题使用 1. 和 1.1 这样的数字编号，左对齐，加粗，与正文间距明确。- 引用部分使用稍大的左缩进，并用斜体字以示区别。- 全文使用 line-height: 1.7，字体采用系统默认的宋体或思源宋体优先。这个风格下，文章立刻变得专业、规整，充满了学术气息。(“严肃学术论文”风格的排版效果图)看到了吗？通过这个简单的“AI x AI”联动，你可以轻松成为自己的排版设计师。👨‍💻对于爱折腾的你：如何拥有属于自己的排版工具？我知道，我的读者里一定藏着不少技术爱好者。好消息是：这个工具我已经开源了！这意味着，你不仅可以使用它，还可以拥有它、改造它，甚至把它部署到你自己的服务器上，变成一个完全属于你的、7x24小时待命的私人排版助理。在本地运行它也非常简单（电脑需要有Node.js环境）：第一步：下载源码从 GitHub 克隆或下载项目文件。⌘ git clone https://github.com/zhgarylu/wechat-formatter.git第二步：安装依赖进入项目目录，运行命令安装所需组件。⌘ npm install express openai dotenv cors第三步：配置你的AI“大脑”这是最关键的一步，分为两小步：1. 填入你的API密钥 (API Key)在项目根目录创建一个名为 .env 的文件，然后在里面填入你的API密钥，格式如下：OPENAI_API_KEY=\"sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxx\"把 sk-xxxxxxxx... 替换成你自己的密钥。2. 更换大模型服务商 (API Base URL) - [核心]工具默认连接了一个第三方代理服务。如果你想换成官方API或其他大模型，请按以下操作：// ⌘ server.js const openai = new OpenAI({    apiKey: apiKey,    baseURL: \"https://api.gptgod.online/v1\", // <= 就是这里！修改或删除它});第四步：启动！保存好所有修改，然后在终端运行：⌘ node server.js看到提示 服务器正在运行于 http://localhost:3000 后，打开浏览器访问这个地址，你的私人排版工具就正式上线了！完整的项目地址和更详细的部署说明，请访问我们的 GitHub 仓库： 点击这里，直达 GitHub 开源仓库：👉https://github.com/zhgarylu/wechat-formatter 👈💡总结：让技术真正服务于创作从一个探索性的“方法”，到一个成熟易用的“工具”，这次的升级，是我对“一切无法落地给予实用的AI应用都是耍流氓”这句话的再次践行。我始终相信，技术的价值在于解决真实世界的问题，把人们从重复、低效的劳动中解放出来。希望这款小工具，能成为你内容创作路上的得力助手，让你从此告别排版焦虑，把每一分热情都倾注在打磨好故事、分享好知识上。快去 GitHub 获取源码，部署你自己的排版神器吧！也欢迎你提交代码、分享想法，我们一起让它变得更好！", "images": [], "word_count": 2860, "url": "https://mp.weixin.qq.com/s/lHlnzsqroWXGDNaV3-PuKg", "site_type": "微信公众号", "extracted_at": "2025-07-20T11:57:15.954071"}