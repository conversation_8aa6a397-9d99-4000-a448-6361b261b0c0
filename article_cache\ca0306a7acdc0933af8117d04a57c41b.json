{"title": "扣子（Coze）工作流实战：这个工作流可以让你的公众号一键生成全文+封面+排版，自动存草稿箱！", "author": "焦哥AI智能体", "publish_time": "2025年05月06日 17:52", "content_html": "<div class=\"rich_media_content js_underline_content autoTypeSetting24psection\" id=\"js_content\" style=\"\"><p class=\"js_darkmode__0\" data-pm-slice=\"0 0 []\" style='font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 15px; line-height: 2em; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(31, 35, 41); margin: 0px 0px 4px; word-break: break-all; min-height: 20px; visibility: visible;'><span class=\"js_darkmode__1\" style=\"color: rgb(31, 35, 41); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">大家好，我是焦哥。零代码基础手搓工作流，专注于AI智能体工作流的搭建知识分享。</span></span></p><p class=\"js_darkmode__2\" style='font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 15px; line-height: 2em; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(31, 35, 41); margin: 0px 0px 4px; word-break: break-all; min-height: 20px; visibility: visible;'><span class=\"js_darkmode__3\" style=\"color: rgb(31, 35, 41); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">还在为公众号日复一日的选题、撰写、排版而烦恼吗？</span></span></p><p class=\"js_darkmode__4\" style='font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 15px; line-height: 2em; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(31, 35, 41); margin: 0px 0px 4px; word-break: break-all; min-height: 20px; visibility: visible;'><span class=\"js_darkmode__5\" style=\"color: rgb(31, 35, 41); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">想象一下，如果能有一个智能助手，根据你的一个想法或一篇参考文章，就能自动生成一篇图文并茂、排版精美的公众号推文，并直接存入你的草稿箱——这样的创作体验是不是令人向往？</span></span></p><p class=\"js_darkmode__6\" style='font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 15px; line-height: 2em; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(31, 35, 41); margin: 0px 0px 4px; word-break: break-all; min-height: 20px; visibility: visible;'><span class=\"js_darkmode__7\" style=\"color: rgb(31, 35, 41); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">今天，焦哥就来分享如何零代码“手搓”这样一个神奇的AI智能体工作流，让你彻底告别繁琐，高效创作！</span></span></p><h3 class=\"js_darkmode__10\" style='font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-weight: bold; color: rgb(31, 35, 41); font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.578px; margin-top: 0px; margin-bottom: 8px; font-size: 20px; padding-bottom: 12px; visibility: visible;'><span class=\"js_darkmode__11\" style=\"color: rgb(31, 35, 41); font-weight: bold; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">先看下效果</span></span></h3><section class=\"js_darkmode__12\" nodeleaf=\"\" style='color: rgb(0, 0, 0); font-family: \"Noto Sans SC\"; font-size: medium; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; text-align: center; margin-bottom: 12px; visibility: visible;'></section><p class=\"js_darkmode__13\" style='font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 15px; line-height: 2em; font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; color: rgb(31, 35, 41); margin: 0px 0px 4px; word-break: break-all; min-height: 20px; visibility: visible;'><span class=\"js_darkmode__14\" style=\"color: rgb(31, 35, 41); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">以上公众号草稿箱文章都是一键生成的，下面跟着焦哥，咱们一起把这个工作流搭建起来！</span></span></p><h3 class=\"js_darkmode__17\" style='font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-weight: bold; color: rgb(31, 35, 41); font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.578px; margin-top: 0px; margin-bottom: 8px; font-size: 20px; padding-bottom: 12px; visibility: visible;'><span class=\"js_darkmode__18\" style=\"color: rgb(31, 35, 41); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">工作流总览</span></span></h3><section class=\"js_darkmode__19\" nodeleaf=\"\" style='color: rgb(0, 0, 0); font-family: \"Noto Sans SC\"; font-size: medium; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; text-align: center; margin-bottom: 12px; visibility: visible;'></section><h3 class=\"js_darkmode__20\" style='font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-weight: bold; color: rgb(31, 35, 41); font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.578px; margin-top: 0px; margin-bottom: 8px; font-size: 20px; padding-bottom: 12px; visibility: visible;'><span class=\"js_darkmode__21\" style=\"color: rgb(31, 35, 41); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">工作流节点</span></span></h3><h4 class=\"js_darkmode__22\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__23\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">1.开始节点</span></span></h4><section class=\"js_darkmode__24\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__25\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__26\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">2.选择器节点</span></span></h4><p class=\"js_darkmode__27\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__28\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">选择器的作用就是判断用户输入的是一个文章链接，还是一个文章撰写要求，从而执行不同的流程。</span></span></p><section class=\"js_darkmode__29\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__30\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__31\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">文章链接分支</span></span></h4><h4 class=\"js_darkmode__32\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__33\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">3.插件节点</span></span></h4><p class=\"js_darkmode__34\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__35\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">这个插件可以获取文章的标题、内容、插图链接</span></span></p><section class=\"js_darkmode__36\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><section class=\"js_darkmode__37\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__38\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__39\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">4.大模型节点（文章二创）</span></span></h4><p class=\"js_darkmode__40\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__41\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">分析原文章的内容特点，重新模仿创作出一篇新的文章。</span></span></p><p class=\"js_darkmode__42\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__43\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">输出标题：title；</span></span></p><p class=\"js_darkmode__44\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__45\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">文章内容：content</span></span></p><p class=\"js_darkmode__46\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__47\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">文章摘要：desc</span></span></p><section class=\"js_darkmode__48\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><p class=\"js_darkmode__49\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__50\" style=\"color: rgb(31, 35, 41);font-weight: bold;\"><span leaf=\"\"><span class=\"js_darkmode__51\" style=\"color: rgb(255, 0, 0);\" textstyle=\"\">文章中所有的系统提示词及用户提示词，请阅读到文章结尾方法获取。</span></span></span></p><h4 class=\"js_darkmode__53\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__54\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">文章撰写要求分支</span></span></h4><h4 class=\"js_darkmode__55\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__56\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">5.大模型节点（文章撰写）</span></span></h4><p class=\"js_darkmode__57\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__58\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">依据输入的要求，用DeepSeek-R1模型，帮我们撰写一篇公众号文章</span></span></p><p class=\"js_darkmode__59\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__60\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">输出标题：title</span></span></p><p class=\"js_darkmode__61\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__62\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">文章内容：content</span></span></p><p class=\"js_darkmode__63\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__64\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">文章摘要：desc</span></span></p><section class=\"js_darkmode__65\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__67\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__68\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">6.变量聚合</span></span></h4><p class=\"js_darkmode__69\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__70\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">变量聚合可以汇总多个分支输出的内容</span></span></p><p class=\"js_darkmode__71\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__72\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">输出Group1：文章内容</span></span></p><p class=\"js_darkmode__73\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__74\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">Group2：文章标题</span></span></p><p class=\"js_darkmode__75\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__76\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">Group3：文章摘要</span></span></p><section class=\"js_darkmode__77\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><section class=\"js_darkmode__78\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__80\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__81\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">7.大模型节点（封面图提示词）</span></span></h4><p class=\"js_darkmode__82\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__83\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">结合文章的内容，生成一个封面图的提示词</span></span></p><section class=\"js_darkmode__84\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__86\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__87\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">8.图像生成节点（生成封面图）</span></span></h4><section class=\"js_darkmode__88\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__90\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__91\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">9.大模型节点（文章排版）</span></span></h4><p class=\"js_darkmode__92\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__93\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">对文章内容进行排版，生成html代码</span></span></p><p class=\"js_darkmode__94\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__95\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">焦哥这里总结了三种排版风格：草原绿、凝夜紫、橙蓝风</span></span></p><p class=\"js_darkmode__96\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__97\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">可以让大模型依据文章不同的主题和类型，选择合适的风格</span></span></p><section class=\"js_darkmode__98\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><section class=\"js_darkmode__99\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h3 class=\"js_darkmode__101\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 20px;padding-bottom: 12px;'><span class=\"js_darkmode__102\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">公众号插件</span></span></h3><h4 class=\"js_darkmode__103\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__104\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">10.get_access_token（获取token）</span></span></h4><section class=\"js_darkmode__105\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><p class=\"js_darkmode__106\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__107\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">填入自己公众号的appid、appsecret</span></span></p><p class=\"js_darkmode__108\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__109\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">获取方法：</span></span></p><section class=\"js_darkmode__110\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><p class=\"js_darkmode__111\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__112\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">这里还要开一下白名单，输入106.15.251.85</span></span></p><section class=\"js_darkmode__113\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__114\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__115\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">11.upload_img（上传封面图）</span></span></h4><section class=\"js_darkmode__116\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__117\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__118\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">12.add_draft（保存草稿）</span></span></h4><section class=\"js_darkmode__119\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h4 class=\"js_darkmode__121\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 18px;'><span class=\"js_darkmode__122\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">13.结束节点</span></span></h4><p class=\"js_darkmode__123\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__124\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">这里可以不用返回变量</span></span></p><p class=\"js_darkmode__125\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__126\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">或者随便输入一句话：已成功保存到公众号草稿箱，请注意查收！</span></span></p><section class=\"js_darkmode__127\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><h3 class=\"js_darkmode__129\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-weight: bold;color: rgb(31, 35, 41);font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.578px;margin-top: 0px;margin-bottom: 8px;font-size: 20px;padding-bottom: 12px;'><span class=\"js_darkmode__130\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">扣子空间</span></span></h3><p class=\"js_darkmode__131\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__132\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">如果我们输入文章链接，二创新的爆款文章，可以在工作流中直接运行即可</span></span></p><p class=\"js_darkmode__133\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__134\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">如果想加上搜索到的新闻及教程，再创作文章，扣子空间就可以派上用场了。</span></span></p><p class=\"js_darkmode__135\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__136\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">（如果想在扣子空间中调用这个工作流，需要新建应用才能添加，具体看这篇文章<a data-itemshowtype=\"0\" data-linktype=\"2\" hasload=\"1\" href=\"https://mp.weixin.qq.com/s?__biz=MzkzMzk5MzE3NQ==&amp;mid=2247483938&amp;idx=1&amp;sn=9f161c89f1c103d24e4039cbcfb1aa7c&amp;scene=21#wechat_redirect\" linktype=\"text\" style=\"\" target=\"_blank\" textvalue=\"扣子空间实战：在扣子空间手搓一个自定义MCP扩展，竟比想象中简单10倍！\">扣子空间实战：在扣子空间手搓一个自定义MCP扩展，竟比想象中简单10倍！</a>）</span></span></p><p class=\"js_darkmode__137\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__138\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">登录扣子空间，提问并添加扩展</span></span></p><section class=\"js_darkmode__139\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><section class=\"js_darkmode__141\" style='color: rgb(0, 0, 0);font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;display: flex;place-items: flex-start;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;background-color: rgb(255, 245, 235);margin-bottom: 20px;padding: 16px 16px 10px;border: 1px solid rgb(254, 212, 164);border-radius: 8px;text-align: left;'><span style=\"display: flex;align-items: center;justify-content: center;\"><span style=\"font-size: 15px;margin-right: 8px;\"><span leaf=\"\">👍</span></span></span><section style=\"\"><p style='font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">搜索通义千问大模型Qwen3的最热新闻和教程文章，调用插件 gzh_liucheng 帮我重新写一篇Qwen3科普文章，字数不少于1000字，并保存到我的公众号草稿箱。</span></span></p></section></section><section class=\"js_darkmode__142\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><section class=\"js_darkmode__143\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><section class=\"js_darkmode__144\" style='color: rgb(0, 0, 0);font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;display: flex;place-items: flex-start;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;background-color: rgb(255, 245, 235);margin-bottom: 20px;padding: 16px 16px 10px;border: 1px solid rgb(254, 212, 164);border-radius: 8px;text-align: left;'><span style=\"display: flex;align-items: center;justify-content: center;\"><span style=\"font-size: 15px;margin-right: 8px;\"><span leaf=\"\">👍</span></span></span><section style=\"\"><p style='font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">https://mp.weixin.qq.com/s/xExmhFO-cYG7lfQZYx0ssg</span></span><span style=\"color: rgb(31, 35, 41);\"><span leaf=\"\"> ，调用插件 gzh_liucheng 首先读取这篇文章内容，并重新创作出一篇新的爆款文章，最后保存到我的公众号草稿箱。</span></span></p></section></section><section class=\"js_darkmode__145\" nodeleaf=\"\" style='color: rgb(0, 0, 0);font-family: \"Noto Sans SC\";font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;text-align: center;margin-bottom: 12px;'></section><p class=\"js_darkmode__147\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__148\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">今天焦哥分享的这个零代码工作流，无疑是你提升公众号内容创作效率的“神兵利器”</span></span></p><p class=\"js_darkmode__149\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__150\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">它能帮你自动化处理大量繁琐环节，让你有更多精力聚焦于创意本身</span></span></p><p class=\"js_darkmode__151\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__152\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">当然，输出的内容或许还需要你结合自身风格进行微调润色，才能更臻完美</span></span></p><p class=\"js_darkmode__153\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__154\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">这只是一个基础框架！希望你能在这个基础上，大胆发挥创意，尝试调整节点、优化提示词，甚至融合更多工具，打造出真正独一无二、更符合你需求的个性化智能工作流。</span></span></p><p class=\"js_darkmode__155\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__156\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">欢迎在评论区留言分享，我们一起探索AI自动化创作的无限可能！</span></span></p><p class=\"js_darkmode__158\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__159\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">都已经看到这里了，如果觉得对你有帮助，请随手点个赞呗！</span></span></p><p class=\"js_darkmode__160\" style='font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 15px;line-height: 2em;font-family: \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;color: rgb(31, 35, 41);margin: 0px 0px 4px;word-break: break-all;min-height: 20px;'><span class=\"js_darkmode__161\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">获取完整提示词，麻烦您一键三连，评论区留言“</span></span><span class=\"js_darkmode__162\" style=\"color: rgb(31, 35, 41);font-weight: bold;\"><span leaf=\"\"><span class=\"js_darkmode__163\" style=\"color: rgb(255, 0, 0);font-weight: bold;\" textstyle=\"\">一键提示词</span></span></span><span class=\"js_darkmode__164\" style=\"color: rgb(31, 35, 41);\"><span leaf=\"\">”，加焦哥VX，邀请您加入交流群，免费领取飞书AI智能体知识库资料。</span></span></p><section data-class=\"_mbEditor\" data-id=\"2246153\" style=\"margin-bottom: 0px;\"><section style=\"margin:10px auto;\"><section nodeleaf=\"\" style=\"box-sizing:border-box;width:100%;\"></section><section class=\"js_darkmode__bg__0\" style=\"padding:1em;background-image:-webkit-linear-gradient(right, #e6eafe,#f7faff);color:#3859fe;\"><section style=\"display:flex;justify-content: space-between;align-items: center;\"><section style=\"box-sizing:border-box;width:auto;\"><section style=\"text-align: center;\"><section class=\"js_darkmode__bg__1\" style=\"display:inline-block;font-size:14px;letter-spacing:1.5px;background-image:-webkit-linear-gradient(right, #758bf9,#3a5bfe);color:#fff;padding:4px 5px;\"><span leaf=\"\">第一时间给你最新资讯</span></section></section><section style=\"text-align: center;font-size:14px;letter-spacing:1.5px;color:#233070;margin-top:8px;\"><span leaf=\"\">扫码关注我们吧！</span></section></section><section nodeleaf=\"\" style=\"box-sizing:border-box;width:90px;\"></section></section></section><section nodeleaf=\"\" style=\"box-sizing:border-box;width:100%;\"></section></section></section></div>", "content_text": "大家好，我是焦哥。零代码基础手搓工作流，专注于AI智能体工作流的搭建知识分享。还在为公众号日复一日的选题、撰写、排版而烦恼吗？想象一下，如果能有一个智能助手，根据你的一个想法或一篇参考文章，就能自动生成一篇图文并茂、排版精美的公众号推文，并直接存入你的草稿箱——这样的创作体验是不是令人向往？今天，焦哥就来分享如何零代码“手搓”这样一个神奇的AI智能体工作流，让你彻底告别繁琐，高效创作！先看下效果以上公众号草稿箱文章都是一键生成的，下面跟着焦哥，咱们一起把这个工作流搭建起来！工作流总览工作流节点1.开始节点2.选择器节点选择器的作用就是判断用户输入的是一个文章链接，还是一个文章撰写要求，从而执行不同的流程。文章链接分支3.插件节点这个插件可以获取文章的标题、内容、插图链接4.大模型节点（文章二创）分析原文章的内容特点，重新模仿创作出一篇新的文章。输出标题：title；文章内容：content文章摘要：desc文章中所有的系统提示词及用户提示词，请阅读到文章结尾方法获取。文章撰写要求分支5.大模型节点（文章撰写）依据输入的要求，用DeepSeek-R1模型，帮我们撰写一篇公众号文章输出标题：title文章内容：content文章摘要：desc6.变量聚合变量聚合可以汇总多个分支输出的内容输出Group1：文章内容Group2：文章标题Group3：文章摘要7.大模型节点（封面图提示词）结合文章的内容，生成一个封面图的提示词8.图像生成节点（生成封面图）9.大模型节点（文章排版）对文章内容进行排版，生成html代码焦哥这里总结了三种排版风格：草原绿、凝夜紫、橙蓝风可以让大模型依据文章不同的主题和类型，选择合适的风格公众号插件10.get_access_token（获取token）填入自己公众号的appid、appsecret获取方法：这里还要开一下白名单，输入106.15.251.8511.upload_img（上传封面图）12.add_draft（保存草稿）13.结束节点这里可以不用返回变量或者随便输入一句话：已成功保存到公众号草稿箱，请注意查收！扣子空间如果我们输入文章链接，二创新的爆款文章，可以在工作流中直接运行即可如果想加上搜索到的新闻及教程，再创作文章，扣子空间就可以派上用场了。（如果想在扣子空间中调用这个工作流，需要新建应用才能添加，具体看这篇文章扣子空间实战：在扣子空间手搓一个自定义MCP扩展，竟比想象中简单10倍！）登录扣子空间，提问并添加扩展👍搜索通义千问大模型Qwen3的最热新闻和教程文章，调用插件 gzh_liucheng 帮我重新写一篇Qwen3科普文章，字数不少于1000字，并保存到我的公众号草稿箱。👍https://mp.weixin.qq.com/s/xExmhFO-cYG7lfQZYx0ssg ，调用插件 gzh_liucheng 首先读取这篇文章内容，并重新创作出一篇新的爆款文章，最后保存到我的公众号草稿箱。今天焦哥分享的这个零代码工作流，无疑是你提升公众号内容创作效率的“神兵利器”它能帮你自动化处理大量繁琐环节，让你有更多精力聚焦于创意本身当然，输出的内容或许还需要你结合自身风格进行微调润色，才能更臻完美这只是一个基础框架！希望你能在这个基础上，大胆发挥创意，尝试调整节点、优化提示词，甚至融合更多工具，打造出真正独一无二、更符合你需求的个性化智能工作流。欢迎在评论区留言分享，我们一起探索AI自动化创作的无限可能！都已经看到这里了，如果觉得对你有帮助，请随手点个赞呗！获取完整提示词，麻烦您一键三连，评论区留言“一键提示词”，加焦哥VX，邀请您加入交流群，免费领取飞书AI智能体知识库资料。第一时间给你最新资讯扫码关注我们吧！", "images": [], "word_count": 1548, "url": "https://mp.weixin.qq.com/s/z1jHOJHWRmRp3atfwEofFw", "site_type": "微信公众号", "extracted_at": "2025-07-20T11:46:31.519778"}