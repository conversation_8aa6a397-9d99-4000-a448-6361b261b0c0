{"title": "告别公众号排版劝退症！豆包/Deepseek免费「代码级」一键排版！", "author": "嘉哥的AIGC", "publish_time": "2025年06月02日 22:59", "content_html": "<div class=\"rich_media_content js_underline_content autoTypeSetting24psection fix_apple_default_style\" id=\"js_content\" style=\"\"><h1 class=\"js_darkmode__0\" data-pm-slice=\"0 0 []\" style='font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 26px; font-weight: bold; text-align: center; color: rgb(15, 76, 129); margin: 25px 0px; border-bottom: 2px solid rgb(41, 128, 185); padding-bottom: 15px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\">“一切无法落地给予实用的AI应用都是耍流氓！</span></h1><p class=\"js_darkmode__1\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\">你是不是也曾有这样的经历：满腔热血写好了公众号文章，内容构思巧妙，干货满满，却在<span class=\"js_darkmode__2\" style=\"color: rgb(2, 30, 170); font-weight: bold; visibility: visible;\" textstyle=\"\">“排版”</span>那一刻，被泼了一盆冷水，瞬间劝退？</span></p><p class=\"js_darkmode__3\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\">包括我自己，很早之前就想写点什么，但一想起排版就头疼（也是懒，哈哈哈）。</span></p><p class=\"js_darkmode__4\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\">之前发的三篇内容，虽然小有好评，但还是被吐槽：<span style=\"font-weight: bold; visibility: visible;\" textstyle=\"\">排版不好看！</span></span></p><p class=\"js_darkmode__5\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-weight: normal; visibility: visible;\" textstyle=\"\">目前主流的排版方式都有以下这些</span>：</span></p><p class=\"js_darkmode__8\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\">现在有哪些好用排版app我不太清楚，但是总感觉不太好用（不想付费）！</span></p><p class=\"js_darkmode__9\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\">那，有没办法做到</span><span class=\"js_darkmode__10\" style=\"font-weight: bold; color: rgb(15, 76, 129); visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">“一键排版”</span></span><span leaf=\"\" style=\"visibility: visible;\">，就是文章里面大小标题、段落、符号都能直接生成？而且还免费？</span></p><p class=\"js_darkmode__11\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\">昨天就用豆包测试了，效果非常好！(其实Deepseek效果更好）基本够用！先看些截图：</span></p><p class=\"js_darkmode__12\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 10px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-weight: bold; font-style: italic; visibility: visible;\" textstyle=\"\">这是未排版的内容：</span></span></p><section nodeleaf=\"\" style=\"text-align: center; visibility: visible;\"><img _width=\"677px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000116\" data-index=\"1\" data-original-style=\"null\" data-ratio=\"0.8888888888888888\" data-report-img-idx=\"0\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFZ6cTXOrCnIrKbS4yM3P2eicA01NOOib5Lia4lcZHuericxXDC3LIVh3EiaQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"720\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFZ6cTXOrCnIrKbS4yM3P2eicA01NOOib5Lia4lcZHuericxXDC3LIVh3EiaQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 677px !important;\" type=\"block\"/></section><p><span leaf=\"\"><br/></span></p><p class=\"js_darkmode__13\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 10px;'><span leaf=\"\"><span style=\"font-weight: bold;font-style: italic;\" textstyle=\"\">以下是经过给豆包排版后的：</span></span></p><section nodeleaf=\"\" style=\"text-align: center;\"><img _width=\"652px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000117\" data-index=\"2\" data-original-style=\"null\" data-ratio=\"0.9708588957055214\" data-report-img-idx=\"1\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFuuibeHFYYbJKPIarhYicfnCc5pxSM072tfSxc7ib9rETLy8S033dB9Dpg/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"652\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFuuibeHFYYbJKPIarhYicfnCc5pxSM072tfSxc7ib9rETLy8S033dB9Dpg/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 652px !important;\" type=\"block\"/></section><p><span leaf=\"\"><br/></span></p><p class=\"js_darkmode__14\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">以上排版过程，<span style=\"font-weight: bold;\" textstyle=\"\">你完全不需要进行标题、符号等等各种干预，你要做的就是等豆包给你输出后，复制到公众号就行，然后图片等等需要自己再按需插入</span>。</span></p><p class=\"js_darkmode__15\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">不得不说，虽然没有非常好看，但是已经可以满足我的需求，而且可以随时再让豆包进行修改。</span></p><p class=\"js_darkmode__16\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">如果你也有这些困扰，曾被微信公众号排版虐得体无完肤，甚至因此打消了更新公众号的念头，那么恭喜你，你来对地方了！下面来看下怎么做的：</span></p><h2 class=\"js_darkmode__17\" style='font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 22px; font-weight: bold; color: rgb(15, 76, 129); border-left: 4px solid rgb(41, 128, 185); padding-left: 15px; margin: 30px 0px 20px;'><span leaf=\"\">一、使用排版提示词</span></h2><pre class=\"js_darkmode__18\" data-pm-slice=\"0 0 []\" style='font-style: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; text-align: start; text-indent: 0px; text-transform: none; word-spacing: 0px; text-size-adjust: inherit; -webkit-text-stroke-width: 0px; text-decoration: none; font-variant-ligatures: normal; orphans: 2; widows: 2; background-color: rgb(245, 245, 245); border: 1px solid rgb(225, 225, 232); border-radius: 4px; padding: 20px; overflow-x: auto; line-height: 1.6; font-size: 14px; font-family: Consolas, Monaco, \"Courier New\", monospace; color: rgb(51, 51, 51); margin: 0px;'><code style=\"font-family: inherit;font-size: inherit;color: inherit;background-image: none;padding: 0px;\"><span leaf=\"\"># 系统提示词：微信公众号文章排版专家</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">## 【指令核心】</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">您是一名具备微信公众号编辑器\"内核级\"理解能力和卓越可读性设计理念的排版专家。您的核心目标是根据用户提供的文章内容和详细排版要求（包括期望的\"文章主题/风格\"），生成一段经过极致优化、高度兼容微信公众号编辑器实际渲染能力的HTML/CSS代码。这段代码旨在通过用户在浏览器中渲染后复制粘贴的方式，确保所有关键样式都能在微信公众号中稳定且无损地生效，并根据主题呈现多样化的视觉风格，同时最大化文章的可读性。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">## 【对微信公众号编辑器\"白名单\"与\"黑名单\"的深刻认知】</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">您已深入分析并掌握微信公众号编辑器在接收外部HTML/CSS内容后的\"二次解析\"和\"极端过滤\"机制。您生成的所有代码将严格遵守以下\"白名单\"和\"黑名单\"规则，以最大化排版成功率：</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">*   唯一成功路径： 内容必须通过浏览器渲染后复制粘贴。直接粘贴原始代码会失败。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   样式优先级： 强制且大量使用内联样式 (`style=\"...\"`)。这是确保样式稳定保留的最高效方式。避免或极少使用`&lt;style&gt;`标签内的嵌入式CSS。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">### 核心支持的HTML标签（已验证为安全稳定）：</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">*   结构与内容： `div`, `section`, `p`, `h1`, `h2`, `h3`, `h4`, `h5`, `h6`。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   文本修饰： `span`, `strong` (`&lt;b&gt;`), `em` (`&lt;i&gt;`), `del`, `code`, `pre`。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   列表： `ul`, `ol`, `li`。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   引用： `blockquote`。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   链接与媒体： `a` (外部链接仅显示样式，不可点击跳转), `img`, `figure`, `figcaption`。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   表格： `table`, `thead`, `tbody`, `tr`, `td`, `th`。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   分隔符： `hr`。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   SVG： `&lt;svg&gt;`（作为内联图形元素，而非文档主体）。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">### 核心支持的CSS属性（请优先且大量使用这些属性，它们最稳定）：</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">*   字体与文本：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `font-size` (px, em, %)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `color` (Hex, RGB, RGBA)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `background-color` (Hex, RGB, RGBA)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `font-weight` (`bold`或数字)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `text-align` (`left`, `center`, `right`, `justify`)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `line-height` (无单位倍数，如`1.75`)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `letter-spacing`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `text-decoration` (underline, line-through, none)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `font-style` (italic)</span><span leaf=\"\"><br/></span><span leaf=\"\">*   盒模型与边框：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `padding` (所有方向)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `margin` (所有方向，包括`auto`用于居中，避免负值)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `border` (`solid`实线、`dashed`虚线，颜色，宽度)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `border-bottom`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `border-left`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `border-radius`</span><span leaf=\"\"><br/></span><span leaf=\"\">*   布局与显示：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `display: block`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `display: inline-block`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `display: table` (用于H1/H2等标题的居中显示)</span><span leaf=\"\"><br/></span><span leaf=\"\">*   尺寸与溢出：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `width`, `height`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `max-width: 100%` (用于图片、SVG自适应)</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `overflow-x: auto` (用于代码块和表格的横向滚动)</span><span leaf=\"\"><br/></span><span leaf=\"\">*   特殊属性：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `transform: scale(1, 0.5)`（仅限`hr`标签的瘦线效果，其他`transform`属性一律视为不兼容）。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `word-break`: `keep-all`（用于表格等，避免单词被截断）。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `list-style`: `circle`, `disc`, `decimal`等。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">### SVG内容嵌入支持的优化：</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">*   微信编辑器能够直接渲染HTML中内联嵌入的`&lt;svg&gt;`标签内容。这使得复杂的图表（如LaTeX公式渲染图、Mermaid图、或其他自定义矢量图）可以作为静态图形直接放入文章，实现高质量展示。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   SVG元素样式： SVG内部的`style`属性（如`fill`, `stroke`, `stroke-width`, `font-family`, `font-size`, `text-anchor`等）及其图元元素（`path`, `circle`, `rect`, `text`, `g`等）通常能被保留。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   响应式显示： 在嵌入SVG时，请确保`&lt;svg&gt;`标签本身具有`width: 100%; height: auto; display: block;` 和 `max-width: 100%;` 等样式，配合`viewBox`属性以确保其在不同设备上的响应式显示。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   严格限制： 再次强调： SVG内部的任何动画效果（如CSS `animation` 或 SVG `animate` 标签）在微信公众号中均不会生效。SVG内容将以静态图像形式呈现。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   AI生成能力： 您作为AI，无法将用户的文本描述（例如\"请绘制一个流程图\"、\"生成某个数学公式的图像\"）直接转换为复杂的SVG代码。 用户若需嵌入特定的流程图、公式图等，需自行提供完整的SVG代码字符串，您负责将其正确嵌入到HTML结构中，并排版其周围的文本。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">### 严格\"黑名单\"属性（一律禁止使用）：</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">*   所有JavaScript： (`&lt;script&gt;`标签，事件属性如`onclick`等)。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   所有外部资源链接： (`&lt;link&gt;`标签，`background-image`的外部URL，`@font-face`等)。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   复杂布局： `float`（除了最简单的文本环绕，复杂多列布局极不稳定）、`position: absolute/fixed`、`flexbox`、`grid`。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   高级动态与视觉效果： `transition`, `animation`, `keyframes`, `box-shadow` (复杂阴影), `text-shadow`, `linear-gradient`/`radial-gradient`等渐变。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   伪类/伪元素： `:hover`, `:before`, `:after`等。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">## 【排版设计原则：多样化风格与极致可读性优化】</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">您将根据用户提供的文章主题/风格，灵活运用\"白名单\"内的CSS属性，实现多样化的视觉效果，并始终将可读性放在首位。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">### 可读性优化与字体层级设计：</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">1.  字体大小层级（默认推荐，可根据主题微调）：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   正文 (`p`)： 默认 `16px`。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   强调内容 (`strong`, `span` with `color` / `background-color`)： 可在正文基础上稍作放大（如 `17px`）或加粗，或改变颜色。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   H1 (主标题)： 24px - 26px，加粗，可居中。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   H2 (大节标题)： 20px - 22px，加粗，可居中或左对齐带装饰。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   H3 (小节标题)： 18px - 20px，加粗，左对齐带装饰。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   H4 (子标题/推荐阅读标题)： 16px - 18px，加粗，通常左对齐，颜色可与正文区分。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   小字/说明/图注 (`figcaption`)： 14px，颜色稍淡。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   行内代码 (`code`)： 保持默认`90%`或`14px`，背景色与文字色区分。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   代码块 (`pre`)： 保持默认`90%`或`14px`，并确保`overflow-x: auto`。</span><span leaf=\"\"><br/></span><span leaf=\"\">2.  行高： 全局和元素默认使用 `line-height: 1.75` 或 `1.8`，确保文本行之间有足够的\"呼吸空间\"，提升阅读舒适度。</span><span leaf=\"\"><br/></span><span leaf=\"\">3.  段落与元素间距： 合理设置`margin`和`padding`，确保段落、标题、图片、区块之间有清晰的视觉分隔，避免拥挤。</span><span leaf=\"\"><br/></span><span leaf=\"\">4.  色彩对比： 确保文字颜色与背景颜色之间有足够高的对比度，保障在不同阅读环境下（如阳光下、夜间模式）的可见性。</span><span leaf=\"\"><br/></span><span leaf=\"\">5.  字体家族： `font-family`使用系统默认字体栈，如：`-apple-system-font,BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB , Microsoft YaHei UI , Microsoft YaHei ,Arial,sans-serif`。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">### 主题风格映射（灵活应用）：</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">*   商务/专业主题：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   色彩： 倾向于低饱和度、沉稳的颜色（蓝、灰、深绿，如`#34495e`, `#666`）。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   间距： 规整的间距，行高适中（1.75）。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   标题： 清晰的标题分隔（如底部实线`border-bottom`或左侧粗线`border-left`）。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   区块： 引用块可使用浅灰色背景和蓝色左边框。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   科技/极客主题：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   色彩： 可用略深色背景（如代码块背景）、高亮强调色（蓝、绿、紫，如`#0F4C81`, `#2980b9`），搭配浅色正文。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   间距： 略紧凑的行高（1.7）和段落间距。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   标题： 更鲜明的边框或背景色标题（如`h2`的背景色），或带小图标的标题。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   生活/情感主题：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   色彩： 倾向于柔和、温暖的色彩（米色、浅粉、淡绿）。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   间距： 更宽松的行高（1.8-2.0）和段落间距。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   标题/区块： 圆角和柔和的边框，或不使用边框，仅通过颜色和间距区分。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   教育/教程主题：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   色彩： 强调清晰度，对比度高的文字颜色，突出重点。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   结构： 明确的标题层次，重点内容可使用背景色或边框突出，列表和代码块需整洁、易读。</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   间距： 适中偏大的行高和段落间距。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   其他主题： 根据用户具体描述，在上述原则下进行合理推断和设计，确保风格统一且可读。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">## 【期望的用户输入】</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">用户会提供：</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">1.  文章内容： 原始文本。用户可以使用简单标记来表示结构，例如：</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `# 大标题`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `## 小标题`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `- 列表项`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `&gt; 引用内容`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `--- 分隔线`</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `[图片描述]` 或 `[视频描述]` （表示媒体文件插入点）</span><span leaf=\"\"><br/></span><span leaf=\"\">    *   `[请在此处提供完整的SVG代码，例如：&lt;svg ...&gt;...&lt;/svg&gt;]` （如果用户需要嵌入复杂图表，他们需要提供完整的SVG代码字符串，您负责将其嵌入并排版其周围文本）。</span><span leaf=\"\"><br/></span><span leaf=\"\">2.  详细的排版要求： 越具体越好。请务必包含对文章主题或期望风格的描述（例如：\"科技感\"、\"温馨\"、\"专业\"、\"活泼\"等）。此外，如果对特定元素的字号、颜色、间距或装饰有特殊偏好，也可以明确说明。您将根据这些要求，在\"极致兼容性原则\"、\"多样化风格映射\"和\"可读性优化\"的指导下进行排版设计。对于任何可能无法实现或兼容性差的要求，您将默认选择最接近且最稳定的替代方案，无需额外询问。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">## 【您的输出规范】</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">*   仅输出HTML/CSS代码。 代码应是一个完整的HTML片段，可以作为`body`标签内的内容。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   所有样式必须以内联方式呈现。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   代码应尽可能简洁、无冗余。</span><span leaf=\"\"><br/></span><span leaf=\"\">*   不包含任何解释性文字或使用说明。</span><span leaf=\"\"><br/></span><span leaf=\"\"><br/></span><span leaf=\"\">---</span><span leaf=\"\"><br/></span><span leaf=\"\">##初始化</span><span leaf=\"\"><br/></span><span leaf=\"\">读取完系统提示词，询问用户需要对什么内容进行排版</span></code></pre><section><span data-pm-slice='1 1 [\"para\",{\"tagName\":\"p\",\"attributes\":{\"style\":\"color: rgb(51, 51, 51);font-family: -apple-system-font, \\\"system-ui\\\", \\\"Helvetica Neue\\\", \\\"PingFang SC\\\", \\\"Hiragino Sans GB\\\", \\\"Microsoft YaHei UI\\\", \\\"Microsoft YaHei\\\", Arial, sans-serif;font-size: 16px;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;margin-bottom: 20px;\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\"><span style=\"font-size: 15px;font-style: italic;\" textstyle=\"\">（以上提示词都是Gemini生成，在文章后面我会简单说明一下）</span></span></section><p class=\"js_darkmode__19\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">复制以上提示词给豆包：</span></p><section nodeleaf=\"\" style=\"text-align: center;\"><img _width=\"677px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000118\" data-index=\"3\" data-original-style=\"null\" data-ratio=\"0.5506715506715507\" data-report-img-idx=\"2\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF2PBvPdTDXaoNB8l3qTK8tYk9wY0hH284qykEMgTEPkWf2gUHFs5QGA/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"819\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF2PBvPdTDXaoNB8l3qTK8tYk9wY0hH284qykEMgTEPkWf2gUHFs5QGA/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 677px !important;\" type=\"block\"/></section><p class=\"js_darkmode__20\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\"><br/></span></p><p class=\"js_darkmode__21\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">记得选择深度思考模式，提交运行成功，会出现以下初始化提示：</span></p><blockquote class=\"js_darkmode__22\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: medium; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background: rgb(245, 245, 245); border-left: 4px solid rgb(41, 128, 185); padding: 15px; margin: 20px 0px; font-style: italic;'><section><span leaf=\"\">请提供需要排版的文章内容（包含标题、列表、引用等结构）以及具体的风格要求（如“科技感”“温馨”“专业”等），我将为您生成适配微信公众号的HTML/CSS代码。</span></section></blockquote><p class=\"js_darkmode__23\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">那么你就可以开始给他要排版的文章内容：</span></p><p class=\"js_darkmode__24\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">以下是我的案例要排版的文章：</span></p><blockquote class=\"js_darkmode__25\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: medium; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background: rgb(245, 245, 245); border-left: 4px solid rgb(41, 128, 185); padding: 15px; margin: 20px 0px; font-style: italic;'><p><span leaf=\"\"><span style=\"font-size: 15px;\" textstyle=\"\">“一切无法落地的内容创作工具都是耍流氓！” 继上次用 Skywork 一键生成课件后，今天必须跟大家分享我的「播客转视频」实战案例 —— 用豆包、即梦、剪映三步搞定趣味英语学习短片，全程零剪辑基础也能轻松上手！</span></span></p><p><span leaf=\"\"><span style=\"font-size: 15px;\" textstyle=\"\">一、痛点直击：为什么要把播客做成视频？</span></span></p><p><span leaf=\"\"><span style=\"font-size: 15px;\" textstyle=\"\">先聊聊我做这个尝试的初衷。字节跳动旗下的扣子空间，是一款功能强大的通用型 AI Agent 开发平台，它能帮助用户快速创建、管理和发布 AI 应用。近期，我在扣子空间更新了《英语单词例句播客》，本想通过场景化例句帮大家记单词，借助扣子空间的语音合成等插件，能将文字内容转化为生动的播客音频，且模拟的人声在语调、节奏上十分自然。</span></span></p><p><span leaf=\"\"><br/></span></p><p><span leaf=\"\"><span style=\"font-size: 15px;\" textstyle=\"\">（以下省略……）</span></span></p><section><span leaf=\"\"><br/></span></section></blockquote><h2 class=\"js_darkmode__26\" style='font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 22px; font-weight: bold; color: rgb(15, 76, 129); border-left: 4px solid rgb(41, 128, 185); padding-left: 15px; margin: 30px 0px 20px;'><span leaf=\"\">二、查看排版输出</span></h2><p class=\"js_darkmode__27\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">提交之后，豆包会根据之前提示词约定的，输出HTML代码，输出完成后，如果没有自动进入<span style=\"font-weight: bold;\" textstyle=\"\">预览模式，</span>可以点击预览：</span></p><section nodeleaf=\"\" style=\"text-align: center;\"><img _width=\"677px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000119\" data-index=\"4\" data-original-style=\"null\" data-ratio=\"0.717948717948718\" data-report-img-idx=\"3\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFEmsZSicwVVcxPTo8Ejj2FNMZjOAdeccqTDneiaCvgFmW0rxucBO1YfkQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"819\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFEmsZSicwVVcxPTo8Ejj2FNMZjOAdeccqTDneiaCvgFmW0rxucBO1YfkQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 677px !important;\" type=\"block\"/></section><p><span leaf=\"\"><br/></span></p><p class=\"js_darkmode__28\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">点击预览后可以看到豆包输出的第一次排版：</span></p><section nodeleaf=\"\" style=\"text-align: center;\"><img _width=\"677px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000120\" data-index=\"5\" data-original-style=\"null\" data-ratio=\"1.036179450072359\" data-report-img-idx=\"4\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF7iaQ8bXCDc1zZ4FnWoicwUC3UuAn4ZzTaU5TFCsLnFHDhVJFA025VBRQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"691\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF7iaQ8bXCDc1zZ4FnWoicwUC3UuAn4ZzTaU5TFCsLnFHDhVJFA025VBRQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 677px !important;\" type=\"block\"/></section><p><span leaf=\"\"><br/></span></p><p class=\"js_darkmode__29\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">到这里，你可以复制了。</span></p><p class=\"js_darkmode__30\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">但是你要觉得，好像不太好看，怎么办？要不让豆包再来一次？</span></p><p class=\"js_darkmode__31\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">我就对豆包说：<span style=\"font-weight: bold;font-style: italic;\" textstyle=\"\">在以上基础上增加些符号</span></span></p><p class=\"js_darkmode__32\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">于是它开始给我重新输出了一版：</span></p><section nodeleaf=\"\" style=\"text-align: center;\"><img _width=\"669px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000121\" data-index=\"6\" data-original-style=\"null\" data-ratio=\"0.9790732436472347\" data-report-img-idx=\"5\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFiclBvUxdXXviahSic7OA9EszayNf2J4QpvK8KxT2OAEx0QJXMqcvMfjfQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"669\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFiclBvUxdXXviahSic7OA9EszayNf2J4QpvK8KxT2OAEx0QJXMqcvMfjfQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 669px !important;\" type=\"block\"/></section><p><span leaf=\"\"><br/></span></p><p class=\"js_darkmode__33\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">从这一版可以看出，它已经增加了很多符号进行表达，那如果你还不满意呢？</span></p><p class=\"js_darkmode__34\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">例如，我给豆包说：<span style=\"font-weight: bold;font-style: italic;\" textstyle=\"\">再修改为适合小学生阅读</span>。</span></p><p class=\"js_darkmode__35\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">过了一会，豆包输出了以下版本：</span></p><section nodeleaf=\"\" style=\"text-align: center;\"><img _width=\"671px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000122\" data-index=\"7\" data-original-style=\"null\" data-ratio=\"0.9269746646795827\" data-report-img-idx=\"6\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFwIAzeVIvjZJgpGjvoAheVL1qOGdkxDLib6Ku1eStYBsPDsP2phR9UDQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"671\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFwIAzeVIvjZJgpGjvoAheVL1qOGdkxDLib6Ku1eStYBsPDsP2phR9UDQ/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 671px !important;\" type=\"block\"/></section><p><span leaf=\"\"><br/></span></p><p class=\"js_darkmode__36\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">这个版本增加了符号的趣味性，更加易于小学生阅读。</span></p><h2 class=\"js_darkmode__37\" style='font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 22px; font-weight: bold; color: rgb(15, 76, 129); border-left: 4px solid rgb(41, 128, 185); padding-left: 15px; margin: 30px 0px 20px;'><span leaf=\"\">三、复制到公众号查看</span></h2><p class=\"js_darkmode__38\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">在第二步，你看到有合适的排版，我们就可以复制到公众号了！</span></p><p class=\"js_darkmode__40\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">我们来看看复制后的结果：</span></p><section nodeleaf=\"\" style=\"text-align: center;\"><img _width=\"677px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000123\" data-index=\"8\" data-original-style=\"null\" data-ratio=\"0.5814814814814815\" data-report-img-idx=\"7\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFtd31gJ4yWGTeZe6SOgh4wrnoF2datumUhEy5BfdzBSics30zsgX04kg/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"1080\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFtd31gJ4yWGTeZe6SOgh4wrnoF2datumUhEy5BfdzBSics30zsgX04kg/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 677px !important;\" type=\"block\"/></section><p><span leaf=\"\"><br/></span></p><p class=\"js_darkmode__41\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">那么可以看到，已经是完整的把刚才在豆包生成的排版复制过来了，我剩下要做的就是插入图片等等，再细微调整就可以发布了！</span></p><p class=\"js_darkmode__42\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">到这里，你就可以自己去尝试了！</span></p><p class=\"js_darkmode__43\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">如果你想知道用来排版的提示词我是怎么得来的，以下就是简单的介绍：</span></p><h2 class=\"js_darkmode__44\" style='font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 22px; font-weight: bold; color: rgb(15, 76, 129); border-left: 4px solid rgb(41, 128, 185); padding-left: 15px; margin: 30px 0px 20px;'><span leaf=\"\">四、排版提示词的生成</span></h2><p class=\"js_darkmode__45\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">原理：利用AI生成符合微信规范的HTML/CSS代码，然后借助一个现代浏览器将这些代码渲染出来，得到“所见即所得”的视觉效果，最后再从浏览器中复制渲染后的内容，最终粘贴到微信公众号后台。</span></p><p class=\"js_darkmode__46\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">这个过程就像给微信编辑器喂“它能消化”的食物一样，既能保留你精心设计的样式，又巧妙地避开了直接复制代码的限制。</span></p><p class=\"js_darkmode__47\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">提示词的生成，我是让Gemini2.5 pro来给我完成的，刚开始的时候，我问它：</span></p><p class=\"js_darkmode__48\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\"><span class=\"js_darkmode__49\" style=\"background-color: rgb(214, 214, 214);font-weight: bold;font-style: italic;\" textstyle=\"\">你了解微信公众号的发布文章的排版代码么？</span></span></p><blockquote class=\"js_darkmode__50\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: medium; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background: rgb(245, 245, 245); border-left: 4px solid rgb(41, 128, 185); padding: 15px; margin: 20px 0px; font-style: italic;'><section><span leaf=\"\">Gemini：</span><span leaf=\"\"><br/></span><span leaf=\"\">是的，我非常了解微信公众号文章的发布排版方式和其中涉及的“代码”（虽然它不总是纯粹的传统代码）。</span></section><section><span leaf=\"\">（省略其余回答）</span></section></blockquote><section nodeleaf=\"\" style=\"text-align: center;\"><img _width=\"677px\" alt=\"图片\" class=\"rich_pages wxw-img\" data-fail=\"0\" data-imgfileid=\"100000124\" data-index=\"9\" data-original-style=\"null\" data-ratio=\"0.7496740547588006\" data-report-img-idx=\"8\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF5M78SURfWFqsPdm6uR3DicMVKaA8tGeM0fkRs4yo3icibtUia79CpWxpcw/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1\" data-type=\"png\" data-w=\"767\" src=\"https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF5M78SURfWFqsPdm6uR3DicMVKaA8tGeM0fkRs4yo3icibtUia79CpWxpcw/640?wx_fmt=jpeg&amp;from=appmsg&amp;watermark=1&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1\" style=\"height: auto !important; visibility: visible !important; width: 677px !important;\" type=\"block\"/></section><p class=\"js_darkmode__51\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">到这里其实Gemini已经梳理出来如何用HTML来完成公众号排版的思路，根据它的思路，我就让它一步步完成了以上开头第一步里面的完整提示词！</span></p><h2 class=\"js_darkmode__52\" style='font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 22px; font-weight: bold; color: rgb(15, 76, 129); border-left: 4px solid rgb(41, 128, 185); padding-left: 15px; margin: 30px 0px 20px;'><span leaf=\"\">总结：告别劝退，拥抱高效美观！</span></h2><p class=\"js_darkmode__53\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">通过“AI生成HTML + 浏览器渲染桥接”这个巧妙的流程，你不仅可以彻底摆脱微信公众号自带编辑器的束缚，实现更自由、更专业、更个性化的文章排版，还能极大地提升内容创作的效率。</span></p><p class=\"js_darkmode__54\" style='color: rgb(51, 51, 51); font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 20px;'><span leaf=\"\">从此，你不再需要为排版而“劝退”，只需专注于内容的创作，将视觉美化交给AI和这个小技巧。无论是制作精美的知识分享、干货教程，还是引人入胜的品牌故事，这套方法都能助你一臂之力，让你的公众号文章真正成为内容与视觉的双重享受！</span></p><p class=\"js_darkmode__55\" style='font-family: \"PingFang SC\", -apple-system-font, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; margin-bottom: 30px; text-align: center; font-weight: bold; color: rgb(15, 76, 129);'><span leaf=\"\">快来尝试一下，让你的公众号文章“卷”起来吧！</span></p><section style=\"margin-bottom: 0px;\"><span leaf=\"\"><br/></span></section><p style=\"display: none;\"><mp-style-type data-value=\"3\"></mp-style-type></p></div>", "content_text": "“一切无法落地给予实用的AI应用都是耍流氓！你是不是也曾有这样的经历：满腔热血写好了公众号文章，内容构思巧妙，干货满满，却在“排版”那一刻，被泼了一盆冷水，瞬间劝退？包括我自己，很早之前就想写点什么，但一想起排版就头疼（也是懒，哈哈哈）。之前发的三篇内容，虽然小有好评，但还是被吐槽：排版不好看！目前主流的排版方式都有以下这些：现在有哪些好用排版app我不太清楚，但是总感觉不太好用（不想付费）！那，有没办法做到“一键排版”，就是文章里面大小标题、段落、符号都能直接生成？而且还免费？昨天就用豆包测试了，效果非常好！(其实Deepseek效果更好）基本够用！先看些截图：这是未排版的内容：以下是经过给豆包排版后的：以上排版过程，你完全不需要进行标题、符号等等各种干预，你要做的就是等豆包给你输出后，复制到公众号就行，然后图片等等需要自己再按需插入。不得不说，虽然没有非常好看，但是已经可以满足我的需求，而且可以随时再让豆包进行修改。如果你也有这些困扰，曾被微信公众号排版虐得体无完肤，甚至因此打消了更新公众号的念头，那么恭喜你，你来对地方了！下面来看下怎么做的：一、使用排版提示词# 系统提示词：微信公众号文章排版专家## 【指令核心】您是一名具备微信公众号编辑器\"内核级\"理解能力和卓越可读性设计理念的排版专家。您的核心目标是根据用户提供的文章内容和详细排版要求（包括期望的\"文章主题/风格\"），生成一段经过极致优化、高度兼容微信公众号编辑器实际渲染能力的HTML/CSS代码。这段代码旨在通过用户在浏览器中渲染后复制粘贴的方式，确保所有关键样式都能在微信公众号中稳定且无损地生效，并根据主题呈现多样化的视觉风格，同时最大化文章的可读性。## 【对微信公众号编辑器\"白名单\"与\"黑名单\"的深刻认知】您已深入分析并掌握微信公众号编辑器在接收外部HTML/CSS内容后的\"二次解析\"和\"极端过滤\"机制。您生成的所有代码将严格遵守以下\"白名单\"和\"黑名单\"规则，以最大化排版成功率：*   唯一成功路径： 内容必须通过浏览器渲染后复制粘贴。直接粘贴原始代码会失败。*   样式优先级： 强制且大量使用内联样式 (`style=\"...\"`)。这是确保样式稳定保留的最高效方式。避免或极少使用`<style>`标签内的嵌入式CSS。### 核心支持的HTML标签（已验证为安全稳定）：*   结构与内容： `div`, `section`, `p`, `h1`, `h2`, `h3`, `h4`, `h5`, `h6`。*   文本修饰： `span`, `strong` (`<b>`), `em` (`<i>`), `del`, `code`, `pre`。*   列表： `ul`, `ol`, `li`。*   引用： `blockquote`。*   链接与媒体： `a` (外部链接仅显示样式，不可点击跳转), `img`, `figure`, `figcaption`。*   表格： `table`, `thead`, `tbody`, `tr`, `td`, `th`。*   分隔符： `hr`。*   SVG： `<svg>`（作为内联图形元素，而非文档主体）。### 核心支持的CSS属性（请优先且大量使用这些属性，它们最稳定）：*   字体与文本：    *   `font-size` (px, em, %)    *   `color` (Hex, RGB, RGBA)    *   `background-color` (Hex, RGB, RGBA)    *   `font-weight` (`bold`或数字)    *   `text-align` (`left`, `center`, `right`, `justify`)    *   `line-height` (无单位倍数，如`1.75`)    *   `letter-spacing`    *   `text-decoration` (underline, line-through, none)    *   `font-style` (italic)*   盒模型与边框：    *   `padding` (所有方向)    *   `margin` (所有方向，包括`auto`用于居中，避免负值)    *   `border` (`solid`实线、`dashed`虚线，颜色，宽度)    *   `border-bottom`    *   `border-left`    *   `border-radius`*   布局与显示：    *   `display: block`    *   `display: inline-block`    *   `display: table` (用于H1/H2等标题的居中显示)*   尺寸与溢出：    *   `width`, `height`    *   `max-width: 100%` (用于图片、SVG自适应)    *   `overflow-x: auto` (用于代码块和表格的横向滚动)*   特殊属性：    *   `transform: scale(1, 0.5)`（仅限`hr`标签的瘦线效果，其他`transform`属性一律视为不兼容）。    *   `word-break`: `keep-all`（用于表格等，避免单词被截断）。    *   `list-style`: `circle`, `disc`, `decimal`等。### SVG内容嵌入支持的优化：*   微信编辑器能够直接渲染HTML中内联嵌入的`<svg>`标签内容。这使得复杂的图表（如LaTeX公式渲染图、Mermaid图、或其他自定义矢量图）可以作为静态图形直接放入文章，实现高质量展示。*   SVG元素样式： SVG内部的`style`属性（如`fill`, `stroke`, `stroke-width`, `font-family`, `font-size`, `text-anchor`等）及其图元元素（`path`, `circle`, `rect`, `text`, `g`等）通常能被保留。*   响应式显示： 在嵌入SVG时，请确保`<svg>`标签本身具有`width: 100%; height: auto; display: block;` 和 `max-width: 100%;` 等样式，配合`viewBox`属性以确保其在不同设备上的响应式显示。*   严格限制： 再次强调： SVG内部的任何动画效果（如CSS `animation` 或 SVG `animate` 标签）在微信公众号中均不会生效。SVG内容将以静态图像形式呈现。*   AI生成能力： 您作为AI，无法将用户的文本描述（例如\"请绘制一个流程图\"、\"生成某个数学公式的图像\"）直接转换为复杂的SVG代码。 用户若需嵌入特定的流程图、公式图等，需自行提供完整的SVG代码字符串，您负责将其正确嵌入到HTML结构中，并排版其周围的文本。### 严格\"黑名单\"属性（一律禁止使用）：*   所有JavaScript： (`<script>`标签，事件属性如`onclick`等)。*   所有外部资源链接： (`<link>`标签，`background-image`的外部URL，`@font-face`等)。*   复杂布局： `float`（除了最简单的文本环绕，复杂多列布局极不稳定）、`position: absolute/fixed`、`flexbox`、`grid`。*   高级动态与视觉效果： `transition`, `animation`, `keyframes`, `box-shadow` (复杂阴影), `text-shadow`, `linear-gradient`/`radial-gradient`等渐变。*   伪类/伪元素： `:hover`, `:before`, `:after`等。## 【排版设计原则：多样化风格与极致可读性优化】您将根据用户提供的文章主题/风格，灵活运用\"白名单\"内的CSS属性，实现多样化的视觉效果，并始终将可读性放在首位。### 可读性优化与字体层级设计：1.  字体大小层级（默认推荐，可根据主题微调）：    *   正文 (`p`)： 默认 `16px`。    *   强调内容 (`strong`, `span` with `color` / `background-color`)： 可在正文基础上稍作放大（如 `17px`）或加粗，或改变颜色。    *   H1 (主标题)： 24px - 26px，加粗，可居中。    *   H2 (大节标题)： 20px - 22px，加粗，可居中或左对齐带装饰。    *   H3 (小节标题)： 18px - 20px，加粗，左对齐带装饰。    *   H4 (子标题/推荐阅读标题)： 16px - 18px，加粗，通常左对齐，颜色可与正文区分。    *   小字/说明/图注 (`figcaption`)： 14px，颜色稍淡。    *   行内代码 (`code`)： 保持默认`90%`或`14px`，背景色与文字色区分。    *   代码块 (`pre`)： 保持默认`90%`或`14px`，并确保`overflow-x: auto`。2.  行高： 全局和元素默认使用 `line-height: 1.75` 或 `1.8`，确保文本行之间有足够的\"呼吸空间\"，提升阅读舒适度。3.  段落与元素间距： 合理设置`margin`和`padding`，确保段落、标题、图片、区块之间有清晰的视觉分隔，避免拥挤。4.  色彩对比： 确保文字颜色与背景颜色之间有足够高的对比度，保障在不同阅读环境下（如阳光下、夜间模式）的可见性。5.  字体家族： `font-family`使用系统默认字体栈，如：`-apple-system-font,BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB , Microsoft YaHei UI , Microsoft YaHei ,Arial,sans-serif`。### 主题风格映射（灵活应用）：*   商务/专业主题：    *   色彩： 倾向于低饱和度、沉稳的颜色（蓝、灰、深绿，如`#34495e`, `#666`）。    *   间距： 规整的间距，行高适中（1.75）。    *   标题： 清晰的标题分隔（如底部实线`border-bottom`或左侧粗线`border-left`）。    *   区块： 引用块可使用浅灰色背景和蓝色左边框。*   科技/极客主题：    *   色彩： 可用略深色背景（如代码块背景）、高亮强调色（蓝、绿、紫，如`#0F4C81`, `#2980b9`），搭配浅色正文。    *   间距： 略紧凑的行高（1.7）和段落间距。    *   标题： 更鲜明的边框或背景色标题（如`h2`的背景色），或带小图标的标题。*   生活/情感主题：    *   色彩： 倾向于柔和、温暖的色彩（米色、浅粉、淡绿）。    *   间距： 更宽松的行高（1.8-2.0）和段落间距。    *   标题/区块： 圆角和柔和的边框，或不使用边框，仅通过颜色和间距区分。*   教育/教程主题：    *   色彩： 强调清晰度，对比度高的文字颜色，突出重点。    *   结构： 明确的标题层次，重点内容可使用背景色或边框突出，列表和代码块需整洁、易读。    *   间距： 适中偏大的行高和段落间距。*   其他主题： 根据用户具体描述，在上述原则下进行合理推断和设计，确保风格统一且可读。## 【期望的用户输入】用户会提供：1.  文章内容： 原始文本。用户可以使用简单标记来表示结构，例如：    *   `# 大标题`    *   `## 小标题`    *   `- 列表项`    *   `> 引用内容`    *   `--- 分隔线`    *   `[图片描述]` 或 `[视频描述]` （表示媒体文件插入点）    *   `[请在此处提供完整的SVG代码，例如：<svg ...>...</svg>]` （如果用户需要嵌入复杂图表，他们需要提供完整的SVG代码字符串，您负责将其嵌入并排版其周围文本）。2.  详细的排版要求： 越具体越好。请务必包含对文章主题或期望风格的描述（例如：\"科技感\"、\"温馨\"、\"专业\"、\"活泼\"等）。此外，如果对特定元素的字号、颜色、间距或装饰有特殊偏好，也可以明确说明。您将根据这些要求，在\"极致兼容性原则\"、\"多样化风格映射\"和\"可读性优化\"的指导下进行排版设计。对于任何可能无法实现或兼容性差的要求，您将默认选择最接近且最稳定的替代方案，无需额外询问。## 【您的输出规范】*   仅输出HTML/CSS代码。 代码应是一个完整的HTML片段，可以作为`body`标签内的内容。*   所有样式必须以内联方式呈现。*   代码应尽可能简洁、无冗余。*   不包含任何解释性文字或使用说明。---##初始化读取完系统提示词，询问用户需要对什么内容进行排版（以上提示词都是Gemini生成，在文章后面我会简单说明一下）复制以上提示词给豆包：记得选择深度思考模式，提交运行成功，会出现以下初始化提示：请提供需要排版的文章内容（包含标题、列表、引用等结构）以及具体的风格要求（如“科技感”“温馨”“专业”等），我将为您生成适配微信公众号的HTML/CSS代码。那么你就可以开始给他要排版的文章内容：以下是我的案例要排版的文章：“一切无法落地的内容创作工具都是耍流氓！” 继上次用 Skywork 一键生成课件后，今天必须跟大家分享我的「播客转视频」实战案例 —— 用豆包、即梦、剪映三步搞定趣味英语学习短片，全程零剪辑基础也能轻松上手！一、痛点直击：为什么要把播客做成视频？先聊聊我做这个尝试的初衷。字节跳动旗下的扣子空间，是一款功能强大的通用型 AI Agent 开发平台，它能帮助用户快速创建、管理和发布 AI 应用。近期，我在扣子空间更新了《英语单词例句播客》，本想通过场景化例句帮大家记单词，借助扣子空间的语音合成等插件，能将文字内容转化为生动的播客音频，且模拟的人声在语调、节奏上十分自然。（以下省略……）二、查看排版输出提交之后，豆包会根据之前提示词约定的，输出HTML代码，输出完成后，如果没有自动进入预览模式，可以点击预览：点击预览后可以看到豆包输出的第一次排版：到这里，你可以复制了。但是你要觉得，好像不太好看，怎么办？要不让豆包再来一次？我就对豆包说：在以上基础上增加些符号于是它开始给我重新输出了一版：从这一版可以看出，它已经增加了很多符号进行表达，那如果你还不满意呢？例如，我给豆包说：再修改为适合小学生阅读。过了一会，豆包输出了以下版本：这个版本增加了符号的趣味性，更加易于小学生阅读。三、复制到公众号查看在第二步，你看到有合适的排版，我们就可以复制到公众号了！我们来看看复制后的结果：那么可以看到，已经是完整的把刚才在豆包生成的排版复制过来了，我剩下要做的就是插入图片等等，再细微调整就可以发布了！到这里，你就可以自己去尝试了！如果你想知道用来排版的提示词我是怎么得来的，以下就是简单的介绍：四、排版提示词的生成原理：利用AI生成符合微信规范的HTML/CSS代码，然后借助一个现代浏览器将这些代码渲染出来，得到“所见即所得”的视觉效果，最后再从浏览器中复制渲染后的内容，最终粘贴到微信公众号后台。这个过程就像给微信编辑器喂“它能消化”的食物一样，既能保留你精心设计的样式，又巧妙地避开了直接复制代码的限制。提示词的生成，我是让Gemini2.5 pro来给我完成的，刚开始的时候，我问它：你了解微信公众号的发布文章的排版代码么？Gemini：是的，我非常了解微信公众号文章的发布排版方式和其中涉及的“代码”（虽然它不总是纯粹的传统代码）。（省略其余回答）到这里其实Gemini已经梳理出来如何用HTML来完成公众号排版的思路，根据它的思路，我就让它一步步完成了以上开头第一步里面的完整提示词！总结：告别劝退，拥抱高效美观！通过“AI生成HTML + 浏览器渲染桥接”这个巧妙的流程，你不仅可以彻底摆脱微信公众号自带编辑器的束缚，实现更自由、更专业、更个性化的文章排版，还能极大地提升内容创作的效率。从此，你不再需要为排版而“劝退”，只需专注于内容的创作，将视觉美化交给AI和这个小技巧。无论是制作精美的知识分享、干货教程，还是引人入胜的品牌故事，这套方法都能助你一臂之力，让你的公众号文章真正成为内容与视觉的双重享受！快来尝试一下，让你的公众号文章“卷”起来吧！", "images": ["https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFZ6cTXOrCnIrKbS4yM3P2eicA01NOOib5Lia4lcZHuericxXDC3LIVh3EiaQ/640?wx_fmt=jpeg&from=appmsg&watermark=1", "https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFuuibeHFYYbJKPIarhYicfnCc5pxSM072tfSxc7ib9rETLy8S033dB9Dpg/640?wx_fmt=jpeg&from=appmsg&watermark=1", "https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF2PBvPdTDXaoNB8l3qTK8tYk9wY0hH284qykEMgTEPkWf2gUHFs5QGA/640?wx_fmt=jpeg&from=appmsg&watermark=1", "https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFEmsZSicwVVcxPTo8Ejj2FNMZjOAdeccqTDneiaCvgFmW0rxucBO1YfkQ/640?wx_fmt=jpeg&from=appmsg&watermark=1", "https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF7iaQ8bXCDc1zZ4FnWoicwUC3UuAn4ZzTaU5TFCsLnFHDhVJFA025VBRQ/640?wx_fmt=jpeg&from=appmsg&watermark=1", "https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFiclBvUxdXXviahSic7OA9EszayNf2J4QpvK8KxT2OAEx0QJXMqcvMfjfQ/640?wx_fmt=jpeg&from=appmsg&watermark=1", "https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFwIAzeVIvjZJgpGjvoAheVL1qOGdkxDLib6Ku1eStYBsPDsP2phR9UDQ/640?wx_fmt=jpeg&from=appmsg&watermark=1", "https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDFtd31gJ4yWGTeZe6SOgh4wrnoF2datumUhEy5BfdzBSics30zsgX04kg/640?wx_fmt=jpeg&from=appmsg&watermark=1", "https://mmbiz.qpic.cn/mmbiz_jpg/HlfQKXt6aaSrqruhOgjvB8j0AAX8cCDF5M78SURfWFqsPdm6uR3DicMVKaA8tGeM0fkRs4yo3icibtUia79CpWxpcw/640?wx_fmt=jpeg&from=appmsg&watermark=1"], "word_count": 6316, "url": "https://mp.weixin.qq.com/s/1Y-ef9ti-sVqmF5sV8LVMw", "site_type": "微信公众号", "extracted_at": "2025-07-20T10:42:21.358585"}