
.theme-dark
{
  --background-primary: #2f3136;
  --background-primary-alt: #36393f;
  --background-secondary: #202225;
  --background-secondary-alt: #0c1018d1;
  --text-normal: #bbc0c5;
  --text-faint: #a09b80;
  --text-title-h1: #a79645;
  --text-title-h2: #939bd6;
  --text-title-h3: #ba6ea0;
  --text-title-h4: #b78b60;
  --text-title-h5: #859679;
  --text-link: #59a2c5;
  --text-a: #59a2c5;
  --text-a-hover: #b3a14b;
  --text-mark: #d79921;
  --pre-code: #272524d9;
  --text-highlight-bg: rgba(255, 255, 0, 0.2);
  --interactive-accent: #92A1A1;
  --interactive-before: #5e6565;
  --background-modifier-border: #92a1a17a;
  --blockquote-border: rgba(77, 60, 166, .6);
  --text-accent: #ff9640ba;
  --interactive-accent-rgb: #fe8019;
  --font-family-editor: Avenir;
  --font-family-preview: Avenir;
}

#graph-view-canvas .links
{
  stroke: var(--interactive-accent-rgb) !important;
}

strong {
  color: #b5c7c7;
  font-weight: 700;
}

em {
  color: #b8cece;
}

mark
{
  background-color: var(--text-mark) !important;
  padding-top: 4px;
  padding-bottom: 1px;
}

/**********************/
/* links and brackets */
/**********************/

/* link */
a,
.internal-link,
.cm-hmd-internal-link,
.cm-link,
.cm-formatting-link
{
  color: var(--text-a) !important;
  text-decoration: none !important;
  font-family: Avenir !important;
}

/* link hover color */
a:hover,
.internal-link:hover
{
  color: var(--text-a-hover) !important;
  text-decoration: none !important;
}

/* make external links italics to differentiate */
a:not(.internal-link) {
  font-style: italic;
}

/* blockquote */
.cm-quote /* for editor */
{
  color: var(--text-normal) !important;
}

blockquote /* for preview */
{
  border-color: var(--blockquote-border) !important;
}

/* icons at top of panes*/
.file-view-actions a
{
  color: var(--text-muted) !important;
}

.file-view-actions a:hover
{
  color: var(--text-a) !important;
}

/* html tags in editor */
.cm-tag
{
  color: var(--text-accent) !important;
}

/* code blocks in preview */
pre code
{
  padding: 6px !important;
  line-height: normal;
  display: block;
  background-color: var(--pre-code) !important;
}

.markdown-preview-view pre
{
  padding: 0px !important;
}

/* in-line code for editor and preview and code block for editor*/
code,
.cm-inline-code
{
  background-color: var(--pre-code) !important;
  color: var(--text-muted) !important;
  bottom: 0px !important;
}

/* code and code blocks for preview */
.markdown-preview-view code
{
  font-size: 13.5px;
}

/**********/
/* tables */
/**********/
th
{
  font-weight: 800 !important;
}

thead
{
  border-bottom: 4px solid var(--background-modifier-border);
}

.table
{
  background-color: var(--background-secondary-alt);
  border: 1px solid var(--background-modifier-border);
  padding: 4px;
  line-height: normal;
  display: block;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

/* embedded images */
img
{
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* horizontal line in preview */
.markdown-preview-view hr
{
  background-color: var(--text-normal);
}

/* checkboxes */
.markdown-preview-view .task-list-item-checkbox
{
  top: 0px;
}

/*************/
/* side dock */
/*************/

/* side dock text, like file names and backlink context */
.side-dock-panels-container *
{
  font-size: 13px !important;
}

/* side dock titles at top */
.side-dock-title
{

  font-size: 20px !important;
  font-weight: 800 !important;
}

/* hover actions on side dock navigation */
.side-dock-ribbon-tab:hover,
.side-dock-ribbon-tab-inner:hover,
.side-dock-ribbon-action:hover,
.side-dock-ribbon-action.is-active:hover,
.nav-action-button:hover,
.side-dock-collapse-btn:hover
{
  color: var(--text-a);
}

/* condense line spacing on file explorer title list. also avoids character-level word breaks */
.nav-file-title-content,
.search-result-file-title,
.search-result-file-match
{
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  line-height: normal !important;
  word-break: keep-all;
}

/* clean up side bar empty state (e.g. unlinked mentions) */
.search-empty-state
{
  width: auto;
  padding-left: 15px;
  padding-right: 15px;
  line-height: normal;
}

/* font for everything outside of editor/preview panes */
.app-container
{
  font-family: var(--font-family-preview);
}

.status-bar-item
{
  font-family: var(--font-family-preview);
  font-size: 12px;
}

/******************/
/* editor section */
/******************/

/* normal text outside of headings and code */
.cm-s-obsidian
{
  font-family: var(--font-family-editor);
  font-size: 15px;
  color: var(--text-normal);
  padding-left: 10% !important;
  padding-right: 10% !important;
}

.mod-single-child .cm-s-obsidian
{
  font-family: var(--font-family-editor);
  font-size: 15px;
  color: var(--text-normal);
  padding-left: 20% !important;
  padding-right: 20% !important;
}

/* headings */
.cm-header-1
{
  font-family: var(--font-family-editor);
  font-weight: 500;
  font-size: 28px;
  font-weight: bold;
  color: var(--text-title-h1);
}

.cm-header-2
{
  font-family: var(--font-family-editor);
  font-weight: 500;
  font-size: 26px;
  font-weight: bold;
  color: var(--text-title-h2);
}

.cm-header-3
{
  font-family: var(--font-family-editor);
  font-weight: 500;
  font-size: 22px;
  font-weight: bold;
  color: var(--text-title-h3);
}

.cm-header-4
{
  font-family: var(--font-family-editor);
  font-weight: 500;
  font-size: 20px;
  font-weight: bold;
  color: var(--text-title-h4);
}

.cm-header-5
{
  font-family: var(--font-family-editor);
  font-weight: 500;
  font-size: 18px;
  font-weight: bold;
  color: var(--text-title-h5);
}

.cm-header-6
{
  font-family: var(--font-family-editor);
  font-weight: 500;
  font-size: 16px;
  font-weight: bold;
  color: var(--text-title-h5);
}

/******************/
/* preview section */
/******************/

/* normal text outside of headings and code */
.markdown-preview-view
{
  font-family: var(--font-family-preview);
  font-size: 15px;
  color: var(--text-normal);
  padding-left: 10% !important;
  padding-right: 10% !important;
}

.mod-single-child .markdown-preview-view
{
  font-family: var(--font-family-preview);
  font-size: 15px;
  color: var(--text-normal);
  padding-left: 20% !important;
  padding-right: 20% !important;
}

/* headings */
.markdown-preview-view h1
{
  font-family: var(--font-family-preview);
  font-weight: 500;
  font-size: 28px;
  font-weight: bold;
  color: var(--text-title-h1);
}

.markdown-preview-view h2
{
  font-family: var(--font-family-preview);
  font-weight: 500;
  font-size: 26px;
  font-weight: bold;
  color: var(--text-title-h2);
}

.markdown-preview-view h3
{
  font-family: var(--font-family-preview);
  font-weight: 500;
  font-size: 22px;
  font-weight: bold;
  color: var(--text-title-h3);
}

.markdown-preview-view h4
{
  font-family: var(--font-family-preview);
  font-weight: 500;
  font-size: 20px;
  font-weight: bold;
  color: var(--text-title-h4);
}

.markdown-preview-view h5,
{
  font-family: var(--font-family-preview);
  font-weight: 500;
  font-size: 18px;
  font-weight: bold;
  color: var(--text-title-h5);
}

.markdown-preview-view h6
{
  font-family: var(--font-family-preview);
  font-weight: 500;
  font-size: 16px;
  font-weight: bold;
  color: var(--text-title-h5);
}


/* internal embedded link rendering in preview */
.markdown-embed-title
{
  font-weight: 600;
}

.markdown-embed
{
  padding-left: 10px !important;
  padding-right: 10px !important;
  margin-left: 10px !important;
  margin-right: 10px !important;
}

/* remove secondary scroll bar in editor that comes from adding variable padding */
.CodeMirror-scroll::-webkit-scrollbar {
    display: none;
}

/* to have sidebar hide and then reveal on hover
.app-container.is-left-sidedock-collapsed .side-dock.mod-left:not(:hover), .app-container.is-right-sidedock-collapsed .side-dock.mod-right:not(:hover) {
  width: 0px !important;
}
*/
