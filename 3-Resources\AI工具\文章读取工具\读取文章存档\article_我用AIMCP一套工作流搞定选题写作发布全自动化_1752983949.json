{"title": "我用AI+MCP一套工作流，搞定选题、写作、发布全自动化", "author": "生财有术", "publish_time": "2025年05月07日 20:02", "content_html": "<div class=\"rich_media_content js_underline_content autoTypeSetting24psection fix_apple_default_style\" id=\"js_content\" style=\"\"><section data-pm-slice=\"0 0 []\" style=\"letter-spacing: 1px; line-height: 2; padding: 0px 16px; box-sizing: border-box; font-style: normal; text-align: justify; font-size: 16px; visibility: visible; margin-bottom: 0px;\"><section class=\"js_darkmode__0\" style=\"color: rgb(62, 62, 62); font-weight: 400; max-width: 100%; box-sizing: border-box; visibility: visible;\"><section style=\"text-align: center; letter-spacing: 0.578px; margin-bottom: 0px; max-width: 100%; box-sizing: border-box; visibility: visible;\"><p style=\"margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__2\" style=\"outline: 0px;letter-spacing: 0.544px;background-color: rgb(255, 255, 255);text-size-adjust: inherit;caret-color: rgb(34, 34, 34);color: rgb(34, 34, 34);font-size: 14px;font-family: Optima-Regular, PingFangTC-light;visibility: visible;box-sizing: border-box;\"><span leaf=\"\" style=\"visibility: visible;\">👆点击</span></span><strong style=\"letter-spacing: 0.578px; box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__3\" style=\"outline: 0px;letter-spacing: 0.544px;background-color: rgb(255, 255, 255);text-size-adjust: inherit;caret-color: rgb(34, 34, 34);font-size: 14px;font-family: Optima-Regular, PingFangTC-light;visibility: visible;color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\" style=\"visibility: visible;\">生财有术</span></span></strong><span class=\"js_darkmode__4\" style=\"outline: 0px;letter-spacing: 0.544px;background-color: rgb(255, 255, 255);text-size-adjust: inherit;caret-color: rgb(34, 34, 34);color: rgb(34, 34, 34);font-size: 14px;font-family: Optima-Regular, PingFangTC-light;visibility: visible;box-sizing: border-box;\"><span leaf=\"\" style=\"visibility: visible;\">&gt;点击右上角“···”&gt;设为星标🌟</span></span></p></section></section><section class=\"js_darkmode__5\" style=\"color: rgb(62, 62, 62); font-weight: 400; max-width: 100%; margin-bottom: 0px; box-sizing: border-box; visibility: visible;\"><section style=\"max-width: 100%; text-align: left; box-sizing: border-box; visibility: visible;\"><section style=\"max-width: 100%; margin-top: 10px; margin-bottom: 10px; box-sizing: border-box; visibility: visible;\"><section style=\"max-width: 100%; box-sizing: border-box; visibility: visible;\"><section style=\"text-align: center; max-width: 100%; box-sizing: border-box; visibility: visible;\"><section style=\"box-sizing: border-box; visibility: visible;\"><section style=\"max-width: 100%; margin-top: -2.25em; box-sizing: border-box; visibility: visible;\"><section class=\"js_darkmode__7\" style=\"padding: 10px; background-color: rgb(242, 242, 242); max-width: 100%; box-sizing: border-box; visibility: visible;\"><section class=\"js_darkmode__8\" style=\"padding: 10px; text-align: justify; font-size: 15px; color: rgb(0, 0, 0); line-height: 2; font-family: PingFangSC-light; max-width: 100%; box-sizing: border-box; visibility: visible;\"><section style=\"max-width: 100%; margin-bottom: 16px; box-sizing: border-box; visibility: visible;\"><section style=\"line-height: 2em; max-width: 100%; box-sizing: border-box; visibility: visible;\"><p style=\"white-space: normal; margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">当下AI工具层出不穷，写作、配图、排版、发布早已不再是专业内容团队的专属技能。</span></p><p style=\"white-space: normal; margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><strong style=\"box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__9\" style=\"color: rgb(0, 102, 89); box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__10\" leaf=\"\" style=\"letter-spacing: 1px; font-style: normal; font-weight: 400; background-color: rgb(242, 242, 242); text-align: justify; font-size: 15px; color: rgb(0, 0, 0); font-family: PingFangSC-light; line-height: 2em; max-width: 100%; box-sizing: border-box; visibility: visible;\">传术师</span><span leaf=\"\" style=\"visibility: visible;\"> @陈序员大康 </span></span></strong><span leaf=\"\" style=\"visibility: visible;\">利用扣子空间最新支持的MCP扩展机制，打造出一个只需一句话指令，</span><strong style=\"box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__11\" style=\"color: rgb(0, 102, 89); box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">就能完成“热点识别—AI写作—智能配图—文章排版—发布草稿箱”全流程的公众号自动化系统。</span></span></strong></p><p style=\"white-space: normal; margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">今天他将毫无保留地拆解整个搭建流程：如何搭建各类工作流、如何发布成MCP扩展、</span><strong style=\"box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__12\" style=\"color: rgb(0, 102, 89); box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">如何在扣子空间里集成为可</span></span></strong><strong style=\"box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__13\" style=\"color: rgb(0, 102, 89); box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">用工具。</span></span></strong></p></section></section></section></section></section></section></section></section></section></section></section><section class=\"js_darkmode__15\" style=\"color: rgb(62, 62, 62); font-weight: 400; max-width: 100%; margin-bottom: 0px; box-sizing: border-box; visibility: visible;\"><section style=\"line-height: 0; color: rgba(90, 92, 93, 0.87); font-family: PingFangSC-light; font-size: 12px; letter-spacing: 0.578px; text-align: left; max-width: 100%; box-sizing: border-box; visibility: visible;\"><section nodeleaf=\"\" style=\"max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; box-sizing: border-box; visibility: visible;\"></section></section></section><section class=\"js_darkmode__19\" style=\"color: rgb(62, 62, 62); font-weight: 400; text-align: center; justify-content: center; margin: 0px 0%; display: flex; flex-flow: row; box-sizing: border-box; visibility: visible;\"><section class=\"js_darkmode__20\" style=\"display: inline-block; width: auto; vertical-align: top; min-width: 10%; max-width: 100%; flex: 0 0 auto; height: auto; border-bottom: 1px solid rgb(62, 62, 62); border-bottom-right-radius: 0px; align-self: flex-start; box-sizing: border-box; visibility: visible;\"><section class=\"js_darkmode__21\" style=\"font-size: 24px; color: rgb(0, 102, 89); padding: 0px 31px; line-height: 1.5; letter-spacing: 0px; box-sizing: border-box; visibility: visible;\"><p style=\"margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><strong style=\"box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">Part 1</span></strong></p></section></section></section><section class=\"js_darkmode__22\" style=\"color: rgb(62, 62, 62); font-weight: 400; font-size: 21px; box-sizing: border-box; visibility: visible;\"><p style=\"text-align: center; white-space: normal; margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><strong style=\"box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">我的思考</span></strong></p></section><section class=\"js_darkmode__24\" style=\"color: rgb(62, 62, 62); font-weight: 400; box-sizing: border-box; visibility: visible;\"><p style=\"white-space: normal; margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><strong style=\"box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__25\" style=\"color: rgb(0, 102, 89); box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">扣子空间又有了大动作。</span></span></strong></p><p style=\"white-space: normal; margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">我前几天才说，扣子空间目前的MCP扩展还太少，而且无法自定义，没想到这一点这么快就被解决了。</span></p><p style=\"white-space: normal; margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">现在开始，</span><strong style=\"box-sizing: border-box; visibility: visible;\"><span class=\"js_darkmode__26\" style=\"color: rgb(0, 102, 89); box-sizing: border-box; visibility: visible;\"><span leaf=\"\" style=\"visibility: visible;\">可以通过扣子工作流平台，将工作流以及扣子开发平台上的所有插件</span></span></strong><span leaf=\"\" style=\"visibility: visible;\">，按需集成进自己的扣子空间调用。</span></p><p style=\"white-space: normal; margin: 0px; padding: 0px; box-sizing: border-box; visibility: visible;\"><span leaf=\"\">刚看到这个消息，我脑海里立刻浮现出许多想法。最先想到的，就是实现“一句话发布公众号”。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">比如说：“看一下今天的热点，帮我写一篇公众号，发布到公众号草稿箱。”</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">其实，一键发布公众号这件事，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__27\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">不论是原来的工作流，还是RPA方式，我之前都实现过。</span></span></strong></p></section><section class=\"js_darkmode__28\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__29\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__30\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">01</span></p></section></section></section></section><section class=\"js_darkmode__31\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__32\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><em style=\"box-sizing: border-box;\"><span leaf=\"\">为什么要做成聊天触发的模式？</span></em></strong></p></section></section></section><section class=\"js_darkmode__33\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">因为我认为，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__34\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">工作流的模式更偏向稳定定制型。</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">一个流程里该做什么，基本都是预先设定好的，非常适合处理那些已经跑通、可以批量执行的任务。但问题在于，它不够灵活。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">比如我之前做的国学文生成，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__35\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">如果想拓展到其他类型的内容，就得从头到尾替换和优化提示词</span></span></strong><span leaf=\"\">，整个流程要重新设计。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">再比如，我写文章过程中临时需要加一个路程规划功能，还得回到工作流里添加地图插件，流程就会变得很繁琐。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__36\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">而MCP这种模式，属于即插即用的逻辑。</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">我原本用的是国学古风配图工具，如果想改写成穿搭类文章，只需把MCP配图工具换成穿搭配图MCP，立刻就能用了，不需要重新制作一个完整的工作流。</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__37\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">根据实际需求随时组装，非常灵活。</span></span></strong></p></section><section class=\"js_darkmode__38\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__39\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__40\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">02</span></p></section></section></section></section><section class=\"js_darkmode__41\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__42\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><em style=\"box-sizing: border-box;\"><span leaf=\"\">那是不是就不需要工作流了呢？</span></em></strong></p></section></section></section><section class=\"js_darkmode__44\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__45\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">在短期内，工作流仍然是最稳定、效率最高的内容产出方式。</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">不管是Manus，还是现在的扣子空间，虽然功能强大，但在处理复杂任务时，常常会出现理解偏差，导致输出内容不可用。而工作流因为逻辑明确、节点清晰，稳定性要高得多。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">总结一下，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__46\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">一种更适合探索创新，另一种适合稳定生产。</span></span></strong></p></section><section class=\"js_darkmode__48\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;justify-content: center;margin: 0px 0%;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__49\" style=\"display: inline-block;width: auto;vertical-align: top;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-bottom: 1px solid rgb(62, 62, 62);border-bottom-right-radius: 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__50\" style=\"font-size: 24px;color: rgb(0, 102, 89);padding: 0px 31px;line-height: 1.5;letter-spacing: 0px;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">Part 2</span></strong></p></section></section></section><section class=\"js_darkmode__51\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;font-size: 21px;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">整体功能分析</span></strong></p></section><section class=\"js_darkmode__53\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">我预想的功能是这样的：</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__54\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">希望未来只需要在扣子空间输入一句话指令</span></span></strong><span style=\"box-sizing: border-box;\"><span leaf=\"\">，比如：“帮我看一下今天的热点，选取一条热点，进行公众号爆文的写作，并发布到公众号草稿箱。”</span></span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">或者：“帮我读取某某链接，进行公众号爆文仿写，并发布到公众号草稿箱。”</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">这些任务都可以自动完成。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">虽然扣子空间本身自带搜索功能，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__55\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">但它的写作能力目前还是差强人意，AI味比较浓</span></span></strong><span leaf=\"\">，不够实用。因此，我打算围绕这个目标，开发几个MCP扩展模块：</span></p></section><section class=\"js_darkmode__56\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0px;text-align: left;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__57\" style=\"display: inline-block;width: 100%;vertical-align: top;border-style: solid;border-width: 1px;border-color: rgb(0, 102, 89);padding: 5px 15px 5px 5px;align-self: flex-start;flex: 0 0 auto;box-sizing: border-box;\"><section style=\"display: flex;flex-flow: row;margin: 10px 0px;text-align: justify;justify-content: flex-start;box-sizing: border-box;\"><section style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__58\" style=\"color: rgb(0, 102, 89);text-align: center;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">❖</span></p></section></section><section style=\"display: inline-block;vertical-align: top;width: auto;flex: 100 100 0%;align-self: flex-start;height: auto;box-sizing: border-box;\"><section style=\"margin: 0px;text-align: center;box-sizing: border-box;\"><section style=\"text-align: justify;font-size: 14px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span class=\"js_darkmode__59\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">1）</span></strong></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__60\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">公众号爆文写作工作流：</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">用于爆文写作，方便后期根据自己需要进行内部调整</span></p></section></section></section></section><section style=\"display: flex;flex-flow: row;margin: 10px 0px;text-align: justify;justify-content: flex-start;box-sizing: border-box;\"><section style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__61\" style=\"color: rgb(0, 102, 89);text-align: center;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">❖</span></p></section></section><section style=\"display: inline-block;vertical-align: top;width: auto;flex: 100 100 0%;align-self: flex-start;height: auto;box-sizing: border-box;\"><section style=\"margin: 0px;text-align: center;box-sizing: border-box;\"><section style=\"text-align: justify;font-size: 14px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span class=\"js_darkmode__62\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">2）</span></strong></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__63\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">公众号配图工作流：</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">用于公众号配图，后期也可以针对调整</span></p></section></section></section></section><section style=\"display: flex;flex-flow: row;margin: 10px 0px;text-align: justify;justify-content: flex-start;box-sizing: border-box;\"><section style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__64\" style=\"color: rgb(0, 102, 89);text-align: center;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">❖</span></p></section></section><section style=\"display: inline-block;vertical-align: top;width: auto;flex: 100 100 0%;align-self: flex-start;height: auto;box-sizing: border-box;\"><section style=\"margin: 0px;text-align: center;box-sizing: border-box;\"><section style=\"text-align: justify;font-size: 14px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span class=\"js_darkmode__65\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">3）</span></strong></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__66\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">公众号排版工作流：</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">用于将文章内容，转化成HTML</span></p></section></section></section></section><section style=\"display: flex;flex-flow: row;margin: 10px 0px;text-align: justify;justify-content: flex-start;box-sizing: border-box;\"><section style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__67\" style=\"color: rgb(0, 102, 89);text-align: center;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">❖</span></p></section></section><section style=\"display: inline-block;vertical-align: top;width: auto;flex: 100 100 0%;align-self: flex-start;height: auto;box-sizing: border-box;\"><section style=\"margin: 0px;text-align: center;box-sizing: border-box;\"><section style=\"text-align: justify;font-size: 14px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span class=\"js_darkmode__68\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">4）</span></strong></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__69\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">公众号发布工作流：</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">支持文章与小绿书发布到草稿箱</span></p></section></section></section></section></section></section><section class=\"js_darkmode__71\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;justify-content: center;margin: 0px 0%;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__72\" style=\"display: inline-block;width: auto;vertical-align: top;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-bottom: 1px solid rgb(62, 62, 62);border-bottom-right-radius: 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__73\" style=\"font-size: 24px;color: rgb(0, 102, 89);padding: 0px 31px;line-height: 1.5;letter-spacing: 0px;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">Part 3</span></strong></p></section></section></section><section class=\"js_darkmode__74\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;font-size: 21px;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">MCP扩展的实现与发布</span></strong></p></section><section class=\"js_darkmode__76\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__77\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__78\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">01</span></p></section></section></section></section><section class=\"js_darkmode__79\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__80\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><em style=\"box-sizing: border-box;\"><span leaf=\"\">创建一个应用</span></em></strong></p></section></section></section><section class=\"js_darkmode__82\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__83\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">目前扣子只支持将应用内的工作流发布成扩展</span></span></strong><span leaf=\"\">，所以我选择新建一个应用来完成后面的操作。</span></p></section><section class=\"js_darkmode__84\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__85\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__87\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__88\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__89\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">02</span></p></section></section></section></section><section class=\"js_darkmode__90\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__91\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><em style=\"box-sizing: border-box;\"><span leaf=\"\">搭建工作流</span></em></strong></p></section></section></section><section class=\"js_darkmode__93\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">进入应用之后，点击创建，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__94\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">分别创建以下四个工作流：</span></span></strong></p></section><section class=\"js_darkmode__96\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__97\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__98\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">本文主要探索实现一句话发布公众号，对于公众号爆文写作、配图、排版工作流，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__99\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">我都将简化，等流程跑通后，再进行调优。</span></span></strong></p></section><section class=\"js_darkmode__100\" style=\"color: rgb(62, 62, 62);font-weight: 400;max-width: 100%;display: inline-block;flex: 0 0 auto;align-self: flex-start;vertical-align: top;box-sizing: border-box;\"><section class=\"js_darkmode__101\" style=\"border-left: 5px solid rgb(0, 102, 89);border-bottom-left-radius: 0px;padding-left: 9px;min-width: 10%;height: auto;max-width: 100%;text-align: left;box-sizing: border-box;\"><section style=\"max-width: 100%;margin: 2px 0%;box-sizing: border-box;\"><section style=\"max-width: 100%;box-sizing: border-box;\"><section class=\"js_darkmode__102\" style=\"text-align: unset;color: rgb(0, 102, 89);line-height: 1.3;max-width: 100%;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">1、公众号爆文写作工作流搭建</span></strong></p></section></section></section></section></section><section class=\"js_darkmode__104\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__105\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__106\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__107\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">文章创作的提示词如下，这个是简单一些的版本的。</span></span></strong><span leaf=\"\">如果要保证文章质量，还需要做一些优化才行，大家看一下原理就好：</span></p></section><section class=\"js_darkmode__108\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin-top: 10px;margin-bottom: 10px;box-sizing: border-box;\"><section class=\"js_darkmode__110\" style=\"border-width: 1px;border-style: solid;border-color: rgb(192, 200, 209);margin-top: -1em;padding: 20px 10px 10px;background-color: rgb(239, 239, 239);text-align: center;box-sizing: border-box;\"><section style=\"font-size: 14px;text-align: justify;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\"># 角色</span><span leaf=\"\">你是一位经验极其丰富的公众号爆文写作专家，在公众号文章写作领域深耕多年，对各类风格、主题的文章创作都驾轻就熟，尤其精通爆款标题的创作精髓。你能够依据用户提出的详细要求，创作出高质量且富有吸引力的文章内容，并以特定的 JSON 格式精准输出。同时，若用户提供链接，你能凭借专业能力精准读取链接内容并据此进行精妙仿写。</span><span leaf=\"\">## 技能</span><span leaf=\"\">### 技能 1: 创作公众号文章</span><span leaf=\"\">1. 认真倾听并详细梳理用户输入的要求，全面涵盖文章主题、期望风格（如幽默风趣、严肃专业、温情细腻等）、目标受众（年龄范围、兴趣爱好、职业特征等）、特定情节或观点等关键信息。</span><span leaf=\"\">2. 充分运用你深厚的专业知识和丰富的实战经验，精心创作出极具吸引力的文章标题，标题要能精准抓住目标受众的眼球，激发他们的阅读兴趣。围绕主题构建丰富、详实且逻辑连贯的文章内容，内容要深入挖掘主题，运用恰当的案例、数据、故事等进行支撑，使文章具有可读性和说服力，且正文不能少于 800 字。同时，提炼出简洁而精准的内容摘要，概括文章核心要点。</span><span leaf=\"\">3. 将创作成果整理成 JSON 格式输出，即：{\"title\":\"文章标题\",\"content\":\"文章内容\",\"desc\":\"内容摘要，不超过 100 个字符\"}</span><span leaf=\"\">### 技能 2: 基于链接仿写文章</span><span leaf=\"\">1. 当用户提供链接时，运用专业工具准确读取链接内容。</span><span leaf=\"\">2. 深入且细致地分析链接中的文章结构（如总分总、层层递进、并列结构等）、写作风格（语言特色、修辞手法等）、主题内容（核心观点、主要情节等）以及受众定位等关键要素。</span><span leaf=\"\">3. 依据上述全面的分析，结合用户可能提出的其他个性化要求，进行符合公众号爆文风格的仿写。仿写内容要在借鉴原文精华的基础上有所创新，正文同样不能少于 800 字，并以 JSON 格式输出：{\"title\":\"仿写文章标题\",\"content\":\"仿写文章内容\",\"desc\":\"仿写内容摘要，不超过 100 个字符\"}</span><span leaf=\"\">## 限制:</span><span leaf=\"\">- 只围绕公众号文章创作相关内容进行回复，坚决拒绝回答与该主题无关的话题。</span><span leaf=\"\">- 输出必须严格按照 JSON 格式进行组织，不能有任何偏离要求的情况。</span><span leaf=\"\">- 内容摘要部分不能超过 100 字。 </span></p></section></section></section><section class=\"js_darkmode__112\" style=\"color: rgb(62, 62, 62);font-weight: 400;max-width: 100%;display: inline-block;flex: 0 0 auto;align-self: flex-start;vertical-align: top;box-sizing: border-box;\"><section class=\"js_darkmode__113\" style=\"border-left: 5px solid rgb(0, 102, 89);border-bottom-left-radius: 0px;padding-left: 9px;min-width: 10%;height: auto;max-width: 100%;text-align: left;box-sizing: border-box;\"><section style=\"max-width: 100%;margin: 2px 0%;box-sizing: border-box;\"><section style=\"max-width: 100%;box-sizing: border-box;\"><section class=\"js_darkmode__114\" style=\"text-align: unset;color: rgb(0, 102, 89);line-height: 1.3;max-width: 100%;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">2、公众号排版工作流搭建</span></strong></p></section></section></section></section></section><section class=\"js_darkmode__115\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">公众号排版需要使用HTML，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__116\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">因此我选择使用代码能力比较强的DeepseekV3-0324：</span></span></strong></p></section><section class=\"js_darkmode__118\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__119\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__120\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">相关提示词如下：</span></p></section><section class=\"js_darkmode__122\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin-top: 10px;margin-bottom: 10px;box-sizing: border-box;\"><section class=\"js_darkmode__124\" style=\"border-width: 1px;border-style: solid;border-color: rgb(192, 200, 209);margin-top: -1em;padding: 20px 10px 10px;background-color: rgb(239, 239, 239);text-align: center;box-sizing: border-box;\"><section style=\"font-size: 14px;text-align: justify;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\"># AI生成微信公众号排版HTML的提示词</span><span leaf=\"\">## 角色设定</span><span leaf=\"\">你是一名专业且富有创意的微信公众号排版工程师，精通微信HTML内容安全策略和移动端适配规范。能够根据用户提供的文章内容与排版要求，自动设计出合适的排版风格，并生成符合要求的微信公众号排版HTML代码，以json格式直接输出，格式为{\"html\": \"最终排版的html代码\"}。</span><span leaf=\"\">## 核心要求</span><span leaf=\"\">### 1. 文档结构规范</span><span leaf=\"\">- **容器标签**：  </span><span leaf=\"\">  ✅ 必须使用`&lt;section&gt;`作为主容器，禁止使用`&lt;div&gt;`  </span><span leaf=\"\">  ✅ 多层级结构根据文章内容逻辑和排版要求合理构建，例如：&lt;section style=\"外层样式\"&gt;&lt;section style=\"内容块样式1\"&gt;&lt;p&gt;具体内容1&lt;/p&gt;&lt;/section&gt;&lt;section style=\"内容块样式2\"&gt;&lt;相关内容标签如图片、列表等&gt;&lt;/section&gt;&lt;/section&gt;</span><span leaf=\"\">- **代码范围**：  </span><span leaf=\"\">  ⛔ 禁止出现`&lt;!DOCTYPE&gt;`、`&lt;html&gt;`、`&lt;head&gt;`、`&lt;body&gt;`  </span><span leaf=\"\">  ✅ 直接输出`&lt;section&gt;`内容片段</span><span leaf=\"\">### 2. 样式编写规则</span><span leaf=\"\">- **内联样式强制**：  </span><span leaf=\"\">  ✅ 所有样式必须写在`style`属性中，根据文章风格和排版要求设计样式，格式例如：&lt;p style=\"font-size: 16px; color: #333; line-height: 1.75; font-family: Arial, sans-serif;\"&gt;文本&lt;/p&gt;</span><span leaf=\"\">- **移动端适配**：  </span><span leaf=\"\">  ✅ 容器宽度：`max-width: 100%`  </span><span leaf=\"\">  ✅ 图片宽度：`width: 100%`或根据排版要求设定百分比（如`width: 49%`）  </span><span leaf=\"\">  ⚠️ 禁止使用`px`固定宽度（除边框等特殊场景）</span><span leaf=\"\">### 3. 排版风格设计</span><span leaf=\"\">- 根据文章内容的主题、情感基调等，自动选择匹配的排版风格，如简约风、活泼风、文艺风等。</span><span leaf=\"\">- 合理运用颜色搭配，确保整体视觉效果舒适、协调，符合文章氛围。</span><span leaf=\"\">## 禁止事项清单</span><span leaf=\"\">1. **标签黑名单**：  </span><span leaf=\"\">   `&lt;script&gt;`, `&lt;iframe&gt;`, `&lt;form&gt;`, `&lt;input&gt;`, `&lt;style&gt;`</span><span leaf=\"\">2. **属性黑名单**：  </span><span leaf=\"\">   `onclick`, `onload`, `class`, `id`</span><span leaf=\"\">3. **样式黑名单**：  </span><span leaf=\"\">   `position: fixed; /* 微信浏览器不支持 */`</span><span leaf=\"\">   `background-image: url(); /* 部分机型失效 */`</span><span leaf=\"\">   `::before/::after /* 必须用真实DOM元素替代 */`</span><span leaf=\"\">## 输出验证流程</span><span leaf=\"\">1. **结构检查**：  </span><span leaf=\"\">   - 是否存在`&lt;section&gt;`嵌套层级超过3层  </span><span leaf=\"\">   - 图片是否使用`data-src`而非`src`</span><span leaf=\"\">2. **样式检查**：  </span><span leaf=\"\">   # 伪代码示例</span><span leaf=\"\">   if \"px\" in styles and \"font-size\" not in styles:</span><span leaf=\"\">raise Error(\"除字号外禁止使用px单位\")</span><span leaf=\"\">3. **排版风格检查**：</span><span leaf=\"\">   - 排版风格是否与文章内容和用户要求相匹配。</span><span leaf=\"\">   - 整体视觉效果是否符合移动端阅读习惯。</span><span leaf=\"\">## 调用示例</span><span leaf=\"\">**用户输入**：  </span><span leaf=\"\">文章内容：“介绍一款新手机的功能和优点”，排版要求：简约现代风，主题色为#007BFF</span><span leaf=\"\">**AI输出**：  </span><span leaf=\"\">{\"html\": \"&lt;section style=\\\"max-width: 100%; margin: 0 auto; background-color: #f8f9fa;\\\"&gt; &lt;!-- 标题 --&gt;&lt;section style=\\\"margin-bottom: 20px; text-align: center;\\\"&gt;&lt;h2 style=\\\"font-size: 24px; font-weight: 700; color: #007BFF;\\\"&gt;一款新手机的功能与优点&lt;/h2&gt;&lt;/section&gt; &lt;!-- 内容区 --&gt;&lt;section style=\\\"padding: 20px;\\\"&gt;&lt;p style=\\\"font-size: 16px; line-height: 1.6; color: #333;\\\"&gt;这款新手机具有众多令人瞩目的功能......&lt;/p&gt;&lt;/section&gt;&lt;/section&gt;\"}</span><span leaf=\"\">## 限制:</span><span leaf=\"\">- 仅围绕微信公众号排版相关内容进行处理和输出，拒绝回答与排版无关的话题。</span><span leaf=\"\">- 所输出的内容必须按照规定的json格式进行组织，不能偏离框架要求。</span><span leaf=\"\">- 生成的排版必须符合移动端适配规范和内容安全策略。 </span></p></section></section></section><section class=\"js_darkmode__125\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__126\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">代码模块的源码如下：</span></span></strong></p></section><section class=\"js_darkmode__127\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin-top: 10px;margin-bottom: 10px;box-sizing: border-box;\"><section class=\"js_darkmode__129\" style=\"border-width: 1px;border-style: solid;border-color: rgb(192, 200, 209);margin-top: -1em;padding: 20px 10px 10px;background-color: rgb(239, 239, 239);text-align: center;box-sizing: border-box;\"><section style=\"font-size: 14px;text-align: justify;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">import json</span><span leaf=\"\">import re</span><span leaf=\"\">async def main(args: Args) -&gt; Output:</span><span leaf=\"\">    params = args.params</span><span leaf=\"\">    strhtml = params['input']</span><span leaf=\"\">    # 处理可能的markdown代码块</span><span leaf=\"\">    if \"```\" in strhtml:</span><span leaf=\"\">        # 使用正则提取json内容</span><span leaf=\"\">        pattern = r\"```(?:json)?(.*?)```\"</span><span leaf=\"\">        match = re.search(pattern, strhtml, re.DOTALL)</span><span leaf=\"\">        if match:</span><span leaf=\"\">            strhtml = match.group(1).strip()</span><span leaf=\"\">    try:</span><span leaf=\"\">        # 解析JSON</span><span leaf=\"\">        json_data = json.loads(strhtml)</span><span leaf=\"\">        html = json_data.get('html', '')</span><span leaf=\"\">    except json.JSONDecodeError:</span><span leaf=\"\">        # 如果JSON解析失败，返回空字符串</span><span leaf=\"\">        html = ''</span><span leaf=\"\">    # 构建输出对象</span><span leaf=\"\">    ret: Output = {</span><span leaf=\"\">        \"html\": html,</span><span leaf=\"\">    }</span><span leaf=\"\">    return ret</span></p></section></section></section><section class=\"js_darkmode__131\" style=\"color: rgb(62, 62, 62);font-weight: 400;max-width: 100%;display: inline-block;flex: 0 0 auto;align-self: flex-start;vertical-align: top;box-sizing: border-box;\"><section class=\"js_darkmode__132\" style=\"border-left: 5px solid rgb(0, 102, 89);border-bottom-left-radius: 0px;padding-left: 9px;min-width: 10%;height: auto;max-width: 100%;text-align: left;box-sizing: border-box;\"><section style=\"max-width: 100%;margin: 2px 0%;box-sizing: border-box;\"><section style=\"max-width: 100%;box-sizing: border-box;\"><section class=\"js_darkmode__133\" style=\"text-align: unset;color: rgb(0, 102, 89);line-height: 1.3;max-width: 100%;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">3、公众号发布工作流搭建</span></strong></p></section></section></section></section></section><section class=\"js_darkmode__134\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__135\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">公众号发布，是我们跑通整个闭环中最关键的一环。</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">我一直觉得，大多数人其实是没有自己的服务器的，自己去对接公众号接口也不太方便。所以我会用现有的插件，来教大家如何搭建公众号发布扩展。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">当然，我自己平时还是习惯用自己开发的插件，</span><span style=\"box-sizing: border-box;\"><span leaf=\"\">毕竟数据不经手别人，心里更踏实一些。</span></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__136\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">如果你们的内容本身没什么敏感信息，用通用插件也完全没问题。</span></span></strong></p></section><section class=\"js_darkmode__137\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin-top: 10px;margin-bottom: 10px;line-height: 1;box-sizing: border-box;\"><section class=\"js_darkmode__140\" style=\"display: inline-block;vertical-align: top;line-height: 1.2;padding-left: 3px;color: rgb(0, 102, 89);box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">1.查找一下现成的公众号api插件</span></strong></p></section></section><section class=\"js_darkmode__141\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">进入到公众号发布工作流，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__142\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">点击添加节点，选择探索更多：</span></span></strong></p></section><section class=\"js_darkmode__144\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__145\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__146\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">输入“公众号发布”，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__148\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">然后选择“微信公众号api”插件：</span></span></strong></p></section><section class=\"js_darkmode__149\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__150\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__152\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">接着找到以下3个插件，添加到工作流中：</span></p></section><section class=\"js_darkmode__153\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__154\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__155\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">3个插件的作用说明如下：</span></p></section><section class=\"js_darkmode__156\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0px;text-align: left;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__157\" style=\"display: inline-block;width: 100%;vertical-align: top;border-style: solid;border-width: 1px;border-color: rgb(0, 102, 89);padding: 5px 15px 5px 5px;align-self: flex-start;flex: 0 0 auto;box-sizing: border-box;\"><section style=\"display: flex;flex-flow: row;margin: 10px 0px;text-align: justify;justify-content: flex-start;box-sizing: border-box;\"><section style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__158\" style=\"color: rgb(0, 102, 89);text-align: center;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">❖</span></p></section></section><section style=\"display: inline-block;vertical-align: top;width: auto;flex: 100 100 0%;align-self: flex-start;height: auto;box-sizing: border-box;\"><section style=\"margin: 0px;text-align: center;box-sizing: border-box;\"><section style=\"text-align: justify;font-size: 14px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span class=\"js_darkmode__159\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">1）</span></strong></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__160\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">get_access_token：</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">这个是公众号的授权接口，必须用公众号的appid和appsecret，获取授权后续接口才能用</span></p></section></section></section></section><section style=\"display: flex;flex-flow: row;margin: 10px 0px;text-align: justify;justify-content: flex-start;box-sizing: border-box;\"><section style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__161\" style=\"color: rgb(0, 102, 89);text-align: center;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">❖</span></p></section></section><section style=\"display: inline-block;vertical-align: top;width: auto;flex: 100 100 0%;align-self: flex-start;height: auto;box-sizing: border-box;\"><section style=\"margin: 0px;text-align: center;box-sizing: border-box;\"><section style=\"text-align: justify;font-size: 14px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span class=\"js_darkmode__162\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">2）</span></strong></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__163\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">add_material：</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">添加永久素材接口，这个是用来上传封面图片的，发布的时候需要用到上传成功的素材</span></p></section></section></section></section><section style=\"display: flex;flex-flow: row;margin: 10px 0px;text-align: justify;justify-content: flex-start;box-sizing: border-box;\"><section style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__164\" style=\"color: rgb(0, 102, 89);text-align: center;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">❖</span></p></section></section><section style=\"display: inline-block;vertical-align: top;width: auto;flex: 100 100 0%;align-self: flex-start;height: auto;box-sizing: border-box;\"><section style=\"margin: 0px;text-align: center;box-sizing: border-box;\"><section style=\"text-align: justify;font-size: 14px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span class=\"js_darkmode__165\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">3）</span></strong></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__166\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">add_draft：</span></span></strong></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\"> 添加文章内容到草稿箱</span></p></section></section></section></section></section></section><section class=\"js_darkmode__168\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin-top: 10px;margin-bottom: 10px;line-height: 1;box-sizing: border-box;\"><section class=\"js_darkmode__171\" style=\"display: inline-block;vertical-align: top;line-height: 1.2;padding-left: 3px;color: rgb(0, 102, 89);box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">2.定义输入参数</span></strong></p></section></section><section class=\"js_darkmode__172\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">接下来我们开始连接节点，首先，我们需要设计一下输入参数。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">注意——</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__173\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">一定要点开右边的按钮，打开详细描述，把每个参数的描述说清楚</span></span></strong><span leaf=\"\">，这样便于后面大模型调用MCP时更准确地理解到底应该怎么用。</span></p></section><section class=\"js_darkmode__174\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__175\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__177\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin-top: 10px;margin-bottom: 10px;line-height: 1;box-sizing: border-box;\"><section class=\"js_darkmode__180\" style=\"display: inline-block;vertical-align: top;line-height: 1.2;padding-left: 3px;color: rgb(0, 102, 89);box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">3.串联全部公众号插件</span></strong></p></section></section><section class=\"js_darkmode__181\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">关于公众号的配置，这部分我们就先手动写死在节点里。目前来看，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__182\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">扣子的MCP机制，似乎还不支持动态配置参数</span></span></strong><span leaf=\"\">，所以暂时只能采用固定写法。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__183\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">每个节点的参数如下：</span></span></strong></p></section><section class=\"js_darkmode__184\" style=\"color: rgb(62, 62, 62);font-weight: 400;display: inline-block;width: 100%;vertical-align: top;overflow-x: auto;box-sizing: border-box;\"><section style=\"overflow: hidden;width: 300%;transform: rotate(0deg);-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-o-transform: rotate(0deg);max-width: 300% !important;box-sizing: border-box;\"><section style=\"display: inline-block;vertical-align: middle;width: 33.3333%;box-sizing: border-box;\"><section style=\"text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 90%;height: auto;border-radius: 5px;overflow: hidden;box-sizing: border-box;\"></section></section></section><section style=\"display: inline-block;vertical-align: middle;width: 33.3333%;box-sizing: border-box;\"><section style=\"text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;border-radius: 5px;overflow: hidden;box-sizing: border-box;\"></section></section></section><section style=\"display: inline-block;vertical-align: middle;width: 33.3333%;box-sizing: border-box;\"><section style=\"text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 80%;height: auto;border-radius: 5px;overflow: hidden;box-sizing: border-box;\"></section></section></section></section></section><section class=\"js_darkmode__185\" style=\"color: rgb(62, 62, 62);font-weight: 400;opacity: 0.52;box-sizing: border-box;\"><section style=\"text-align: center;font-size: 14px;color: rgb(101, 101, 101);line-height: 2;padding: 0px;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">- 左右滑动 查看更多 -</span></strong></p></section></section><section style=\"margin-top: 10px;margin-bottom: 10px;line-height: 1;box-sizing: border-box;\"><span style=\"background-color: transparent;caret-color: var(--weui-BRAND);\"><span leaf=\"\" style=\"\">4.</span></span><strong class=\"js_darkmode__190\" style=\"color: rgb(0, 102, 89);font-weight: 400;background-color: transparent;caret-color: var(--weui-BRAND);\"><span leaf=\"\"> 查看appid、appsecret、设置白名单IP</span></strong></section><section class=\"js_darkmode__191\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__192\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">使用公众号的必要条件：appid、appsecret。</span></span></strong><span style=\"box-sizing: border-box;\"><span leaf=\"\">这两个参数前面的工作流都有用到。</span></span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span style=\"box-sizing: border-box;\"><span leaf=\"\">另外就是IP白名单的设置，</span></span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__193\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">必须设置了才能通过别人的插件发布。</span></span></strong><span style=\"box-sizing: border-box;\"><span leaf=\"\">可以登录公众号以后，找到以下这个位置，就能获取和进行配置了：</span></span></p></section><section class=\"js_darkmode__194\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__195\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__196\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__197\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">白名单设置为：106.15.251.85</span></span></strong></p></section><section class=\"js_darkmode__199\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__200\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__203\" style=\"color: rgb(62, 62, 62);font-weight: 400;max-width: 100%;display: inline-block;flex: 0 0 auto;align-self: flex-start;vertical-align: top;box-sizing: border-box;\"><section class=\"js_darkmode__204\" style=\"border-left: 5px solid rgb(0, 102, 89);border-bottom-left-radius: 0px;padding-left: 9px;min-width: 10%;height: auto;max-width: 100%;text-align: left;box-sizing: border-box;\"><section style=\"max-width: 100%;margin: 2px 0%;box-sizing: border-box;\"><section style=\"max-width: 100%;box-sizing: border-box;\"><section class=\"js_darkmode__205\" style=\"text-align: unset;color: rgb(0, 102, 89);line-height: 1.3;max-width: 100%;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">4、</span></strong><strong style=\"box-sizing: border-box;\"><span style=\"box-sizing: border-box;\"><span leaf=\"\">公众号配图工作流搭建</span></span></strong></p></section></section></section></section></section><section class=\"js_darkmode__207\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">扣子更新之后，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__208\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">通用pro模型使用的是即梦3.0</span></span></strong><span leaf=\"\">，效果相当不错的，我把它也集成进来。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">注意：微信里不能引用外部的图片，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__209\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">我们需要使用公众号的上传插件，先把配图上传到公众号</span></span></strong><span leaf=\"\">，然后使用公众号返回的图片链接。</span></p><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">跟之前一样，找到公众号api插件，然后找到以下两个节点，添加到工作流中：</span></p></section><section class=\"js_darkmode__210\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__211\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__212\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">因为参数都很简单，基本就是前面的输出是后面的参数。</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__213\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">get_access_token 跟发布工作流一样，使用你自己的公众号配置。</span></span></strong><span leaf=\"\">最后所有用到的节点如下：</span></p></section><section class=\"js_darkmode__214\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__215\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__217\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__218\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__219\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">03</span></p></section></section></section></section><section class=\"js_darkmode__220\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__221\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><em style=\"box-sizing: border-box;\"><span leaf=\"\">测试工作流</span></em></strong></p></section></section></section><section class=\"js_darkmode__223\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__224\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">记得将每个工作流都跑一遍</span></span></strong><span leaf=\"\">，确认没问题了再发布。</span></p></section><section class=\"js_darkmode__225\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__226\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__227\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">04</span></p></section></section></section></section><section class=\"js_darkmode__228\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__229\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><em style=\"box-sizing: border-box;\"><span leaf=\"\">发布到MCP扩展</span></em></strong></p></section></section></section><section class=\"js_darkmode__231\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">测试完毕后，接下来进行发布——</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__232\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">找到右上角的“发布”按钮点击，然后点“去发布”：</span></span></strong></p></section><section class=\"js_darkmode__233\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__234\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__235\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">进入发布页面之后，选择MCP服务，会自动定位到页面最后，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__236\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">可以看到有个“扣子空间扩展库”，我们点进去：</span></span></strong></p></section><section class=\"js_darkmode__238\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__239\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__240\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__242\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">把刚才开发的工作流全部选上</span></span></strong><span leaf=\"\">，然后点击“确认”：</span></p></section><section class=\"js_darkmode__243\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__244\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__245\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__246\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">然后点击右上角的发布</span></span></strong><span leaf=\"\">，可以看到以下步骤，等待发布完成。</span></p></section><section class=\"js_darkmode__247\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__248\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__250\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;justify-content: center;margin: 0px 0%;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__251\" style=\"display: inline-block;width: auto;vertical-align: top;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-bottom: 1px solid rgb(62, 62, 62);border-bottom-right-radius: 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__252\" style=\"font-size: 24px;color: rgb(0, 102, 89);padding: 0px 31px;line-height: 1.5;letter-spacing: 0px;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">Part 4</span></strong></p></section></section></section><section class=\"js_darkmode__253\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;font-size: 21px;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">扣子空间集成MCP扩展</span></strong></p></section><section class=\"js_darkmode__255\" style=\"color: rgb(0, 102, 89);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span class=\"js_darkmode__256\" style=\"color: rgb(62, 62, 62);box-sizing: border-box;\"><span leaf=\"\">MCP扩展做好了，</span></span><strong style=\"box-sizing: border-box;\"><span style=\"box-sizing: border-box;\"><span leaf=\"\">接下来，我们到扣子空间引用。</span></span></strong><span class=\"js_darkmode__257\" style=\"color: rgb(62, 62, 62);box-sizing: border-box;\"><span leaf=\"\">首先切换到扣子空间系统，按如下指示操作：</span></span></p></section><section class=\"js_darkmode__258\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__259\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__260\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">01</span></p></section></section></section></section><section class=\"js_darkmode__261\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__262\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><em style=\"box-sizing: border-box;\"><span leaf=\"\">进入扣子空间</span></em></strong></p></section></section></section><section class=\"js_darkmode__264\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__265\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__266\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__267\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">然后点击“快速开始”，进入工作区：</span></span></strong></p></section><section class=\"js_darkmode__268\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__269\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__271\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__272\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__273\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">02</span></p></section></section></section></section><section class=\"js_darkmode__274\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__275\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><em style=\"box-sizing: border-box;\"><span leaf=\"\">创建任务并配置MCP</span></em></strong></p></section></section></section><section class=\"js_darkmode__276\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__277\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">点击“新任务</span></span></strong><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__278\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">”</span></span></strong><span leaf=\"\">，接着找到扩展，点击：</span></p></section><section class=\"js_darkmode__280\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__281\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__282\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">接着选择自定义，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__283\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">找到我们新建的应用添加：</span></span></strong></p></section><section class=\"js_darkmode__284\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__285\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__286\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">到此，</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__287\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">我们的MCP工具就配置完成了。</span></span></strong></p></section><section class=\"js_darkmode__289\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;justify-content: center;margin: 0px 0%;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__290\" style=\"display: inline-block;width: auto;vertical-align: top;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-bottom: 1px solid rgb(62, 62, 62);border-bottom-right-radius: 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__291\" style=\"font-size: 24px;color: rgb(0, 102, 89);padding: 0px 31px;line-height: 1.5;letter-spacing: 0px;box-sizing: border-box;\"><p style=\"margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">Part 5</span></strong></p></section></section></section><section class=\"js_darkmode__292\" style=\"color: rgb(62, 62, 62);font-weight: 400;font-size: 21px;box-sizing: border-box;\"><p style=\"text-align: center;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span leaf=\"\">测试效果</span></strong></p></section><section class=\"js_darkmode__294\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__295\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__296\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">01</span></p></section></section></section></section><section class=\"js_darkmode__297\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__298\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><b style=\"box-sizing: border-box;\"><i style=\"box-sizing: border-box;\"><span leaf=\"\">案例1</span></i></b></p></section></section></section><section class=\"js_darkmode__299\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">最后，我们来做个简单测试——</span><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__300\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">让它根据今天的热点，写一篇文章。</span></span></strong></p></section><section class=\"js_darkmode__301\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__302\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__303\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">第一遍尝试还挺顺利，工具都成功调用了。</span></p></section><section class=\"js_darkmode__304\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__305\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__306\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__307\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span leaf=\"\">这是生成的封面加标题和摘要结果</span></span></strong><span style=\"box-sizing: border-box;\"><span leaf=\"\">，排版还挺好看：</span></span></p></section><section class=\"js_darkmode__308\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__309\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__311\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__312\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__314\" style=\"color: rgb(62, 62, 62);font-weight: 400;margin: 10px 0%;justify-content: flex-start;display: flex;flex-flow: row;box-sizing: border-box;\"><section class=\"js_darkmode__315\" style=\"display: inline-block;vertical-align: top;width: auto;border-top: 1px solid rgb(0, 102, 89);border-top-left-radius: 0px;padding: 0px 0px 0px 10px;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;align-self: stretch;box-sizing: border-box;\"><section style=\"text-align: left;margin: 0px 0%;box-sizing: border-box;\"><section class=\"js_darkmode__316\" style=\"display: inline-block;min-width: 10%;max-width: 100%;vertical-align: top;transform: matrix(1, 0, -0.2, 1, 0, 0);-webkit-transform: matrix(1, 0, -0.2, 1, 0, 0);-moz-transform: matrix(1, 0, -0.2, 1, 0, 0);-o-transform: matrix(1, 0, -0.2, 1, 0, 0);border-style: none none none solid;border-width: 1px 5px 1px 0px;border-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0) rgb(92, 107, 192) rgb(223, 46, 0);padding: 5px 10px;background-color: rgb(0, 102, 89);box-shadow: rgba(255, 255, 255, 0) 0px 0px 0px;line-height: 1;letter-spacing: 0px;width: auto;height: auto;box-sizing: border-box;\"><section style=\"color: rgb(255, 255, 255);text-align: justify;font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">02</span></p></section></section></section></section><section class=\"js_darkmode__317\" style=\"display: inline-block;vertical-align: top;width: auto;min-width: 10%;max-width: 100%;flex: 0 0 auto;height: auto;border-style: solid;border-width: 1px 0px 0px;border-top-color: rgb(0, 102, 89);padding: 0px 20px 0px 10px;box-shadow: rgb(0, 0, 0) 0px 0px 0px 0px;align-self: flex-start;box-sizing: border-box;\"><section class=\"js_darkmode__318\" style=\"color: rgb(0, 102, 89);font-size: 17px;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><b style=\"box-sizing: border-box;\"><i style=\"box-sizing: border-box;\"><span leaf=\"\">案例2</span></i></b></p></section></section></section><section class=\"js_darkmode__320\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__321\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__323\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__324\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__325\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">最终结果也很不错：</span></p></section><section class=\"js_darkmode__326\" style=\"color: rgb(62, 62, 62);font-weight: 400;text-align: center;margin-top: 10px;margin-bottom: 10px;line-height: 0;box-sizing: border-box;\"><section class=\"js_darkmode__327\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;width: 85%;height: auto;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;box-sizing: border-box;\"></section></section><p class=\"js_darkmode__329\" style=\"color: rgb(62, 62, 62);font-weight: 400;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><span leaf=\"\">以上就是我的分享，希望对大家有帮助。</span></p><p class=\"js_darkmode__331\" style=\"color: rgb(62, 62, 62);font-weight: 400;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__332\" style=\"font-size: 14px;color: rgb(189, 189, 189);box-sizing: border-box;\"><span leaf=\"\">作者</span></span></strong><span class=\"js_darkmode__333\" style=\"font-size: 14px;color: rgb(189, 189, 189);box-sizing: border-box;\"><span leaf=\"\"> |</span></span><span class=\"js_darkmode__335\" style=\"color: rgb(189, 189, 189);font-size: 14px;box-sizing: border-box;\"><span leaf=\"\">陈序员大康</span></span><span class=\"js_darkmode__337\" style=\"font-size: 14px;color: rgb(189, 189, 189);box-sizing: border-box;\"><span leaf=\"\">| </span><strong style=\"box-sizing: border-box;\"><span leaf=\"\">排版</span></strong><span leaf=\"\"> | 鲈</span></span><span class=\"js_darkmode__338\" style=\"font-size: 14px;color: rgb(189, 189, 189);box-sizing: border-box;\"><span leaf=\"\">鱼</span></span></p><p class=\"js_darkmode__339\" style=\"color: rgb(62, 62, 62);font-weight: 400;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__340\" style=\"font-size: 14px;color: rgb(189, 189, 189);box-sizing: border-box;\"><span leaf=\"\">主编</span></span></strong><span class=\"js_darkmode__341\" style=\"font-size: 14px;color: rgb(189, 189, 189);box-sizing: border-box;\"><span leaf=\"\"> | 七天 | </span><strong style=\"box-sizing: border-box;\"><span leaf=\"\">编辑</span></strong><span leaf=\"\"> | 贝拉 杰克逊</span></span></p><section class=\"js_darkmode__342\" style=\"color: rgb(62, 62, 62);font-weight: 400;line-height: 0;box-sizing: border-box;\"><section nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;box-sizing: border-box;\"></section></section><section class=\"js_darkmode__343\" style=\"color: rgb(62, 62, 62);font-weight: 400;box-sizing: border-box;max-width: 100%;\"><strong data-pm-slice=\"0 0 []\"><span class=\"js_darkmode__345\" style=\"color: rgb(0, 102, 89);\"><span leaf=\"\">我们为你准备了一份生财有术团队精选的AI项目</span></span></strong><span leaf=\"\">，涵盖了圈友的实战经验和方法论，希望你也能找到适合自己的项目。</span></section><section class=\"js_darkmode__346\" data-pm-slice=\"0 0 []\" style=\"letter-spacing: 1px;line-height: 2;padding: 0px 16px;box-sizing: border-box;font-style: normal;font-weight: 400;text-align: justify;font-size: 16px;color: rgb(62, 62, 62);\"><section class=\"js_darkmode__347\" data-pm-slice=\"0 0 []\" style=\"letter-spacing: 1px;line-height: 2;padding: 0px 16px;box-sizing: border-box;font-style: normal;font-weight: 400;text-align: justify;font-size: 16px;color: rgb(62, 62, 62);\"><section style=\"max-width: 100%;margin-top: 10px;margin-bottom: 10px;box-sizing: border-box;\"><section style=\"line-height: 0;text-align: center;box-sizing: border-box;max-width: 100%;\"><section class=\"js_darkmode__348\" nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;box-shadow: rgb(0, 0, 0) 0px 0px 5px 0px;width: 92%;height: auto;box-sizing: border-box;\"></section></section></section></section></section><section class=\"js_darkmode__349\" style=\"color: rgb(62, 62, 62);font-weight: 400;max-width: 100%;box-sizing: border-box;\"><section style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;max-width: 100%;\"><p style=\"white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;\"><strong style=\"box-sizing: border-box;\"><span class=\"js_darkmode__350\" style=\"color: rgb(0, 102, 89);box-sizing: border-box;\"><span style=\"box-sizing: border-box;\"><span class=\"js_darkmode__351\" style=\"color: rgb(40, 40, 40);box-sizing: border-box;\"><span leaf=\"\">扫描下方二维码，添加生财服务官微信，回复</span><strong class=\"js_darkmode__352\" data-pm-slice=\"0 0 []\" style=\"letter-spacing: 1px;line-height: 2;font-style: normal;text-align: justify;font-size: 16px;box-sizing: border-box;max-width: 100%;font-weight: bold;color: rgb(0, 102, 89);\"><span class=\"js_darkmode__353\" leaf=\"\" style=\"font-weight: bold;color: rgb(0, 102, 89);\">【AI项目666】</span></strong><span leaf=\"\">即可领取。</span></span></span></span></strong></p></section></section><section class=\"js_darkmode__354\" style=\"color: rgb(62, 62, 62);font-weight: 400;display: inline-block;max-width: 100%;box-sizing: border-box;\"><section style=\"line-height: 0;max-width: 100%;box-sizing: border-box;\"><section nodeleaf=\"\" style=\"max-width: 100%;vertical-align: middle;display: inline-block;line-height: 0;box-sizing: border-box;\"></section></section></section></section></div>", "content_text": "👆点击生财有术>点击右上角“···”>设为星标🌟当下AI工具层出不穷，写作、配图、排版、发布早已不再是专业内容团队的专属技能。传术师 @陈序员大康 利用扣子空间最新支持的MCP扩展机制，打造出一个只需一句话指令，就能完成“热点识别—AI写作—智能配图—文章排版—发布草稿箱”全流程的公众号自动化系统。今天他将毫无保留地拆解整个搭建流程：如何搭建各类工作流、如何发布成MCP扩展、如何在扣子空间里集成为可用工具。Part 1我的思考扣子空间又有了大动作。我前几天才说，扣子空间目前的MCP扩展还太少，而且无法自定义，没想到这一点这么快就被解决了。现在开始，可以通过扣子工作流平台，将工作流以及扣子开发平台上的所有插件，按需集成进自己的扣子空间调用。刚看到这个消息，我脑海里立刻浮现出许多想法。最先想到的，就是实现“一句话发布公众号”。比如说：“看一下今天的热点，帮我写一篇公众号，发布到公众号草稿箱。”其实，一键发布公众号这件事，不论是原来的工作流，还是RPA方式，我之前都实现过。01为什么要做成聊天触发的模式？因为我认为，工作流的模式更偏向稳定定制型。一个流程里该做什么，基本都是预先设定好的，非常适合处理那些已经跑通、可以批量执行的任务。但问题在于，它不够灵活。比如我之前做的国学文生成，如果想拓展到其他类型的内容，就得从头到尾替换和优化提示词，整个流程要重新设计。再比如，我写文章过程中临时需要加一个路程规划功能，还得回到工作流里添加地图插件，流程就会变得很繁琐。而MCP这种模式，属于即插即用的逻辑。我原本用的是国学古风配图工具，如果想改写成穿搭类文章，只需把MCP配图工具换成穿搭配图MCP，立刻就能用了，不需要重新制作一个完整的工作流。根据实际需求随时组装，非常灵活。02那是不是就不需要工作流了呢？在短期内，工作流仍然是最稳定、效率最高的内容产出方式。不管是Manus，还是现在的扣子空间，虽然功能强大，但在处理复杂任务时，常常会出现理解偏差，导致输出内容不可用。而工作流因为逻辑明确、节点清晰，稳定性要高得多。总结一下，一种更适合探索创新，另一种适合稳定生产。Part 2整体功能分析我预想的功能是这样的：希望未来只需要在扣子空间输入一句话指令，比如：“帮我看一下今天的热点，选取一条热点，进行公众号爆文的写作，并发布到公众号草稿箱。”或者：“帮我读取某某链接，进行公众号爆文仿写，并发布到公众号草稿箱。”这些任务都可以自动完成。虽然扣子空间本身自带搜索功能，但它的写作能力目前还是差强人意，AI味比较浓，不够实用。因此，我打算围绕这个目标，开发几个MCP扩展模块：❖1）公众号爆文写作工作流：用于爆文写作，方便后期根据自己需要进行内部调整❖2）公众号配图工作流：用于公众号配图，后期也可以针对调整❖3）公众号排版工作流：用于将文章内容，转化成HTML❖4）公众号发布工作流：支持文章与小绿书发布到草稿箱Part 3MCP扩展的实现与发布01创建一个应用目前扣子只支持将应用内的工作流发布成扩展，所以我选择新建一个应用来完成后面的操作。02搭建工作流进入应用之后，点击创建，分别创建以下四个工作流：本文主要探索实现一句话发布公众号，对于公众号爆文写作、配图、排版工作流，我都将简化，等流程跑通后，再进行调优。1、公众号爆文写作工作流搭建文章创作的提示词如下，这个是简单一些的版本的。如果要保证文章质量，还需要做一些优化才行，大家看一下原理就好：# 角色你是一位经验极其丰富的公众号爆文写作专家，在公众号文章写作领域深耕多年，对各类风格、主题的文章创作都驾轻就熟，尤其精通爆款标题的创作精髓。你能够依据用户提出的详细要求，创作出高质量且富有吸引力的文章内容，并以特定的 JSON 格式精准输出。同时，若用户提供链接，你能凭借专业能力精准读取链接内容并据此进行精妙仿写。## 技能### 技能 1: 创作公众号文章1. 认真倾听并详细梳理用户输入的要求，全面涵盖文章主题、期望风格（如幽默风趣、严肃专业、温情细腻等）、目标受众（年龄范围、兴趣爱好、职业特征等）、特定情节或观点等关键信息。2. 充分运用你深厚的专业知识和丰富的实战经验，精心创作出极具吸引力的文章标题，标题要能精准抓住目标受众的眼球，激发他们的阅读兴趣。围绕主题构建丰富、详实且逻辑连贯的文章内容，内容要深入挖掘主题，运用恰当的案例、数据、故事等进行支撑，使文章具有可读性和说服力，且正文不能少于 800 字。同时，提炼出简洁而精准的内容摘要，概括文章核心要点。3. 将创作成果整理成 JSON 格式输出，即：{\"title\":\"文章标题\",\"content\":\"文章内容\",\"desc\":\"内容摘要，不超过 100 个字符\"}### 技能 2: 基于链接仿写文章1. 当用户提供链接时，运用专业工具准确读取链接内容。2. 深入且细致地分析链接中的文章结构（如总分总、层层递进、并列结构等）、写作风格（语言特色、修辞手法等）、主题内容（核心观点、主要情节等）以及受众定位等关键要素。3. 依据上述全面的分析，结合用户可能提出的其他个性化要求，进行符合公众号爆文风格的仿写。仿写内容要在借鉴原文精华的基础上有所创新，正文同样不能少于 800 字，并以 JSON 格式输出：{\"title\":\"仿写文章标题\",\"content\":\"仿写文章内容\",\"desc\":\"仿写内容摘要，不超过 100 个字符\"}## 限制:- 只围绕公众号文章创作相关内容进行回复，坚决拒绝回答与该主题无关的话题。- 输出必须严格按照 JSON 格式进行组织，不能有任何偏离要求的情况。- 内容摘要部分不能超过 100 字。 2、公众号排版工作流搭建公众号排版需要使用HTML，因此我选择使用代码能力比较强的DeepseekV3-0324：相关提示词如下：# AI生成微信公众号排版HTML的提示词## 角色设定你是一名专业且富有创意的微信公众号排版工程师，精通微信HTML内容安全策略和移动端适配规范。能够根据用户提供的文章内容与排版要求，自动设计出合适的排版风格，并生成符合要求的微信公众号排版HTML代码，以json格式直接输出，格式为{\"html\": \"最终排版的html代码\"}。## 核心要求### 1. 文档结构规范- **容器标签**：    ✅ 必须使用`<section>`作为主容器，禁止使用`<div>`    ✅ 多层级结构根据文章内容逻辑和排版要求合理构建，例如：<section style=\"外层样式\"><section style=\"内容块样式1\"><p>具体内容1</p></section><section style=\"内容块样式2\"><相关内容标签如图片、列表等></section></section>- **代码范围**：    ⛔ 禁止出现`<!DOCTYPE>`、`<html>`、`<head>`、`<body>`    ✅ 直接输出`<section>`内容片段### 2. 样式编写规则- **内联样式强制**：    ✅ 所有样式必须写在`style`属性中，根据文章风格和排版要求设计样式，格式例如：<p style=\"font-size: 16px; color: #333; line-height: 1.75; font-family: Arial, sans-serif;\">文本</p>- **移动端适配**：    ✅ 容器宽度：`max-width: 100%`    ✅ 图片宽度：`width: 100%`或根据排版要求设定百分比（如`width: 49%`）    ⚠️ 禁止使用`px`固定宽度（除边框等特殊场景）### 3. 排版风格设计- 根据文章内容的主题、情感基调等，自动选择匹配的排版风格，如简约风、活泼风、文艺风等。- 合理运用颜色搭配，确保整体视觉效果舒适、协调，符合文章氛围。## 禁止事项清单1. **标签黑名单**：     `<script>`, `<iframe>`, `<form>`, `<input>`, `<style>`2. **属性黑名单**：     `onclick`, `onload`, `class`, `id`3. **样式黑名单**：     `position: fixed; /* 微信浏览器不支持 */`   `background-image: url(); /* 部分机型失效 */`   `::before/::after /* 必须用真实DOM元素替代 */`## 输出验证流程1. **结构检查**：     - 是否存在`<section>`嵌套层级超过3层     - 图片是否使用`data-src`而非`src`2. **样式检查**：     # 伪代码示例   if \"px\" in styles and \"font-size\" not in styles:raise Error(\"除字号外禁止使用px单位\")3. **排版风格检查**：   - 排版风格是否与文章内容和用户要求相匹配。   - 整体视觉效果是否符合移动端阅读习惯。## 调用示例**用户输入**：  文章内容：“介绍一款新手机的功能和优点”，排版要求：简约现代风，主题色为#007BFF**AI输出**：  {\"html\": \"<section style=\\\"max-width: 100%; margin: 0 auto; background-color: #f8f9fa;\\\"> <!-- 标题 --><section style=\\\"margin-bottom: 20px; text-align: center;\\\"><h2 style=\\\"font-size: 24px; font-weight: 700; color: #007BFF;\\\">一款新手机的功能与优点</h2></section> <!-- 内容区 --><section style=\\\"padding: 20px;\\\"><p style=\\\"font-size: 16px; line-height: 1.6; color: #333;\\\">这款新手机具有众多令人瞩目的功能......</p></section></section>\"}## 限制:- 仅围绕微信公众号排版相关内容进行处理和输出，拒绝回答与排版无关的话题。- 所输出的内容必须按照规定的json格式进行组织，不能偏离框架要求。- 生成的排版必须符合移动端适配规范和内容安全策略。 代码模块的源码如下：import jsonimport reasync def main(args: Args) -> Output:    params = args.params    strhtml = params['input']    # 处理可能的markdown代码块    if \"```\" in strhtml:        # 使用正则提取json内容        pattern = r\"```(?:json)?(.*?)```\"        match = re.search(pattern, strhtml, re.DOTALL)        if match:            strhtml = match.group(1).strip()    try:        # 解析JSON        json_data = json.loads(strhtml)        html = json_data.get('html', '')    except json.JSONDecodeError:        # 如果JSON解析失败，返回空字符串        html = ''    # 构建输出对象    ret: Output = {        \"html\": html,    }    return ret3、公众号发布工作流搭建公众号发布，是我们跑通整个闭环中最关键的一环。我一直觉得，大多数人其实是没有自己的服务器的，自己去对接公众号接口也不太方便。所以我会用现有的插件，来教大家如何搭建公众号发布扩展。当然，我自己平时还是习惯用自己开发的插件，毕竟数据不经手别人，心里更踏实一些。如果你们的内容本身没什么敏感信息，用通用插件也完全没问题。1.查找一下现成的公众号api插件进入到公众号发布工作流，点击添加节点，选择探索更多：输入“公众号发布”，然后选择“微信公众号api”插件：接着找到以下3个插件，添加到工作流中：3个插件的作用说明如下：❖1）get_access_token：这个是公众号的授权接口，必须用公众号的appid和appsecret，获取授权后续接口才能用❖2）add_material：添加永久素材接口，这个是用来上传封面图片的，发布的时候需要用到上传成功的素材❖3）add_draft： 添加文章内容到草稿箱2.定义输入参数接下来我们开始连接节点，首先，我们需要设计一下输入参数。注意——一定要点开右边的按钮，打开详细描述，把每个参数的描述说清楚，这样便于后面大模型调用MCP时更准确地理解到底应该怎么用。3.串联全部公众号插件关于公众号的配置，这部分我们就先手动写死在节点里。目前来看，扣子的MCP机制，似乎还不支持动态配置参数，所以暂时只能采用固定写法。每个节点的参数如下：- 左右滑动 查看更多 -4. 查看appid、appsecret、设置白名单IP使用公众号的必要条件：appid、appsecret。这两个参数前面的工作流都有用到。另外就是IP白名单的设置，必须设置了才能通过别人的插件发布。可以登录公众号以后，找到以下这个位置，就能获取和进行配置了：白名单设置为：106.15.251.854、公众号配图工作流搭建扣子更新之后，通用pro模型使用的是即梦3.0，效果相当不错的，我把它也集成进来。注意：微信里不能引用外部的图片，我们需要使用公众号的上传插件，先把配图上传到公众号，然后使用公众号返回的图片链接。跟之前一样，找到公众号api插件，然后找到以下两个节点，添加到工作流中：因为参数都很简单，基本就是前面的输出是后面的参数。get_access_token 跟发布工作流一样，使用你自己的公众号配置。最后所有用到的节点如下：03测试工作流记得将每个工作流都跑一遍，确认没问题了再发布。04发布到MCP扩展测试完毕后，接下来进行发布——找到右上角的“发布”按钮点击，然后点“去发布”：进入发布页面之后，选择MCP服务，会自动定位到页面最后，可以看到有个“扣子空间扩展库”，我们点进去：把刚才开发的工作流全部选上，然后点击“确认”：然后点击右上角的发布，可以看到以下步骤，等待发布完成。Part 4扣子空间集成MCP扩展MCP扩展做好了，接下来，我们到扣子空间引用。首先切换到扣子空间系统，按如下指示操作：01进入扣子空间然后点击“快速开始”，进入工作区：02创建任务并配置MCP点击“新任务”，接着找到扩展，点击：接着选择自定义，找到我们新建的应用添加：到此，我们的MCP工具就配置完成了。Part 5测试效果01案例1最后，我们来做个简单测试——让它根据今天的热点，写一篇文章。第一遍尝试还挺顺利，工具都成功调用了。这是生成的封面加标题和摘要结果，排版还挺好看：02案例2最终结果也很不错：以上就是我的分享，希望对大家有帮助。作者 |陈序员大康| 排版 | 鲈鱼主编 | 七天 | 编辑 | 贝拉 杰克逊我们为你准备了一份生财有术团队精选的AI项目，涵盖了圈友的实战经验和方法论，希望你也能找到适合自己的项目。扫描下方二维码，添加生财服务官微信，回复【AI项目666】即可领取。", "images": [], "word_count": 6081, "url": "https://mp.weixin.qq.com/s/6ONbB8JOrlySKO1MOfQs6A", "site_type": "微信公众号", "extracted_at": "2025-07-20T11:59:09.438840"}