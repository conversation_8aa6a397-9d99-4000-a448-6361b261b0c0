what
--
大脑在面对新信息时所执行的流程：大脑会根据已有的信息来构建一套预测[[框架]]，预测框架用于归纳和理解新信息，减少信息的冗余，如果预测框架预测的结果和新信息不相符，则根据新信息修改修正框架，如果相符则强化框架

![[预测性编码模型图.png]]

why
--
1. 当结果符合预期时，会得到正向的反馈且能更好的理解
	例如：人们更喜欢听故事，因为故事的逻辑性使人们能预测故事的发展，一直得到正向的反馈，听起来更舒服。
2. 能提升对信息的预测能并更好的理解信息

how
--

1. 始终在脑子里放着一个预测框架来对信息进行处理，如果符合预测则进入[[心流状态]]，不符合预测则进入[[唤醒状态]]，都能有效的提升阅读效率和理解信息
	例如：
		形成阅读习惯：接收目前读到的信息通过预测框架猜测新的内容，并对整体有个大致的框架
2. 提高别人对自己提供的信息的预测性：
	1.结论前置
	2.清晰的层次结构
