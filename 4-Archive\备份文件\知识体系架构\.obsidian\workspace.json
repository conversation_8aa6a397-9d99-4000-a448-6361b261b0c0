{"main": {"id": "90adde38e93f1525", "type": "split", "children": [{"id": "cbe3098a2ad3d0f8", "type": "tabs", "children": [{"id": "64e9ff817f07a00c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "项目/项目注意事项.md", "mode": "source", "source": false}}}, {"id": "08a64c880a5adacf", "type": "leaf", "state": {"type": "markdown", "state": {"file": "gpt指令/骂醒恋爱脑.md", "mode": "source", "source": false}}}, {"id": "99b84b6121c7e0db", "type": "leaf", "state": {"type": "release-notes", "state": {"currentVersion": "1.5.8"}}}, {"id": "1b45d5cb410bf6f6", "type": "leaf", "state": {"type": "markdown", "state": {"file": "项目/公众号爆文/情感类爆文/指令/情感类短文写作作家.md", "mode": "source", "source": false}}}, {"id": "9c17edefbc0bb96a", "type": "leaf", "state": {"type": "markdown", "state": {"file": "项目/公众号爆文/情感类爆文/指令/文章/法律案件作家.md", "mode": "source", "source": false}}}], "currentTab": 4}], "direction": "vertical"}, "left": {"id": "917718827b1c8bb7", "type": "split", "children": [{"id": "156fbe3aefdacb26", "type": "tabs", "children": [{"id": "8adca451ce32f11d", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabeticalReverse"}}}, {"id": "9dc02913f54bf1ec", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}}}, {"id": "f387e91a55df1dd3", "type": "leaf", "state": {"type": "bookmarks", "state": {}}}]}], "direction": "horizontal", "width": 428.5}, "right": {"id": "d5bc706cc697d1ec", "type": "split", "children": [{"id": "252d9799305beb71", "type": "tabs", "children": [{"id": "928da782bcd3cea2", "type": "leaf", "state": {"type": "backlink", "state": {"file": "项目/公众号爆文/情感类爆文/指令/文章/法律案件作家.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}}, {"id": "2b16904ac846dd6b", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "项目/公众号爆文/情感类爆文/指令/文章/法律案件作家.md", "linksCollapsed": false, "unlinkedCollapsed": true}}}, {"id": "01166a36adde6399", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true}}}, {"id": "96d4c5767097bd08", "type": "leaf", "state": {"type": "outline", "state": {"file": "项目/公众号爆文/情感类爆文/指令/文章/法律案件作家.md"}}}, {"id": "4515efb42fa6d527", "type": "leaf", "state": {"type": "calendar", "state": {}}}, {"id": "dee803cdcabeb96c", "type": "leaf", "state": {"type": "advanced-tables-toolbar", "state": {}}}, {"id": "94ecbadcb70b55f5", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}}}], "currentTab": 3}], "direction": "horizontal", "width": 200}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "templater-obsidian:Templater": false, "table-editor-obsidian:Advanced Tables Toolbar": false, "obsidian-weread-plugin:Weread": false}}, "active": "9c17edefbc0bb96a", "lastOpenFiles": ["项目/公众号爆文/情感类爆文/指令/文章/法律案件故事.md", "项目/公众号爆文/情感类爆文/指令/文章/法律案件作家.md", "项目/公众号爆文/情感类爆文/指令/情感故事作家-台风-GPT魔法师.md", "项目/公众号爆文/情感类爆文/指令/短篇小说作家.md", "项目/公众号爆文/情感类爆文/指令/风格.md", "项目/公众号爆文/情感类爆文/指令/情感类短文写作作家.md", "项目/公众号爆文/情感类爆文/指令/特定类型的故事的指令.md", "项目/公众号爆文/情感类爆文/指令/同款标题.md", "项目/公众号爆文/情感类爆文/指令/prompt打分.md", "项目/公众号爆文/情感类爆文/指令/文章/病房故事.md", "项目/公众号爆文/情感类爆文/指令/文章/悬念标题.md", "项目/公众号爆文/情感类爆文/指令/文章/社会新闻故事.md", "项目/公众号爆文/情感类爆文/指令/文章/养老金故事.md", "项目/公众号爆文/情感类爆文/指令/大纲.md", "项目/公众号爆文/情感类爆文/指令/单指令根据大纲完成故事.md", "小红书/小红书定位.md", "项目/平台/小红书/文章+课程拆解/决定小红书流量好坏的因素.md", "项目/平台/小红书/文章+课程拆解/场景化内容能让读者容易有代入感.md", "项目/平台/小红书/小红书账号人设标签.md", "项目/平台/小红书/小红书引流.md", "小红书", "gpt指令/分析文章.md", "gpt指令/骂醒恋爱脑.md", "gpt指令/病房情感故事作家.md", "gpt指令/职场邮件撰写专家​.md", "gpt指令/ChatGPT提示工程师.md", "gpt指令/文章模仿大师.md", "项目/平台/小红书/未命名", "项目/20231224养生带货", "学习/影刀rap", "学习", "项目/公众号爆文/情感类爆文/指令/文章", "gpt指令", "项目/20230826核废水", "项目/平台", "项目/公众号爆文/情感类爆文"]}