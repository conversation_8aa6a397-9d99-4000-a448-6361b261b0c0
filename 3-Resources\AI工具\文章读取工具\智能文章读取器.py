#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文章读取器 - 优化版
支持多种文章来源的自动读取和处理

功能特点:
1. 自动检测文章类型（微信公众号、知乎、其他）
2. 智能重试机制
3. 多格式输出（JSON、Markdown、Coze格式）
4. 缓存机制避免重复读取
5. 详细的日志记录

使用方法:
reader = SmartArticleReader()
article = reader.read_article("https://mp.weixin.qq.com/s/xxxxx")

作者: AI Assistant
创建时间: 2025-07-20
版本: 2.0
"""

import time
import random
import json
import re
import sys
import os
import hashlib
import logging
from datetime import datetime
from urllib.parse import urlparse
from pathlib import Path

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"❌ 缺少依赖库: {e}")
    print("请运行: pip install selenium beautifulsoup4 lxml")
    sys.exit(1)

class SmartArticleReader:
    """智能文章读取器"""
    
    def __init__(self, cache_dir="./article_cache", log_level=logging.INFO):
        """
        初始化读取器
        
        Args:
            cache_dir (str): 缓存目录
            log_level: 日志级别
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        self.driver = None
        self.setup_logging(log_level)
        self.setup_driver()
        
        # 支持的网站配置
        self.site_configs = {
            'mp.weixin.qq.com': {
                'name': '微信公众号',
                'content_selector': '#js_content',
                'title_selectors': ['#activity-name', 'h1', '.rich_media_title'],
                'author_selectors': ['#js_name', '.rich_media_meta_text', '.account_nickname'],
                'time_selectors': ['#publish_time', '.rich_media_meta_text'],
                'wait_element': '#js_content'
            },
            'zhuanlan.zhihu.com': {
                'name': '知乎专栏',
                'content_selector': '.RichText',
                'title_selectors': ['.Post-Title', 'h1'],
                'author_selectors': ['.AuthorInfo-name', '.UserLink-link'],
                'time_selectors': ['.ContentItem-time'],
                'wait_element': '.RichText'
            },
            'www.zhihu.com': {
                'name': '知乎',
                'content_selector': '.RichText',
                'title_selectors': ['.QuestionHeader-title', 'h1'],
                'author_selectors': ['.AuthorInfo-name'],
                'time_selectors': ['.ContentItem-time'],
                'wait_element': '.RichText'
            },
            'juejin.cn': {
                'name': '掘金',
                'content_selector': '.markdown-body',
                'title_selectors': ['.article-title', 'h1'],
                'author_selectors': ['.username', '.author-name'],
                'time_selectors': ['.time'],
                'wait_element': '.markdown-body'
            },
            'www.jianshu.com': {
                'name': '简书',
                'content_selector': '[data-note-content]',
                'title_selectors': ['._1RuRku', 'h1'],
                'author_selectors': ['.FxYr8x', '.author'],
                'time_selectors': ['.time'],
                'wait_element': '[data-note-content]'
            },
            'segmentfault.com': {
                'name': 'SegmentFault',
                'content_selector': '.article__content',
                'title_selectors': ['.article__title', 'h1'],
                'author_selectors': ['.article__author', '.author'],
                'time_selectors': ['.article__time'],
                'wait_element': '.article__content'
            },
            'www.cnblogs.com': {
                'name': '博客园',
                'content_selector': '#cnblogs_post_body',
                'title_selectors': ['.postTitle', 'h1'],
                'author_selectors': ['.postDesc', '.author'],
                'time_selectors': ['.postDesc'],
                'wait_element': '#cnblogs_post_body'
            },
            'blog.csdn.net': {
                'name': 'CSDN',
                'content_selector': '#content_views',
                'title_selectors': ['.title-article', 'h1'],
                'author_selectors': ['.follow-nickName', '.author'],
                'time_selectors': ['.time'],
                'wait_element': '#content_views'
            }
        }
    
    def setup_logging(self, level):
        """设置日志"""
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('article_reader.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_driver(self):
        """配置浏览器"""
        self.logger.info("🚀 正在启动浏览器...")
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 随机用户代理
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')
        
        try:
            # 尝试使用项目目录中的ChromeDriver
            chromedriver_path = "./chromedriver.exe"
            if os.path.exists(chromedriver_path):
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                self.logger.info("✅ 使用项目目录中的ChromeDriver")
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
                self.logger.info("✅ 使用系统PATH中的ChromeDriver")
            
            # 反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.logger.info("✅ 浏览器启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 浏览器启动失败: {e}")
            raise
    
    def get_cache_key(self, url):
        """生成缓存键"""
        return hashlib.md5(url.encode()).hexdigest()
    
    def get_cached_article(self, url):
        """获取缓存的文章"""
        cache_key = self.get_cache_key(url)
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                
                # 检查缓存是否过期（24小时）
                cached_time = datetime.fromisoformat(cached_data.get('extracted_at', ''))
                if (datetime.now() - cached_time).total_seconds() < 86400:
                    self.logger.info("📋 使用缓存的文章数据")
                    return cached_data
            except Exception as e:
                self.logger.warning(f"⚠️ 读取缓存失败: {e}")
        
        return None
    
    def cache_article(self, url, article_data):
        """缓存文章"""
        try:
            cache_key = self.get_cache_key(url)
            cache_file = self.cache_dir / f"{cache_key}.json"
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(article_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"💾 文章已缓存: {cache_file}")
        except Exception as e:
            self.logger.warning(f"⚠️ 缓存失败: {e}")
    
    def detect_site_type(self, url):
        """检测网站类型"""
        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # 精确匹配已知网站
        for site_domain, config in self.site_configs.items():
            if site_domain in domain:
                self.logger.info(f"🔍 检测到网站类型: {config['name']}")
                return config

        # 智能检测通用网站类型
        self.logger.info("🔍 使用智能通用配置")
        return self._create_universal_config(domain)

    def _create_universal_config(self, domain):
        """创建通用网站配置"""
        # 根据域名特征推测网站类型
        if any(keyword in domain for keyword in ['blog', 'post', 'article']):
            site_type = '博客网站'
        elif any(keyword in domain for keyword in ['news', 'media']):
            site_type = '新闻网站'
        elif any(keyword in domain for keyword in ['tech', 'dev']):
            site_type = '技术网站'
        else:
            site_type = '通用网站'

        return {
            'name': site_type,
            'content_selector': self._get_universal_content_selectors(),
            'title_selectors': self._get_universal_title_selectors(),
            'author_selectors': self._get_universal_author_selectors(),
            'time_selectors': self._get_universal_time_selectors(),
            'wait_element': 'body'
        }

    def _get_universal_content_selectors(self):
        """获取通用内容选择器"""
        return ', '.join([
            'article',                    # HTML5语义化标签
            'main',                       # 主要内容区域
            '.content',                   # 常见类名
            '.post-content',              # 博客内容
            '.article-content',           # 文章内容
            '.entry-content',             # 条目内容
            '.post-body',                 # 文章主体
            '.article-body',              # 文章主体
            '.markdown-body',             # Markdown内容
            '.rich-text',                 # 富文本
            '.text-content',              # 文本内容
            '[role="main"]',              # ARIA角色
            '#content',                   # ID选择器
            '#main-content',              # 主要内容ID
            '#article-content',           # 文章内容ID
            '.container .content',        # 容器内的内容
            '.wrapper .content'           # 包装器内的内容
        ])

    def _get_universal_title_selectors(self):
        """获取通用标题选择器"""
        return [
            'h1',                         # 最常见的标题标签
            '.title',                     # 通用标题类
            '.post-title',                # 文章标题
            '.article-title',             # 文章标题
            '.entry-title',               # 条目标题
            '.page-title',                # 页面标题
            '.headline',                  # 标题
            '.header h1',                 # 头部的h1
            'header h1',                  # 头部h1
            '.content h1',                # 内容区的h1
            'title',                      # 页面title标签
            '[data-title]',               # 数据属性
            '.main-title',                # 主标题
            '#title'                      # ID选择器
        ]

    def _get_universal_author_selectors(self):
        """获取通用作者选择器"""
        return [
            '.author',                    # 通用作者类
            '.byline',                    # 署名行
            '.post-author',               # 文章作者
            '.article-author',            # 文章作者
            '.writer',                    # 作者
            '.creator',                   # 创建者
            '.username',                  # 用户名
            '.user-name',                 # 用户名
            '.author-name',               # 作者名
            '.by-author',                 # 作者信息
            '[rel="author"]',             # 关系属性
            '[data-author]',              # 数据属性
            '.meta .author',              # 元信息中的作者
            '.post-meta .author'          # 文章元信息中的作者
        ]

    def _get_universal_time_selectors(self):
        """获取通用时间选择器"""
        return [
            '.time',                      # 通用时间类
            '.date',                      # 日期
            '.publish-time',              # 发布时间
            '.post-date',                 # 文章日期
            '.article-date',              # 文章日期
            '.created-time',              # 创建时间
            '.updated-time',              # 更新时间
            '.timestamp',                 # 时间戳
            '[datetime]',                 # datetime属性
            '[data-time]',                # 数据属性
            '[data-date]',                # 数据属性
            'time',                       # time标签
            '.meta .time',                # 元信息中的时间
            '.post-meta .time'            # 文章元信息中的时间
        ]
    
    def read_article(self, url, force_refresh=False):
        """
        读取文章
        
        Args:
            url (str): 文章URL
            force_refresh (bool): 是否强制刷新，忽略缓存
            
        Returns:
            dict: 文章数据
        """
        self.logger.info(f"📖 开始读取文章: {url}")
        
        # 检查缓存
        if not force_refresh:
            cached_article = self.get_cached_article(url)
            if cached_article:
                return cached_article
        
        # 检测网站类型
        site_config = self.detect_site_type(url)
        
        # 读取文章
        article_data = self._read_article_with_retry(url, site_config)
        
        if article_data:
            # 缓存文章
            self.cache_article(url, article_data)
            
            # 生成多种格式
            self._generate_output_files(article_data)
            
            self.logger.info(f"✅ 文章读取成功: {article_data['title']}")
            return article_data
        else:
            self.logger.error("❌ 文章读取失败")
            return None
    
    def _read_article_with_retry(self, url, site_config, max_retries=3):
        """带重试的文章读取"""
        for attempt in range(max_retries):
            try:
                self.logger.info(f"🔄 第 {attempt + 1} 次尝试读取...")
                
                # 访问页面
                self.driver.get(url)
                
                # 等待页面加载
                self._wait_for_page_load(site_config)
                
                # 检查验证
                if self._check_verification():
                    self.logger.warning("⚠️ 遇到验证页面，等待处理...")
                    time.sleep(5)
                
                # 模拟阅读
                self._simulate_reading()
                
                # 提取内容
                article_data = self._extract_content(url, site_config)
                
                if article_data and article_data.get('title'):
                    return article_data
                
            except Exception as e:
                self.logger.warning(f"⚠️ 第 {attempt + 1} 次尝试失败: {e}")
                if attempt < max_retries - 1:
                    wait_time = random.uniform(3, 8)
                    self.logger.info(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                    time.sleep(wait_time)
        
        return None
    
    def _wait_for_page_load(self, site_config):
        """等待页面加载"""
        try:
            wait_element = site_config.get('wait_element', 'body')
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, wait_element))
            )
            self.logger.info("✅ 页面内容加载完成")
        except Exception as e:
            self.logger.warning(f"⚠️ 等待页面加载超时: {e}")
    
    def _check_verification(self):
        """检查验证页面"""
        try:
            page_text = self.driver.page_source
            verification_keywords = ["环境异常", "完成验证", "安全验证", "verification", "captcha"]
            
            for keyword in verification_keywords:
                if keyword in page_text:
                    return True
            return False
        except:
            return False
    
    def _simulate_reading(self):
        """模拟阅读行为"""
        try:
            # 随机等待
            time.sleep(random.uniform(2, 4))
            
            # 获取页面高度
            page_height = self.driver.execute_script("return document.body.scrollHeight")
            viewport_height = self.driver.execute_script("return window.innerHeight")
            
            # 模拟滚动
            current_position = 0
            while current_position < page_height - viewport_height:
                scroll_distance = random.randint(200, 400)
                current_position += scroll_distance
                
                self.driver.execute_script(f"window.scrollTo(0, {current_position});")
                time.sleep(random.uniform(0.5, 1.5))
            
            # 回到顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)
            
        except Exception as e:
            self.logger.warning(f"⚠️ 模拟阅读出错: {e}")
    
    def _extract_content(self, url, site_config):
        """提取内容"""
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # 提取各项信息
            title = self._extract_by_selectors(soup, site_config['title_selectors'], "标题")
            author = self._extract_by_selectors(soup, site_config['author_selectors'], "作者")
            publish_time = self._extract_by_selectors(soup, site_config['time_selectors'], "发布时间")
            
            # 提取正文
            content_data = self._extract_main_content(soup, site_config)
            if not content_data:
                return None
            
            # 提取图片
            images = self._extract_images(soup, site_config)
            
            # 计算字数
            word_count = len(re.sub(r'\s', '', content_data['text']))
            
            return {
                'title': title,
                'author': author,
                'publish_time': publish_time,
                'content_html': content_data['html'],
                'content_text': content_data['text'],
                'images': images,
                'word_count': word_count,
                'url': url,
                'site_type': site_config['name'],
                'extracted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 内容提取失败: {e}")
            return None
    
    def _extract_by_selectors(self, soup, selectors, item_name):
        """通过选择器提取内容"""
        for selector in selectors:
            try:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text().strip()
                    # 根据不同类型调整长度限制
                    max_length = 500 if item_name == "标题" else 200
                    if text and len(text) < max_length:
                        self.logger.debug(f"✅ 找到{item_name}: {text[:50]}...")
                        return text
            except Exception as e:
                self.logger.debug(f"⚠️ 选择器 {selector} 失败: {e}")

        # 如果常规选择器失败，尝试智能提取
        if item_name == "标题":
            smart_title = self._smart_title_extraction(soup)
            if smart_title:
                return smart_title

        self.logger.warning(f"⚠️ 未找到{item_name}")
        return f"未知{item_name}"

    def _smart_title_extraction(self, soup):
        """智能标题提取"""
        try:
            # 方法1: 页面title标签
            title_tag = soup.find('title')
            if title_tag:
                title_text = title_tag.get_text().strip()
                # 清理常见的网站后缀
                title_text = re.sub(r'\s*[-_|]\s*.*$', '', title_text)
                if title_text and 5 < len(title_text) < 200:
                    self.logger.info(f"✅ 从title标签提取标题: {title_text[:50]}...")
                    return title_text

            # 方法2: 寻找最大的标题标签
            for tag_name in ['h1', 'h2', 'h3']:
                headers = soup.find_all(tag_name)
                for header in headers:
                    text = header.get_text().strip()
                    if text and 5 < len(text) < 200:
                        # 检查是否在主要内容区域
                        if self._is_in_main_content(header, soup):
                            self.logger.info(f"✅ 从{tag_name}标签提取标题: {text[:50]}...")
                            return text

            # 方法3: 寻找最长的粗体文本（可能是标题）
            bold_tags = soup.find_all(['strong', 'b'])
            for bold in bold_tags:
                text = bold.get_text().strip()
                if text and 10 < len(text) < 100:
                    # 检查是否在页面顶部
                    if self._is_likely_title(bold, text):
                        self.logger.info(f"✅ 从粗体文本提取标题: {text[:50]}...")
                        return text

            return None

        except Exception as e:
            self.logger.debug(f"⚠️ 智能标题提取失败: {e}")
            return None

    def _is_in_main_content(self, element, soup=None):
        """判断元素是否在主要内容区域"""
        # 检查元素的父级是否包含内容相关的类名或ID
        parent = element.parent
        while parent and parent.name != 'body':
            if parent.name in ['article', 'main']:
                return True

            class_names = ' '.join(parent.get('class', [])).lower()
            element_id = parent.get('id', '').lower()

            content_keywords = ['content', 'article', 'post', 'main']
            if any(keyword in class_names or keyword in element_id for keyword in content_keywords):
                return True

            parent = parent.parent

        return False

    def _is_likely_title(self, element, text):
        """判断是否可能是标题"""
        # 检查文本特征
        if ':' in text or '：' in text:  # 标题通常不包含冒号
            return False

        # 检查位置（应该在页面前部）
        all_text = element.find_parent('body').get_text() if element.find_parent('body') else ""
        if all_text:
            position = all_text.find(text)
            if position > len(all_text) * 0.3:  # 如果在页面后30%，不太可能是标题
                return False

        return True
    
    def _extract_main_content(self, soup, site_config):
        """提取主要内容"""
        content_selector = site_config['content_selector']

        # 尝试多个选择器
        selectors = content_selector.split(', ') if ', ' in content_selector else [content_selector]

        for selector in selectors:
            try:
                element = soup.select_one(selector)
                if element:
                    # 清理元素
                    self._clean_element(element)

                    content_html = str(element)
                    content_text = element.get_text().strip()

                    if content_text and len(content_text) > 100:
                        self.logger.info(f"✅ 找到正文内容，长度: {len(content_text)} 字符")
                        return {
                            'html': content_html,
                            'text': content_text
                        }
            except Exception as e:
                self.logger.debug(f"⚠️ 内容选择器 {selector} 失败: {e}")

        # 如果常规选择器都失败，尝试智能内容提取
        self.logger.info("🤖 尝试智能内容提取...")
        return self._smart_content_extraction(soup)

    def _smart_content_extraction(self, soup):
        """智能内容提取"""
        try:
            # 方法1: 寻找最长的文本块
            candidates = []

            # 查找可能的内容容器
            potential_containers = soup.find_all(['div', 'section', 'article', 'main'])

            for container in potential_containers:
                # 跳过明显的非内容区域
                if self._is_non_content_area(container):
                    continue

                text = container.get_text().strip()
                if len(text) > 200:  # 至少200字符
                    candidates.append({
                        'element': container,
                        'text': text,
                        'length': len(text),
                        'score': self._calculate_content_score(container, text)
                    })

            if candidates:
                # 按评分排序，选择最佳候选
                candidates.sort(key=lambda x: x['score'], reverse=True)
                best_candidate = candidates[0]

                if best_candidate['length'] > 100:
                    self.logger.info(f"✅ 智能提取成功，长度: {best_candidate['length']} 字符")

                    # 清理元素
                    self._clean_element(best_candidate['element'])

                    return {
                        'html': str(best_candidate['element']),
                        'text': best_candidate['text']
                    }

            # 方法2: 如果还是没找到，尝试提取所有段落
            paragraphs = soup.find_all('p')
            if paragraphs:
                combined_text = '\n\n'.join([p.get_text().strip() for p in paragraphs if p.get_text().strip()])
                if len(combined_text) > 100:
                    self.logger.info(f"✅ 段落组合提取成功，长度: {len(combined_text)} 字符")
                    return {
                        'html': '\n'.join([str(p) for p in paragraphs]),
                        'text': combined_text
                    }

            self.logger.error("❌ 智能提取也失败了")
            return None

        except Exception as e:
            self.logger.error(f"❌ 智能内容提取出错: {e}")
            return None

    def _is_non_content_area(self, element):
        """判断是否为非内容区域"""
        if not element:
            return True

        # 检查类名和ID
        class_names = ' '.join(element.get('class', [])).lower()
        element_id = element.get('id', '').lower()

        # 排除的关键词
        exclude_keywords = [
            'nav', 'menu', 'sidebar', 'footer', 'header',
            'ad', 'advertisement', 'banner', 'popup',
            'comment', 'share', 'social', 'related',
            'recommend', 'tag', 'category', 'meta',
            'breadcrumb', 'pagination', 'toolbar'
        ]

        for keyword in exclude_keywords:
            if keyword in class_names or keyword in element_id:
                return True

        return False

    def _calculate_content_score(self, element, text):
        """计算内容评分"""
        score = 0

        # 基础分数：文本长度
        score += len(text) * 0.1

        # 段落数量加分
        paragraphs = element.find_all('p')
        score += len(paragraphs) * 10

        # 包含常见内容标签加分
        content_tags = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'blockquote']
        for tag in content_tags:
            score += len(element.find_all(tag)) * 5

        # 类名和ID加分
        class_names = ' '.join(element.get('class', [])).lower()
        element_id = element.get('id', '').lower()

        content_keywords = ['content', 'article', 'post', 'main', 'body', 'text']
        for keyword in content_keywords:
            if keyword in class_names or keyword in element_id:
                score += 50

        # 减分项：包含非内容元素
        non_content_tags = ['script', 'style', 'nav', 'aside', 'footer']
        for tag in non_content_tags:
            score -= len(element.find_all(tag)) * 20

        return score
    
    def _clean_element(self, element):
        """清理元素"""
        if not element:
            return

        # 移除脚本和样式
        for tag in element.find_all(['script', 'style', 'noscript']):
            tag.decompose()

        # 移除广告和推广内容
        ad_selectors = [
            '[class*="ad"]', '[id*="ad"]',
            '[class*="advertisement"]', '[id*="advertisement"]',
            '[class*="banner"]', '[id*="banner"]',
            '[class*="promo"]', '[id*="promo"]',
            '.ad', '.ads', '.advertisement', '.banner',
            '.google-ad', '.adsense', '.adsbygoogle'
        ]

        for selector in ad_selectors:
            try:
                for unwanted in element.select(selector):
                    unwanted.decompose()
            except:
                continue

        # 移除社交分享和评论
        social_selectors = [
            '[class*="share"]', '[id*="share"]',
            '[class*="social"]', '[id*="social"]',
            '[class*="comment"]', '[id*="comment"]',
            '[class*="discuss"]', '[id*="discuss"]',
            '.share', '.social', '.comment', '.comments',
            '.share-button', '.social-share', '.comment-box'
        ]

        for selector in social_selectors:
            try:
                for unwanted in element.select(selector):
                    unwanted.decompose()
            except:
                continue

        # 移除导航和侧边栏
        nav_selectors = [
            'nav', 'aside', 'footer', 'header',
            '[class*="nav"]', '[id*="nav"]',
            '[class*="sidebar"]', '[id*="sidebar"]',
            '[class*="menu"]', '[id*="menu"]',
            '.navigation', '.sidebar', '.menu'
        ]

        for selector in nav_selectors:
            try:
                for unwanted in element.select(selector):
                    unwanted.decompose()
            except:
                continue

        # 移除相关推荐和标签
        related_selectors = [
            '[class*="related"]', '[id*="related"]',
            '[class*="recommend"]', '[id*="recommend"]',
            '[class*="tag"]', '[id*="tag"]',
            '[class*="category"]', '[id*="category"]',
            '.related', '.recommend', '.tags', '.categories'
        ]

        for selector in related_selectors:
            try:
                for unwanted in element.select(selector):
                    unwanted.decompose()
            except:
                continue

        # 移除空的元素
        for tag in element.find_all():
            if not tag.get_text().strip() and not tag.find('img'):
                tag.decompose()
    
    def _extract_images(self, soup, site_config):
        """提取图片"""
        images = []

        # 在内容区域查找图片
        content_element = soup.select_one(site_config['content_selector']) or soup.find('body')
        if content_element:
            for img in content_element.find_all('img'):
                # 尝试多种图片源属性
                src = (img.get('data-src') or
                      img.get('src') or
                      img.get('data-original') or
                      img.get('data-lazy-src') or
                      img.get('data-url') or
                      img.get('data-img-src'))

                if src:
                    # 处理相对路径
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        # 需要网站的基础URL，这里先跳过相对路径
                        continue
                    elif src.startswith('http'):
                        # 过滤掉明显的非内容图片
                        if self._is_content_image(src, img):
                            images.append(src)

        # 去重
        images = list(dict.fromkeys(images))

        self.logger.info(f"🖼️ 找到 {len(images)} 张图片")
        return images

    def _is_content_image(self, src, img_element):
        """判断是否为内容图片"""
        # 排除明显的非内容图片
        exclude_patterns = [
            'avatar', 'logo', 'icon', 'button', 'banner',
            'ad', 'advertisement', 'sponsor', 'promo',
            'share', 'social', 'qr', 'qrcode',
            'pixel', 'tracking', 'analytics'
        ]

        src_lower = src.lower()
        for pattern in exclude_patterns:
            if pattern in src_lower:
                return False

        # 检查图片尺寸属性（如果有的话）
        width = img_element.get('width')
        height = img_element.get('height')

        if width and height:
            try:
                w, h = int(width), int(height)
                # 排除太小的图片（可能是图标或像素图）
                if w < 50 or h < 50:
                    return False
                # 排除明显的广告尺寸
                if (w == 728 and h == 90) or (w == 300 and h == 250):
                    return False
            except ValueError:
                pass

        # 检查alt属性
        alt = img_element.get('alt', '').lower()
        for pattern in exclude_patterns:
            if pattern in alt:
                return False

        return True
    
    def _generate_output_files(self, article_data):
        """生成多种格式的输出文件"""
        try:
            # 创建输出目录
            output_dir = Path("./读取文章存档")
            output_dir.mkdir(exist_ok=True)

            timestamp = int(time.time())
            safe_title = re.sub(r'[^\w\s-]', '', article_data['title'])[:30]

            # JSON格式
            json_filename = output_dir / f"article_{safe_title}_{timestamp}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(article_data, f, ensure_ascii=False, indent=2)

            # Markdown格式
            md_filename = output_dir / f"article_{safe_title}_{timestamp}.md"
            markdown_content = self._generate_markdown(article_data)
            with open(md_filename, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            # Coze格式
            coze_filename = output_dir / f"coze_input_{safe_title}_{timestamp}.md"
            coze_content = self._generate_coze_input(article_data)
            with open(coze_filename, 'w', encoding='utf-8') as f:
                f.write(coze_content)

            self.logger.info(f"📁 已生成输出文件到 {output_dir}: {json_filename.name}, {md_filename.name}, {coze_filename.name}")

        except Exception as e:
            self.logger.warning(f"⚠️ 生成输出文件失败: {e}")
    
    def _generate_markdown(self, article_data):
        """生成Markdown格式"""
        lines = [
            f"# {article_data['title']}",
            "",
            f"**作者**: {article_data['author']}",
            f"**来源**: {article_data['site_type']}",
            f"**发布时间**: {article_data['publish_time']}",
            f"**字数**: {article_data['word_count']}",
            f"**原文链接**: {article_data['url']}",
            "",
            "## 正文内容",
            "",
            article_data['content_text'],
            ""
        ]
        
        if article_data['images']:
            lines.extend([
                "## 文章图片",
                ""
            ])
            for i, img_url in enumerate(article_data['images'], 1):
                lines.append(f"![图片{i}]({img_url})")
            lines.append("")
        
        lines.extend([
            "---",
            f"*提取时间: {article_data['extracted_at']}*",
            f"*提取工具: 智能文章读取器*"
        ])
        
        return '\n'.join(lines)
    
    def _generate_coze_input(self, article_data):
        """生成Coze智能体输入格式"""
        return f"""# 智能体处理指令

## 任务说明
请基于以下文章内容，按照我们的创作要求进行重新创作。

## 原文信息
- **标题**: {article_data['title']}
- **作者**: {article_data['author']}
- **来源**: {article_data['site_type']}
- **字数**: {article_data['word_count']}字
- **原文链接**: {article_data['url']}

## 原文内容

{article_data['content_text']}

## 创作要求
1. 保持原文核心信息和观点
2. 优化文章结构和逻辑流程
3. 提升语言表达的吸引力
4. 添加适当的情感色彩
5. 确保内容符合目标受众需求
6. 重点突出实用性和可操作性

## 输出格式
请按以下结构输出：
- **新标题**：[重新设计的吸引人标题]
- **引言**：[200字左右的引言段落]
- **主体内容**：[重新组织的文章主体，保持原文信息完整性]
- **结尾**：[总结性段落，呼应主题]

请开始处理。"""
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            self.logger.info("🔒 浏览器已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
        # 不抑制异常，让调用者处理
        return False

# 便捷函数
def read_article_quick(url, force_refresh=False):
    """快速读取文章的便捷函数"""
    with SmartArticleReader() as reader:
        return reader.read_article(url, force_refresh)

def test_multiple_sites():
    """测试多个网站的读取能力"""
    test_urls = [
        "https://mp.weixin.qq.com/s/1Y-ef9ti-sVqmF5sV8LVMw",  # 微信公众号
        # 可以添加更多测试URL
    ]

    print("🧪 开始多站点读取测试...")

    with SmartArticleReader() as reader:
        for i, url in enumerate(test_urls, 1):
            print(f"\n� 测试 {i}/{len(test_urls)}: {url}")

            try:
                article = reader.read_article(url)

                if article:
                    print(f"✅ 成功: {article['title'][:50]}...")
                    print(f"📊 字数: {article['word_count']}, 图片: {len(article['images'])}张")
                    print(f"🔗 来源: {article['site_type']}")
                else:
                    print("❌ 读取失败")

            except Exception as e:
                print(f"❌ 异常: {e}")

            # 避免请求过于频繁
            if i < len(test_urls):
                import time
                time.sleep(2)

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # 如果提供了URL参数，读取指定URL
        test_url = sys.argv[1]
        print(f"🚀 读取指定文章: {test_url}")

        article = read_article_quick(test_url)

        if article:
            print(f"✅ 读取成功: {article['title']}")
            print(f"📊 字数: {article['word_count']}, 图片: {len(article['images'])}张")
            print(f"🔗 来源: {article['site_type']}")

            # 显示内容预览
            preview = article['content_text'][:300] + "..." if len(article['content_text']) > 300 else article['content_text']
            print(f"\n📝 内容预览:\n{preview}")
        else:
            print("❌ 读取失败")
    else:
        # 默认测试
        print("🚀 启动智能文章读取器测试...")
        print("💡 提示: 可以通过 python 智能文章读取器.py <URL> 来读取指定文章")

        # 运行多站点测试
        test_multiple_sites()
