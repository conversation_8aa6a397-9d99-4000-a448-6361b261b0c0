{"api_key": "", "engine": "gpt-3.5-turbo", "max_tokens": 4000, "temperature": 0.7, "frequency_penalty": 0.5, "prompt": "", "showStatusBar": true, "outputToBlockQuote": false, "promptsPath": "textgenerator/prompts", "context": {"includeTitle": false, "includeStaredBlocks": true, "includeFrontmatter": true, "includeHeadings": true, "includeChildren": false, "includeMentions": false, "includeHighlights": true}, "options": {"generate-text": true, "generate-text-with-metadata": true, "insert-generated-text-From-template": true, "create-generated-text-From-template": false, "insert-text-From-template": false, "create-text-From-template": false, "show-model-From-template": true, "set_max_tokens": true, "set-model": true, "packageManager": true, "create-template": false, "get-title": true, "auto-suggest": false, "generated-text-to-clipboard-From-template": false}, "autoSuggestOptions": {"status": true, "delay": 300, "numberOfSuggestions": 5, "triggerPhrase": "  ", "stop": ".", "showStatus": true}, "displayErrorInEditor": false, "models": {}}