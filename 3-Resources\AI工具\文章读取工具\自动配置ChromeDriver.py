#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动配置ChromeDriver脚本
自动检测Chrome版本并下载对应的ChromeDriver

使用方法:
python 自动配置ChromeDriver.py

作者: AI Assistant
创建时间: 2025-07-19
"""

import os
import sys
import subprocess
import requests
import zipfile
import shutil
from pathlib import Path

class ChromeDriverInstaller:
    def __init__(self):
        self.chrome_version = None
        self.driver_version = None
        self.download_url = None
        
    def get_chrome_version(self):
        """获取Chrome版本"""
        print("🔍 正在检测Chrome版本...")
        
        # 方法1: 通过注册表
        try:
            result = subprocess.run([
                'reg', 'query', 
                'HKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon', 
                '/v', 'version'
            ], capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'version' in line and 'REG_SZ' in line:
                        version = line.split()[-1]
                        self.chrome_version = version
                        print(f"✅ 检测到Chrome版本: {version}")
                        return True
        except Exception as e:
            print(f"⚠️  注册表检测失败: {e}")
        
        # 方法2: 通过PowerShell
        try:
            result = subprocess.run([
                'powershell', '-Command',
                '(Get-Item "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe").VersionInfo.ProductVersion'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                self.chrome_version = version
                print(f"✅ 检测到Chrome版本: {version}")
                return True
        except Exception as e:
            print(f"⚠️  PowerShell检测失败: {e}")
        
        # 方法3: 通过文件属性
        chrome_paths = [
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe")
        ]
        
        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                try:
                    result = subprocess.run([
                        'powershell', '-Command',
                        f'(Get-Item "{chrome_path}").VersionInfo.ProductVersion'
                    ], capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        self.chrome_version = version
                        print(f"✅ 检测到Chrome版本: {version}")
                        return True
                except:
                    continue
        
        print("❌ 无法检测Chrome版本")
        return False
    
    def get_driver_download_url(self):
        """获取ChromeDriver下载URL"""
        if not self.chrome_version:
            return False
        
        major_version = self.chrome_version.split('.')[0]
        print(f"🔍 查找Chrome {major_version} 对应的ChromeDriver...")
        
        try:
            # Chrome 115及以上版本
            if int(major_version) >= 115:
                # 获取可用版本列表
                api_url = "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
                response = requests.get(api_url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 查找匹配的版本
                    for version_info in data['versions']:
                        if version_info['version'].startswith(f"{major_version}."):
                            # 查找Windows ChromeDriver下载链接
                            for download in version_info.get('downloads', {}).get('chromedriver', []):
                                if download['platform'] == 'win64':
                                    self.download_url = download['url']
                                    self.driver_version = version_info['version']
                                    print(f"✅ 找到匹配版本: {self.driver_version}")
                                    return True
            
            # Chrome 114及以下版本（备用方法）
            else:
                # 尝试构造下载URL
                self.driver_version = self.chrome_version
                self.download_url = f"https://chromedriver.storage.googleapis.com/{self.chrome_version}/chromedriver_win32.zip"
                print(f"✅ 使用版本: {self.driver_version}")
                return True
                
        except Exception as e:
            print(f"❌ 获取下载链接失败: {e}")
        
        return False
    
    def download_chromedriver(self):
        """下载ChromeDriver"""
        if not self.download_url:
            return False
        
        print(f"📥 正在下载ChromeDriver...")
        print(f"🔗 下载地址: {self.download_url}")
        
        try:
            response = requests.get(self.download_url, timeout=30)
            
            if response.status_code == 200:
                # 保存zip文件
                zip_filename = "chromedriver.zip"
                with open(zip_filename, "wb") as f:
                    f.write(response.content)
                
                print(f"✅ 下载完成: {zip_filename}")
                
                # 解压文件
                print("📦 正在解压文件...")
                with zipfile.ZipFile(zip_filename, 'r') as zip_ref:
                    zip_ref.extractall(".")
                
                # 查找chromedriver.exe
                chromedriver_path = None
                for root, dirs, files in os.walk("."):
                    for file in files:
                        if file == "chromedriver.exe":
                            chromedriver_path = os.path.join(root, file)
                            break
                    if chromedriver_path:
                        break
                
                if chromedriver_path:
                    print(f"✅ 找到ChromeDriver: {chromedriver_path}")
                    
                    # 清理临时文件
                    os.remove(zip_filename)
                    
                    return chromedriver_path
                else:
                    print("❌ 解压后未找到chromedriver.exe")
                    
            else:
                print(f"❌ 下载失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 下载过程出错: {e}")
        
        return False
    
    def install_chromedriver(self, chromedriver_path):
        """安装ChromeDriver"""
        print("🔧 正在安装ChromeDriver...")
        
        # 方法1: 复制到系统PATH
        system_paths = [
            "C:\\Windows\\System32",
            "C:\\Windows"
        ]
        
        for sys_path in system_paths:
            try:
                target_path = os.path.join(sys_path, "chromedriver.exe")
                shutil.copy2(chromedriver_path, target_path)
                print(f"✅ 已安装到系统目录: {target_path}")
                
                # 验证安装
                if self.verify_installation():
                    return True
                    
            except PermissionError:
                print(f"⚠️  权限不足，无法安装到: {sys_path}")
                continue
            except Exception as e:
                print(f"⚠️  安装到 {sys_path} 失败: {e}")
                continue
        
        # 方法2: 安装到项目目录
        print("📁 尝试安装到项目目录...")
        try:
            project_driver_path = "./chromedriver.exe"
            shutil.copy2(chromedriver_path, project_driver_path)
            print(f"✅ 已安装到项目目录: {project_driver_path}")
            
            # 更新环境变量
            current_dir = os.path.abspath(".")
            if current_dir not in os.environ.get("PATH", ""):
                print("💡 建议将项目目录添加到PATH环境变量")
            
            return True
            
        except Exception as e:
            print(f"❌ 安装到项目目录失败: {e}")
        
        return False
    
    def verify_installation(self):
        """验证安装"""
        print("🔍 验证ChromeDriver安装...")
        
        try:
            result = subprocess.run([
                "chromedriver", "--version"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                version_info = result.stdout.strip()
                print(f"✅ ChromeDriver安装成功!")
                print(f"📋 版本信息: {version_info}")
                return True
            else:
                print(f"❌ ChromeDriver验证失败: {result.stderr}")
                
        except FileNotFoundError:
            print("❌ ChromeDriver未找到，可能未正确安装到PATH")
        except Exception as e:
            print(f"❌ 验证过程出错: {e}")
        
        return False
    
    def test_selenium(self):
        """测试Selenium集成"""
        print("🧪 测试Selenium集成...")
        
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(options=chrome_options)
            driver.get("https://www.google.com")
            title = driver.title
            driver.quit()
            
            print(f"✅ Selenium测试成功! 页面标题: {title}")
            return True
            
        except Exception as e:
            print(f"❌ Selenium测试失败: {e}")
            return False
    
    def run(self):
        """运行完整安装流程"""
        print("=" * 60)
        print("🚀 ChromeDriver自动配置工具")
        print("=" * 60)
        
        # 步骤1: 检测Chrome版本
        if not self.get_chrome_version():
            print("\n❌ 无法检测Chrome版本，请确保已安装Chrome浏览器")
            return False
        
        # 步骤2: 获取下载链接
        if not self.get_driver_download_url():
            print("\n❌ 无法获取ChromeDriver下载链接")
            return False
        
        # 步骤3: 下载ChromeDriver
        chromedriver_path = self.download_chromedriver()
        if not chromedriver_path:
            print("\n❌ ChromeDriver下载失败")
            return False
        
        # 步骤4: 安装ChromeDriver
        if not self.install_chromedriver(chromedriver_path):
            print("\n❌ ChromeDriver安装失败")
            return False
        
        # 步骤5: 验证安装
        if not self.verify_installation():
            print("\n⚠️  ChromeDriver验证失败，但可能仍可使用")
        
        # 步骤6: 测试Selenium
        if self.test_selenium():
            print("\n🎉 ChromeDriver配置完成！现在可以运行自动化脚本了")
        else:
            print("\n⚠️  Selenium测试失败，请检查配置")
        
        # 清理临时文件
        try:
            if os.path.exists(chromedriver_path) and chromedriver_path != "./chromedriver.exe":
                os.remove(chromedriver_path)
            
            # 清理解压的文件夹
            for item in os.listdir("."):
                if os.path.isdir(item) and "chromedriver" in item.lower():
                    shutil.rmtree(item)
                    
        except:
            pass
        
        return True

def main():
    """主函数"""
    installer = ChromeDriverInstaller()
    
    try:
        success = installer.run()
        
        if success:
            print("\n" + "=" * 60)
            print("✅ 配置完成！接下来可以:")
            print("1. 运行自动化脚本: python 执行文章读取.py")
            print("2. 测试文章读取功能")
            print("3. 集成到Coze智能体")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ 配置失败！建议:")
            print("1. 检查网络连接")
            print("2. 以管理员身份运行")
            print("3. 手动下载ChromeDriver")
            print("=" * 60)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
