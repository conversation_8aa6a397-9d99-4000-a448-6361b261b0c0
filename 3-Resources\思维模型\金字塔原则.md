概念
--
一个高层级信息以及若干个同层级且用于支撑高层级信息的低层级信息组成小金字塔，由这些小金字塔组合形成有层次感的结构化的信息

由金字塔的概念总结出三个元素

层级
1. 信息是有层级的，且越重要越核心的信息位于越高层级-增加可预测性（原理：[[预测性编码模型]]）
2. 信息有低层级和高层级的配合才会是结构更加牢固，即使观点让令人信服


分类
1. 按照低层级信息的内在逻辑关系，分门别类分点进行阐述跟[[FCR阅读法]]的组块本质一致
2. 平齐原则：
	1. 逻辑层级保持一致（同一层级的信息属需一致）
	2. 要点之间不重叠

支撑
1. 自上而下-是否必要
2. 自下而上-是否充分

口诀
--
核心先行
分点阐述
不偏不重
不过不失

类型
--
[[并列型金字塔]]
[[递进型金字塔]]

技巧：
1. 低层级信息控制在3~4个
	人的短时记忆容量是4个单位，太多则会超过大脑的负责，会将其分为多个部分去认知对待，因此控制在4个以内
	少于三个则缺乏说服力
2. 