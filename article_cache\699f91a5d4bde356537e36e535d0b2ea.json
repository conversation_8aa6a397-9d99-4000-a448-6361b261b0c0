{"title": "推荐 5 个 AI 笔记开源神器，起飞了啊。", "author": "逛逛GitHub", "publish_time": "2025年06月09日 19:34", "content_html": "<div class=\"rich_media_content js_underline_content autoTypeSetting24psection fix_apple_default_style\" id=\"js_content\" style=\"\"><p class=\"js_darkmode__0\" data-pm-slice=\"0 0 []\" style='-webkit-tap-highlight-color: transparent; margin: 32px 8px 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; font-size: 17px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.544px; orphans: 2; text-align: justify; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background-color: rgb(255, 255, 255); line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important; visibility: visible;'><span class=\"js_darkmode__1\" style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; background-color: rgb(255, 255, 255); color: rgb(255, 104, 39); font-family: Futura-Medium; font-size: 32px; font-weight: 700; letter-spacing: 1px; box-sizing: border-box !important; overflow-wrap: break-word !important; visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; visibility: visible;\">01</span></span></p><p class=\"js_darkmode__2\" style='-webkit-tap-highlight-color: transparent; margin: 0px 8px 16px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; font-size: 17px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.544px; orphans: 2; text-align: justify; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; color: rgb(34, 34, 34); font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; background-color: rgb(255, 255, 255); visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;'><strong class=\"js_darkmode__3\" style='-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.544px; color: rgba(0, 0, 0, 0.9); font-size: 17px; box-sizing: border-box !important; overflow-wrap: break-word !important; visibility: visible;'><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 16px; box-sizing: border-box !important; overflow-wrap: break-word !important; visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; visibility: visible;\">AI 笔记工具</span></span></strong></p><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px; margin-right: 8px; line-height: 2em; margin-bottom: 16px; visibility: visible;\"><span class=\"js_darkmode__4\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>Blinko 开源没多长时间，现在已经获得<span style=\"font-weight: bold; visibility: visible;\" textstyle=\"\"> 4K+ </span>的 Star 了。</span></h2><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px; margin-right: 8px; line-height: 2em; margin-bottom: 16px; visibility: visible;\"><span class=\"js_darkmode__5\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>堪称你的 AI 私人笔记神器，支持 </span><span class=\"js_darkmode__6\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>macOS、Windows、Android 和 Linux 在内的<span style=\"font-weight: bold; visibility: visible;\" textstyle=\"\">多平台部署</span></span><span class=\"js_darkmode__7\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'><span style=\"font-weight: bold; visibility: visible;\" textstyle=\"\">。</span></span></h2><section class=\"js_darkmode__8\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 17px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; line-height: 2em; letter-spacing: 0.034em; font-style: normal; font-weight: normal; margin-left: 8px; margin-right: 8px; margin-bottom: 16px; visibility: visible;'></section><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px; margin-right: 8px; line-height: 2em; margin-bottom: 16px; visibility: visible;\"><span class=\"js_darkmode__9\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>老罗的锤子手机之前有一个软件，叫<span style=\"font-weight: bold; visibility: visible;\" textstyle=\"\">闪念胶囊</span>。 有好的想法能快速记录下来。</span></h2><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px; margin-right: 8px; line-height: 2em; margin-bottom: 16px; visibility: visible;\"><span class=\"js_darkmode__10\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>Blinko 这个开源神器也是<span style=\"font-weight: bold; visibility: visible;\" textstyle=\"\">帮助你记录一闪而过的灵感</span>，基于卡片笔记的理念，非常简洁、高效。</span></h2><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px; margin-right: 8px; line-height: 2em; margin-bottom: 16px; visibility: visible;\"><span class=\"js_darkmode__11\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>最重要的是它支持<span style=\"font-weight: bold; visibility: visible;\" textstyle=\"\">自己部署</span>，可以把数据保存在你的本地，隐私保障非常安全。</span></h2><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px; margin-right: 8px; line-height: 2em; margin-bottom: 16px; visibility: visible;\"><span class=\"js_darkmode__12\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>而且 </span><span class=\"js_darkmode__13\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>Blinko </span><span class=\"js_darkmode__14\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>整合了 AI 技术，能够帮助你更好地组织和管理笔记，<span style=\"font-weight: bold; visibility: visible;\" textstyle=\"\">使用自然语言就能快速的搜索查找你的笔记</span>，效率</span><span class=\"js_darkmode__15\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9); font-size: 14px; font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; letter-spacing: 0.034em; font-style: normal; font-weight: normal; visibility: visible;'>提升 Max！</span></h2><section data-mpa-action-id=\"mbnu5mh6wag\" data-mpa-preserve-tpl-color=\"t\" data-mpa-template=\"t\" data-pm-slice=\"0 0 []\" mpa-font-style=\"mbnu5mgp1nt2\" mpa-from-tpl=\"t\" mpa-preserve=\"t\" style=\"font-size: 13px;\"><pre style=\"margin: 0px 8px;padding: 0px;background: none;\"><code class=\"js_darkmode__16\" style=\"border-radius: 4px;margin-top: 0px;margin-bottom: 0px;background: rgb(248, 248, 248);color: rgb(51, 51, 51);display: block;padding: 5.95px;overflow-x: auto;white-space: nowrap;\"><span leaf=\"\">开源地址：https://github.com/blinkospace/blinko</span></code></pre></section><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__17\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"p\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>Blinko：主页</span></h2><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__18\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'></span></h2><h2 data-pm-slice=\"0 0 []\" style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__19\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"h2\",\"attributes\":{\"data-pm-slice\":\"0 0 []\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>Blinko 笔记：</span></h2><section class=\"js_darkmode__20\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'></section><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__21\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>ToDo：</span></p><section class=\"js_darkmode__22\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'></section><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__23\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>AI 加持：</span></p><section class=\"js_darkmode__24\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'></section><p class=\"js_darkmode__25\" data-pm-slice=\"0 0 []\" style='-webkit-tap-highlight-color: transparent;margin: 32px 8px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgba(0, 0, 0, 0.9);font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;font-size: 17px;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: 0.544px;orphans: 2;text-align: justify;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;background-color: rgb(255, 255, 255);line-height: 2em;'><span class=\"js_darkmode__26\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;background-color: rgb(255, 255, 255);color: rgb(255, 104, 39);font-family: Futura-Medium;font-size: 32px;font-weight: 700;letter-spacing: 1px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">02</span></span></p><p class=\"js_darkmode__27\" style='-webkit-tap-highlight-color: transparent; margin: 0px 8px 16px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; font-size: 17px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.544px; orphans: 2; text-align: justify; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; color: rgb(34, 34, 34); font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; background-color: rgb(255, 255, 255); visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;'><strong class=\"js_darkmode__28\" style='-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.544px;color: rgba(0, 0, 0, 0.9);font-size: 17px;'><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 16px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">AI 私人知识管理专家</span></span></strong></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__29\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>Reor 是一款开源<span style=\"font-weight: bold;\" textstyle=\"\">本地运行的 AI 驱动的个人知识管理应用</span>，专为那些信息量巨大、思维活跃的人设计。</span></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__30\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>不仅仅是一个简单的笔记应用，更是一个强大的知识管理系统，能够帮助你<span style=\"font-weight: bold;\" textstyle=\"\">高效地组织、搜索和利用你的知识</span>，</span><strong style=\"font-size: 14px;\"><span class=\"js_darkmode__31\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"section\",\"attributes\":{\"style\":null},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"node\",{\"tagName\":\"strong\",\"attributes\":{\"style\":null},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>支持 Markdown 编辑</span></strong><span class=\"js_darkmode__32\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>。</span></p><section class=\"js_darkmode__33\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'><span class=\"video_iframe rich_pages wx_video_iframe\" data-cover=\"http%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_jpg%2FePw3ZeGRruwp6ppMCPjSliczQLukpVSPXEHWHUNRszAPKoYeibvsrxeC2SfUiaB83sf94MuYyfCLZaKiao5C90oATQ%2F0%3Fwx_fmt%3Djpeg\" data-mpvid=\"wxv_4021751409413603330\" data-src=\"https://mp.weixin.qq.com/mp/readtemplate?t=pages/video_player_tmpl&amp;auto=0&amp;vid=wxv_4021751409413603330\" data-vh=\"369.5625\" data-vidtype=\"2\" data-vw=\"657\" height=\"386\" id=\"js_mp_video_container_0\" scrolling=\"no\" style=\"width: 661px !important; height: 386px !important; overflow: hidden;\" vid=\"wxv_4021751409413603330\" width=\"661\"><div id=\"page-content\"> <!--S 全屏播放 full_screen_mv--> <div id=\"js_mpvedio_wrapper_wxv_4021751409413603330\" style=\"position:relative;height:100%\">  <div class=\"feed-wrapper\"><div aria-hidden=\"true\" aria-modal=\"true\" class=\"wx_bottom_modal_wrp player_relate_video_dialog weui-half-screen-dialog_fold\" hidewhensideslip=\"\" role=\"dialog\" style=\"visibility: hidden; display: none;\" tabindex=\"0\"><div class=\"weui-half-screen-dialog wx_bottom_modal\" style=\"max-height: none;\"><div class=\"wx_bottom_modal_group_container\" style=\"transform: translateX(calc(0% + 0px)); max-height: none;\"><div aria-hidden=\"false\" class=\"wx_bottom_modal_group\" style=\"left: 0%; max-height: none;\"><div class=\"weui-half-screen-dialog__hd__wrp\"><div class=\"weui-half-screen-dialog__hd\"><div class=\"weui-half-screen-dialog__hd__side\"><button class=\"weui-btn_icon weui-wa-hotarea\">关闭</button></div><div class=\"weui-half-screen-dialog__hd__main\"><strong class=\"weui-half-screen-dialog__title\">观看更多</strong></div><div class=\"weui-half-screen-dialog__hd__side\"><!-- --><button class=\"weui-btn_icon weui-wa-hotarea\" style=\"display: none;\">更多</button></div></div></div><!-- --></div></div></div></div><div class=\"infinity-list__wrapper\" style=\"height: 370px;\"><div class=\"\" style=\"height: 370px; overflow: visible;\"><div class=\"infinity-list__page destory-enter-to\" data-key=\"wxv_4021751409413603330\" infinity-idx=\"0\" style=\"height: 370px; position: absolute; top: 0px; opacity: 1;\"><div class=\"mp-video-player\" data-v-1639cf84=\"\" style=\"height: 100%;\"><div class=\"js_mpvedio page_video_wrapper\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" id=\"js_mpvedio_1752993003407_750836637610\"><div class=\"js_page_video page_video ratio_primary align_upper_center page_video_without-control page_video_skin-normal\" data-v-823ba74e=\"\" style=\"display: block; width: 100%; height: 370px;\"><div class=\"full_screen_opr wx_video_play_opr\" data-v-823ba74e=\"\" style=\"\"><button class=\"mid_play_box reset_btn\" data-v-823ba74e=\"\" type=\"button\"><span class=\"aria_hidden_abs\" data-v-823ba74e=\"\">，时长</span><span class=\"video_length\" data-v-823ba74e=\"\">00:09</span></button></div><!-- --><div class=\"mid_opr fast_pre_next\" data-v-823ba74e=\"\" style=\"display: none;\"><p class=\"video_length\" data-v-823ba74e=\"\"><span class=\"played_time js_forward_play_time\" data-v-823ba74e=\"\">0</span><span class=\"js_forward_seperator\" data-v-823ba74e=\"\">/</span><span class=\"total_time js_forward_total_time\" data-v-823ba74e=\"\">0</span></p></div><div class=\"wx_video_progress_msg full_screen_opr\" data-v-823ba74e=\"\" role=\"alert\" style=\"display: none;\"><div class=\"wx_video_progress_msg_inner\" data-v-823ba74e=\"\"><span class=\"wx_video_progress_current\" data-v-823ba74e=\"\">00:00</span><span class=\"wx_video_progress_gap\" data-v-823ba74e=\"\">/</span><span class=\"wx_video_progress_total\" data-v-823ba74e=\"\">00:09</span></div></div><div class=\"video_screen_mode_switch\" data-v-823ba74e=\"\" style=\"bottom: calc(50% - 336px); display: none;\"><button class=\"reset_btn video_screen_mode_switch_btn weui-wa-hotarea\" data-v-823ba74e=\"\" type=\"button\"> 切换到横屏模式 </button></div><div class=\"full_screen_opr wx_video_pause_full_mod\" data-v-823ba74e=\"\" style=\"display: none;\"><button class=\"reset_btn wx_video_pause_full_btn\" data-v-823ba74e=\"\" type=\"button\">继续播放</button></div><div class=\"js_control video_opr video_opr_sns\" data-v-823ba74e=\"\" style=\"bottom: 0px; display: none;\"><div class=\"opr_inner\" data-v-823ba74e=\"\"><div class=\"opr_inner_fl\" data-v-823ba74e=\"\"><div class=\"js_switch weui-wa-hotarea switch switch_on\" data-v-823ba74e=\"\"><a class=\"btn_opr\" data-v-823ba74e=\"\" href=\"javascript:;\" role=\"button\">播放</a></div><div data-v-823ba74e=\"\" role=\"option\"><div class=\"played_time js_now_play_time\" data-v-823ba74e=\"\">00:00</div><span data-v-823ba74e=\"\">/</span><div class=\"total_time js_total_time\" data-v-823ba74e=\"\">00:09</div></div><!-- --><div class=\"total_time js_total_time\" data-v-823ba74e=\"\" role=\"option\" style=\"display: none;\">00:09</div></div><div class=\"opr_inner_fr\" data-v-823ba74e=\"\"><!-- --><!-- --><!-- --><div class=\"weui-wa-hotarea js_full_screen_control screenSize_control full\" data-v-823ba74e=\"\" role=\"button\"><i class=\"icon_control\" data-v-823ba74e=\"\">全屏</i></div></div></div></div><div class=\"full_screen_opr video_quick_play_context\" data-v-823ba74e=\"\" role=\"alert\" style=\"display: none;\"><div class=\"video_quick_play_msg\" data-v-823ba74e=\"\"> 倍速播放中 </div></div><div class=\"js_sub_setting video_full-screen__footer video_full-screen__footer__sub-setting hide\" data-v-823ba74e=\"\"><div class=\"video_full-screen__sub-setting video_full-screen__sub-setting__speed js_playback_mode_select\" data-v-823ba74e=\"\" style=\"display: none;\"><a class=\"video_full-screen__sub-setting__item js_playback_0\" data-v-823ba74e=\"\" href=\"javascript:;\"> 0.5倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_1\" data-v-823ba74e=\"\" href=\"javascript:;\"> 0.75倍 </a><a class=\"video_full-screen__sub-setting__item current js_playback_2\" data-v-823ba74e=\"\" href=\"javascript:;\"> 1.0倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_3\" data-v-823ba74e=\"\" href=\"javascript:;\"> 1.5倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_4\" data-v-823ba74e=\"\" href=\"javascript:;\"> 2.0倍 </a></div><div class=\"video_full-screen__sub-setting video_full-screen__sub-setting__ratio js_play_mode_select\" data-v-823ba74e=\"\" style=\"display: none;\"><a class=\"video_full-screen__sub-setting__item current js_resolution_0\" data-v-823ba74e=\"\" href=\"javascript:;\"> 超清 </a><a class=\"video_full-screen__sub-setting__item js_resolution_1\" data-v-823ba74e=\"\" href=\"javascript:;\"> 流畅 </a></div></div><div class=\"js_inner inner not_fullscreen\" data-v-823ba74e=\"\"><div class=\"js_video_poster video_poster\" data-v-823ba74e=\"\" style=\"display: none;\"><video class=\"\" controlslist=\"nodownload\" crossorigin=\"anonymous\" data-v-823ba74e=\"\" playsinline=\"isiPhoneShowPlaysinline\" poster=\"http://mmbiz.qpic.cn/sz_mmbiz_jpg/ePw3ZeGRruwp6ppMCPjSliczQLukpVSPXEHWHUNRszAPKoYeibvsrxeC2SfUiaB83sf94MuYyfCLZaKiao5C90oATQ/0?wx_fmt=jpeg&amp;wxfrom=16\" preload=\"metadata\" src=\"https://mpvideo.qpic.cn/0bc334bpgaacdaalzssg3juffx6d6ppqf4ya.f10002.mp4?dis_k=b66cba0d298b78cf313c76db626402d1&amp;dis_t=1752993001&amp;play_scene=10120&amp;auth_info=dqeG/KJTLAdi1saSwlErbUE/YBwzZD0fZzEBNhlIIDlQWhN4ZV9xaAAZYwMvczxXSw==&amp;auth_key=824c343ee8f8bc289b3e52101eb68230&amp;vid=wxv_4021751409413603330&amp;format_id=10002&amp;support_redirect=0&amp;mmversion=false\" webkit-playsinline=\"isiPhoneShowPlaysinline\"> 您的浏览器不支持 video 标签 </video></div><div class=\"video_poster__info\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"display: none;\"><p class=\"video_poster__info__title\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"font-size: 17px;\">继续观看</p><p class=\"video_poster__info__desc\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"font-size: 12px;\"> 推荐 5 个 AI 笔记开源神器，起飞了啊。 </p></div></div><div class=\"video_profile_area\" data-v-823ba74e=\"\" style=\"display: none;\"><div data-v-823ba74e=\"\"><button class=\"reset_btn video_profile_relate_video_btn js_wx_tap_highlight wx_tap_link\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"display: none;\">观看更多</button></div><div data-v-823ba74e=\"\" role=\"link\" style=\"width: fit-content; max-width: 100%;\" tabindex=\"0\"><div class=\"weui-wa-hotarea video_profile_desc_wrp\" data-v-823ba74e=\"\" role=\"option\"><div class=\"weui-hidden_abs\" data-v-823ba74e=\"\">,</div><div class=\"video_profile_desc\" data-v-823ba74e=\"\">推荐 5 个 AI 笔记开源神器，起飞了啊。</div></div></div><div class=\"video_profile_wrp weui-flex\" data-v-823ba74e=\"\"><div class=\"video_profile weui-flex weui-flex__item\" data-v-823ba74e=\"\"><span class=\"video_profile_nickname weui-wa-hotarea\" data-v-823ba74e=\"\">逛逛GitHub</span><button class=\"reset_btn video_profile_follow_btn weui-wa-hotarea\" data-v-823ba74e=\"\" style=\"display: none;\" type=\"button\">已关注</button></div><div class=\"video_sns_context\" data-v-823ba74e=\"\" style=\"display: none;\"><button class=\"video_sns_btn video_sns_btn_praise\" data-v-823ba74e=\"\" title=\"\" type=\"button\"><span class=\"video_sns_num\" data-v-823ba74e=\"\">点赞</span></button><button class=\"video_sns_btn video_sns_btn_love\" data-v-823ba74e=\"\" title=\"\" type=\"button\"><span class=\"video_sns_num\" data-v-823ba74e=\"\">在看</span></button></div></div></div></div></div></div></div></div></div><!-- --></div> </div> <!--E 视频播放器--> <!-- S 视频社交--> <div class=\"interact_video\" id=\"bottom_bar\" style=\"display:none;height: 35px;\"> <div class=\"inter_opr\"> <a class=\"access_original\" href=\"javascript:;\" id=\"video_detail_btn\" target=\"_blank\">         视频详情       </a> </div> </div> </div></span></section><section data-mpa-action-id=\"mbnu6mpspi1\" data-mpa-preserve-tpl-color=\"t\" data-mpa-template=\"t\" data-pm-slice=\"0 0 []\" mpa-font-style=\"mbnu6mpakrb\" mpa-from-tpl=\"t\" mpa-preserve=\"t\" style=\"font-size: 13px;\"><pre style=\"margin: 0px 8px;padding: 0px;background: none;\"><code class=\"js_darkmode__34\" style=\"border-radius: 4px;margin-top: 0px;margin-bottom: 0px;background: rgb(248, 248, 248);color: rgb(51, 51, 51);display: block;padding: 5.95px;overflow-x: auto;white-space: nowrap;\"><span leaf=\"\">开源地址：https://github.com/reorproject/reor</span></code></pre></section><section style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><strong style=\"font-size: 14px;\"><span class=\"js_darkmode__35\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'><span style=\"font-weight: bold;\" textstyle=\"\">① 自组织（Self-organizing）：</span>Reor 能够自动链接你的想法，将零散的笔记组织成一个有机的知识网络。</span></strong></section><section style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><strong style=\"font-size: 14px;\"><span class=\"js_darkmode__36\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'><span style=\"font-weight: bold;\" textstyle=\"\">② AI 驱动：</span>利用AI技术，提供强大的语义搜索功能，让你能够快速找到你需要的任何信息，即使你只记得部分关键词。</span></strong></section><p class=\"js_darkmode__37\" data-pm-slice=\"0 0 []\" style='-webkit-tap-highlight-color: transparent;margin: 32px 8px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgba(0, 0, 0, 0.9);font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;font-size: 17px;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: 0.544px;orphans: 2;text-align: justify;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;background-color: rgb(255, 255, 255);line-height: 2em;'><span class=\"js_darkmode__38\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;background-color: rgb(255, 255, 255);color: rgb(255, 104, 39);font-family: Futura-Medium;font-size: 32px;font-weight: 700;letter-spacing: 1px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">03</span></span></p><p class=\"js_darkmode__39\" style='-webkit-tap-highlight-color: transparent; margin: 0px 8px 16px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; font-size: 17px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.544px; orphans: 2; text-align: justify; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; color: rgb(34, 34, 34); font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; background-color: rgb(255, 255, 255); visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;'><strong class=\"js_darkmode__40\" style='-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.544px;color: rgba(0, 0, 0, 0.9);font-size: 17px;'><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 16px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">基于 AI 的 Markdown笔记神器</span></span></strong></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__41\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"p\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>NoteGen <span style=\"font-weight: bold;\" textstyle=\"\">很</span></span><span class=\"js_darkmode__42\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'><span style=\"font-weight: bold;\" textstyle=\"\">轻量才十几兆</span>、支持 Markdown、加持了 AI，目前</span><strong style=\"font-size: 14px;\"><span class=\"js_darkmode__43\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"section\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"node\",{\"tagName\":\"strong\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>支持 Mac、Windows 和 Linux 系统，未来还将支持 iOS 和 Android。</span></strong></p><section class=\"js_darkmode__45\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'></section><section class=\"js_darkmode__46\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'></section><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__47\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'><span style=\"font-weight: bold;\" textstyle=\"\">能将碎片化知识转化为可读性强的笔记</span>，让你轻松记录、高效写作。</span></p><section data-mpa-action-id=\"mbnu80pirdc\" data-mpa-preserve-tpl-color=\"t\" data-mpa-template=\"t\" data-pm-slice=\"0 0 []\" mpa-font-style=\"mbnu80oz1vbe\" mpa-from-tpl=\"t\" mpa-preserve=\"t\" style=\"font-size: 13px;\"><pre style=\"margin: 0px 8px;padding: 0px;background: none;\"><code class=\"js_darkmode__48\" style=\"border-radius: 4px;margin-top: 0px;margin-bottom: 0px;background: rgb(248, 248, 248);color: rgb(51, 51, 51);display: block;padding: 5.95px;overflow-x: auto;white-space: nowrap;\"><span leaf=\"\">开源地址：https://github.com/codexu/note-gen</span></code></pre></section><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__49\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>分为<span style=\"font-weight: bold;\" textstyle=\"\">“记录”</span>和<span style=\"font-weight: bold;\" textstyle=\"\">“写作”</span>两个页面，在“记录”页面使用各种方式记录信息（</span><strong style=\"font-size: 14px;\"><span class=\"js_darkmode__50\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"section\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"node\",{\"tagName\":\"strong\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>截图、文本、插图、文件和链接等多种记录方式</span></strong><span class=\"js_darkmode__51\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>），然后将这些记录整理到“写作”页面，形成一篇完整的笔记。</span></p><section class=\"js_darkmode__52\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'></section><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__53\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'> AI 模型可以帮助你自动整理这些记录，并辅助你进行写作默认支持本地离线使用，你的数据安全可靠地存储在本地。</span></p><section nodeleaf=\"\" style=\"margin-left: 8px;margin-right: 8px;\"><span class=\"video_iframe rich_pages\" data-cover=\"http%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_jpg%2FePw3ZeGRruyib0FBeCyMut1j4Xam7mibK4jUHsluCOoKxntAJvxmaseZWqJFR1Jfh71ZtjfwiaAia42RKVnWoiaKILQ%2F0%3Fwx_fmt%3Djpeg\" data-mpvid=\"wxv_4023272730601406469\" data-src=\"https://mp.weixin.qq.com/mp/readtemplate?t=pages/video_player_tmpl&amp;auto=0&amp;vid=wxv_4023272730601406469\" data-vh=\"369.5625\" data-vidtype=\"2\" data-vw=\"657\" height=\"386\" id=\"js_mp_video_container_1\" scrolling=\"no\" style=\"width: 661px !important; height: 386px !important; overflow: hidden;\" vid=\"wxv_4023272730601406469\" width=\"661\"><div id=\"page-content\"> <!--S 全屏播放 full_screen_mv--> <div id=\"js_mpvedio_wrapper_wxv_4023272730601406469\" style=\"position:relative;height:100%\">  <div class=\"feed-wrapper\"><div aria-hidden=\"true\" aria-modal=\"true\" class=\"wx_bottom_modal_wrp player_relate_video_dialog weui-half-screen-dialog_fold\" hidewhensideslip=\"\" role=\"dialog\" style=\"visibility: hidden; display: none;\" tabindex=\"0\"><div class=\"weui-half-screen-dialog wx_bottom_modal\" style=\"max-height: none;\"><div class=\"wx_bottom_modal_group_container\" style=\"transform: translateX(calc(0% + 0px)); max-height: none;\"><div aria-hidden=\"false\" class=\"wx_bottom_modal_group\" style=\"left: 0%; max-height: none;\"><div class=\"weui-half-screen-dialog__hd__wrp\"><div class=\"weui-half-screen-dialog__hd\"><div class=\"weui-half-screen-dialog__hd__side\"><button class=\"weui-btn_icon weui-wa-hotarea\">关闭</button></div><div class=\"weui-half-screen-dialog__hd__main\"><strong class=\"weui-half-screen-dialog__title\">观看更多</strong></div><div class=\"weui-half-screen-dialog__hd__side\"><!-- --><button class=\"weui-btn_icon weui-wa-hotarea\" style=\"display: none;\">更多</button></div></div></div><!-- --></div></div></div></div><div class=\"infinity-list__wrapper\" style=\"height: 370px;\"><div class=\"\" style=\"height: 370px; overflow: visible;\"><div class=\"infinity-list__page destory-enter-to\" data-key=\"wxv_4023272730601406469\" infinity-idx=\"0\" style=\"height: 370px; position: absolute; top: 0px; opacity: 1;\"><div class=\"mp-video-player\" data-v-1639cf84=\"\" style=\"height: 100%;\"><div class=\"js_mpvedio page_video_wrapper\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" id=\"js_mpvedio_1752993003416_1038182723851\"><div class=\"js_page_video page_video ratio_primary align_upper_center page_video_without-control page_video_skin-normal\" data-v-823ba74e=\"\" style=\"display: block; width: 100%; height: 370px;\"><div class=\"full_screen_opr wx_video_play_opr\" data-v-823ba74e=\"\" style=\"\"><button class=\"mid_play_box reset_btn\" data-v-823ba74e=\"\" type=\"button\"><span class=\"aria_hidden_abs\" data-v-823ba74e=\"\">，时长</span><span class=\"video_length\" data-v-823ba74e=\"\">01:08</span></button></div><!-- --><div class=\"mid_opr fast_pre_next\" data-v-823ba74e=\"\" style=\"display: none;\"><p class=\"video_length\" data-v-823ba74e=\"\"><span class=\"played_time js_forward_play_time\" data-v-823ba74e=\"\">0</span><span class=\"js_forward_seperator\" data-v-823ba74e=\"\">/</span><span class=\"total_time js_forward_total_time\" data-v-823ba74e=\"\">0</span></p></div><div class=\"wx_video_progress_msg full_screen_opr\" data-v-823ba74e=\"\" role=\"alert\" style=\"display: none;\"><div class=\"wx_video_progress_msg_inner\" data-v-823ba74e=\"\"><span class=\"wx_video_progress_current\" data-v-823ba74e=\"\">00:00</span><span class=\"wx_video_progress_gap\" data-v-823ba74e=\"\">/</span><span class=\"wx_video_progress_total\" data-v-823ba74e=\"\">01:08</span></div></div><div class=\"video_screen_mode_switch\" data-v-823ba74e=\"\" style=\"bottom: calc(50% - 336px); display: none;\"><button class=\"reset_btn video_screen_mode_switch_btn weui-wa-hotarea\" data-v-823ba74e=\"\" type=\"button\"> 切换到横屏模式 </button></div><div class=\"full_screen_opr wx_video_pause_full_mod\" data-v-823ba74e=\"\" style=\"display: none;\"><button class=\"reset_btn wx_video_pause_full_btn\" data-v-823ba74e=\"\" type=\"button\">继续播放</button></div><div class=\"js_control video_opr video_opr_sns\" data-v-823ba74e=\"\" style=\"bottom: 0px; display: none;\"><div class=\"opr_inner\" data-v-823ba74e=\"\"><div class=\"opr_inner_fl\" data-v-823ba74e=\"\"><div class=\"js_switch weui-wa-hotarea switch switch_on\" data-v-823ba74e=\"\"><a class=\"btn_opr\" data-v-823ba74e=\"\" href=\"javascript:;\" role=\"button\">播放</a></div><div data-v-823ba74e=\"\" role=\"option\"><div class=\"played_time js_now_play_time\" data-v-823ba74e=\"\">00:00</div><span data-v-823ba74e=\"\">/</span><div class=\"total_time js_total_time\" data-v-823ba74e=\"\">01:08</div></div><!-- --><div class=\"total_time js_total_time\" data-v-823ba74e=\"\" role=\"option\" style=\"display: none;\">01:08</div></div><div class=\"opr_inner_fr\" data-v-823ba74e=\"\"><!-- --><!-- --><!-- --><div class=\"weui-wa-hotarea js_full_screen_control screenSize_control full\" data-v-823ba74e=\"\" role=\"button\"><i class=\"icon_control\" data-v-823ba74e=\"\">全屏</i></div></div></div></div><div class=\"full_screen_opr video_quick_play_context\" data-v-823ba74e=\"\" role=\"alert\" style=\"display: none;\"><div class=\"video_quick_play_msg\" data-v-823ba74e=\"\"> 倍速播放中 </div></div><div class=\"js_sub_setting video_full-screen__footer video_full-screen__footer__sub-setting hide\" data-v-823ba74e=\"\"><div class=\"video_full-screen__sub-setting video_full-screen__sub-setting__speed js_playback_mode_select\" data-v-823ba74e=\"\" style=\"display: none;\"><a class=\"video_full-screen__sub-setting__item js_playback_0\" data-v-823ba74e=\"\" href=\"javascript:;\"> 0.5倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_1\" data-v-823ba74e=\"\" href=\"javascript:;\"> 0.75倍 </a><a class=\"video_full-screen__sub-setting__item current js_playback_2\" data-v-823ba74e=\"\" href=\"javascript:;\"> 1.0倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_3\" data-v-823ba74e=\"\" href=\"javascript:;\"> 1.5倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_4\" data-v-823ba74e=\"\" href=\"javascript:;\"> 2.0倍 </a></div><div class=\"video_full-screen__sub-setting video_full-screen__sub-setting__ratio js_play_mode_select\" data-v-823ba74e=\"\" style=\"display: none;\"><a class=\"video_full-screen__sub-setting__item current js_resolution_0\" data-v-823ba74e=\"\" href=\"javascript:;\"> 超清 </a><a class=\"video_full-screen__sub-setting__item js_resolution_1\" data-v-823ba74e=\"\" href=\"javascript:;\"> 流畅 </a></div></div><div class=\"js_inner inner not_fullscreen\" data-v-823ba74e=\"\"><div class=\"js_video_poster video_poster\" data-v-823ba74e=\"\" style=\"display: none;\"><video class=\"video_fill\" controlslist=\"nodownload\" crossorigin=\"anonymous\" data-v-823ba74e=\"\" playsinline=\"isiPhoneShowPlaysinline\" poster=\"http://mmbiz.qpic.cn/sz_mmbiz_jpg/ePw3ZeGRruyib0FBeCyMut1j4Xam7mibK4jUHsluCOoKxntAJvxmaseZWqJFR1Jfh71ZtjfwiaAia42RKVnWoiaKILQ/0?wx_fmt=jpeg&amp;wxfrom=16\" preload=\"metadata\" src=\"https://mpvideo.qpic.cn/0bc3tebqqaadmualt6cgfbufhgodbcmqgcaa.f10002.mp4?dis_k=04ede81fbbaf255ea31247ccbc17b45d&amp;dis_t=1752993001&amp;play_scene=10120&amp;auth_info=LafrppMBL1Vg1JeRw1YqORA4Y041NWhNZWIGMUBJImkLAxd9aAlyOgIbMgAudD0DGg==&amp;auth_key=********************************&amp;vid=wxv_4023272730601406469&amp;format_id=10002&amp;support_redirect=0&amp;mmversion=false\" webkit-playsinline=\"isiPhoneShowPlaysinline\"> 您的浏览器不支持 video 标签 </video></div><div class=\"video_poster__info\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"display: none;\"><p class=\"video_poster__info__title\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"font-size: 17px;\">继续观看</p><p class=\"video_poster__info__desc\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"font-size: 12px;\"> 推荐 5 个 AI 笔记开源神器，起飞了啊。 </p></div></div><div class=\"video_profile_area\" data-v-823ba74e=\"\" style=\"display: none;\"><div data-v-823ba74e=\"\"><button class=\"reset_btn video_profile_relate_video_btn js_wx_tap_highlight wx_tap_link\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"display: none;\">观看更多</button></div><div data-v-823ba74e=\"\" role=\"link\" style=\"width: fit-content; max-width: 100%;\" tabindex=\"0\"><div class=\"weui-wa-hotarea video_profile_desc_wrp\" data-v-823ba74e=\"\" role=\"option\"><div class=\"weui-hidden_abs\" data-v-823ba74e=\"\">,</div><div class=\"video_profile_desc\" data-v-823ba74e=\"\">推荐 5 个 AI 笔记开源神器，起飞了啊。</div></div></div><div class=\"video_profile_wrp weui-flex\" data-v-823ba74e=\"\"><div class=\"video_profile weui-flex weui-flex__item\" data-v-823ba74e=\"\"><span class=\"video_profile_nickname weui-wa-hotarea\" data-v-823ba74e=\"\">逛逛GitHub</span><button class=\"reset_btn video_profile_follow_btn weui-wa-hotarea\" data-v-823ba74e=\"\" style=\"display: none;\" type=\"button\">已关注</button></div><div class=\"video_sns_context\" data-v-823ba74e=\"\" style=\"display: none;\"><button class=\"video_sns_btn video_sns_btn_praise\" data-v-823ba74e=\"\" title=\"\" type=\"button\"><span class=\"video_sns_num\" data-v-823ba74e=\"\">点赞</span></button><button class=\"video_sns_btn video_sns_btn_love\" data-v-823ba74e=\"\" title=\"\" type=\"button\"><span class=\"video_sns_num\" data-v-823ba74e=\"\">在看</span></button></div></div></div></div></div></div></div></div></div><!-- --></div> </div> <!--E 视频播放器--> <!-- S 视频社交--> <div class=\"interact_video\" id=\"bottom_bar\" style=\"display:none;height: 35px;\"> <div class=\"inter_opr\"> <a class=\"access_original\" href=\"javascript:;\" id=\"video_detail_btn\" target=\"_blank\">         视频详情       </a> </div> </div> </div></span></section><p class=\"js_darkmode__54\" data-pm-slice=\"0 0 []\" style='-webkit-tap-highlight-color: transparent;margin: 32px 8px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgba(0, 0, 0, 0.9);font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;font-size: 17px;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: 0.544px;orphans: 2;text-align: justify;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;background-color: rgb(255, 255, 255);line-height: 2em;'><span class=\"js_darkmode__55\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;background-color: rgb(255, 255, 255);color: rgb(255, 104, 39);font-family: Futura-Medium;font-size: 32px;font-weight: 700;letter-spacing: 1px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">04</span></span></p><p class=\"js_darkmode__56\" style='-webkit-tap-highlight-color: transparent; margin: 0px 8px 16px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; font-size: 17px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.544px; orphans: 2; text-align: justify; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; color: rgb(34, 34, 34); font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; background-color: rgb(255, 255, 255); visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;'><strong class=\"js_darkmode__57\" style='-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.544px;color: rgba(0, 0, 0, 0.9);font-size: 17px;'><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 16px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">Open-NoteBook</span></span></strong></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__59\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>Google Notebook LM 的替代品。AI 驱动的高效创作，接入各大厂商的 AI 大模型。</span></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__60\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'><span style=\"font-weight: bold;\" textstyle=\"\">输入关键词，就能自动总结文献要点。</span></span><span class=\"js_darkmode__61\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>PDF/EPUB/视频/音频等20+格式一键转文字分析，变成你的笔记。</span></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__62\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"p\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>还能<span style=\"font-weight: bold;\" textstyle=\"\">姜笔记秒变成播客，自定义语言与风格。</span></span></p><section data-mpa-action-id=\"mbnu98lgtsg\" data-mpa-preserve-tpl-color=\"t\" data-mpa-template=\"t\" data-pm-slice=\"0 0 []\" mpa-font-style=\"mbnu98ky1fzl\" mpa-from-tpl=\"t\" mpa-preserve=\"t\" style=\"font-size: 13px;\"><pre style=\"margin:0;padding:0;border-radius:none;background:none;\"><code class=\"js_darkmode__63\" style=\"border-radius: 4px;margin: 0px 0.15em;background: rgb(248, 248, 248);color: rgb(51, 51, 51);display: block;padding: 5.95px;overflow-x: auto;white-space: nowrap;\"><span leaf=\"\">开源地址：https://github.com/lfnovo/open-notebook</span></code></pre></section><section class=\"js_darkmode__64\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'></section><p class=\"js_darkmode__65\" data-pm-slice=\"0 0 []\" style='-webkit-tap-highlight-color: transparent;margin: 32px 8px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgba(0, 0, 0, 0.9);font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;font-size: 17px;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: 0.544px;orphans: 2;text-align: justify;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;background-color: rgb(255, 255, 255);line-height: 2em;'><span class=\"js_darkmode__66\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;background-color: rgb(255, 255, 255);color: rgb(255, 104, 39);font-family: Futura-Medium;font-size: 32px;font-weight: 700;letter-spacing: 1px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">05</span></span></p><p class=\"js_darkmode__67\" style='-webkit-tap-highlight-color: transparent; margin: 0px 8px 16px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; font-size: 17px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.544px; orphans: 2; text-align: justify; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; color: rgb(34, 34, 34); font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; background-color: rgb(255, 255, 255); visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;'><strong class=\"js_darkmode__68\" style='-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.544px;color: rgba(0, 0, 0, 0.9);font-size: 17px;'><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 16px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">纯文本文件记事本和待办事项</span></span></strong></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__69\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>一款基于<span style=\"font-weight: bold;\" textstyle=\"\">纯文本文件的笔记和待办事项管理器。</span></span></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__70\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>支持 Markdown 语法，轻松创建格式丰富的笔记，插入图片、链接等，提升笔记的可读性和组织性。  </span></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__71\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>除了笔记，还内置了<span style=\"font-weight: bold;\" textstyle=\"\">待办事项</span>管理功能，轻松创建和管理待办事项列表，设置截止日期和优先级，有效提升你的工作效率和时间管理能力。</span></p><p style=\"margin-left: 8px;margin-right: 8px;line-height: 2em;margin-bottom: 16px;\"><span class=\"js_darkmode__72\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>而且 QOwnNotes 支持与 Nextcloud 和 ownCloud <span style=\"font-weight: bold;\" textstyle=\"\">云端同步</span>，无论是在电脑上还是手机上，</span><span class=\"js_darkmode__73\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"p\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>随时随地访问你的笔记和待办事项</span><span class=\"js_darkmode__74\" leaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 14px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>。  </span></p><section data-mpa-action-id=\"mbnua1nr22lj\" data-mpa-preserve-tpl-color=\"t\" data-mpa-template=\"t\" data-pm-slice=\"0 0 []\" mpa-font-style=\"mbnua1n912db\" mpa-from-tpl=\"t\" mpa-preserve=\"t\" style=\"font-size: 13px;\"><pre style=\"margin: 0px 8px;padding: 0px;background: none;\"><code class=\"js_darkmode__75\" style=\"border-radius: 4px;margin-top: 0px;margin-bottom: 0px;background: rgb(248, 248, 248);color: rgb(51, 51, 51);display: block;padding: 5.95px;overflow-x: auto;white-space: nowrap;\"><span leaf=\"\">开源地址：https://github.com/pbek/QOwnNotes</span></code></pre></section><section class=\"js_darkmode__76\" data-mpa-action-id=\"mbnu4czcy4j\" nodeleaf=\"\" style='color: rgba(0, 0, 0, 0.9);font-size: 17px;font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;line-height: 2em;letter-spacing: 0.034em;font-style: normal;font-weight: normal;margin-left: 8px;margin-right: 8px;margin-bottom: 16px;'></section><p class=\"js_darkmode__77\" data-pm-slice=\"0 0 []\" style='-webkit-tap-highlight-color: transparent;margin: 32px 8px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgba(0, 0, 0, 0.9);font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;font-size: 17px;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: 0.544px;orphans: 2;text-align: justify;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;background-color: rgb(255, 255, 255);line-height: 2em;'><span class=\"js_darkmode__78\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;background-color: rgb(255, 255, 255);color: rgb(255, 104, 39);font-family: Futura-Medium;font-size: 32px;font-weight: 700;letter-spacing: 1px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">05</span></span></p><p class=\"js_darkmode__79\" style='-webkit-tap-highlight-color: transparent; margin: 0px 8px 16px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; font-size: 17px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.544px; orphans: 2; text-align: justify; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; color: rgb(34, 34, 34); font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif; background-color: rgb(255, 255, 255); visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;'><strong class=\"js_darkmode__80\" style='-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.544px;color: rgba(0, 0, 0, 0.9);font-size: 17px;'><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 16px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">都看到这了，关注下吧。</span></span></strong></p><p class=\"js_darkmode__82\" data-mpa-action-id=\"mb8qzook17a4\" data-pm-slice=\"0 0 []\" mpa-font-style=\"mb8qzony1b20\" style='-webkit-tap-highlight-color: transparent;margin: 0px 8px 16px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgba(0, 0, 0, 0.9);font-family: \"PingFang SC\", system-ui, -apple-system, \"system-ui\", \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: 0.544px;orphans: 2;text-align: justify;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;background-color: rgb(255, 255, 255);font-size: 14px;line-height: 2em;'><span class=\"js_darkmode__83\" leaf=\"\" style='-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgba(0, 0, 0, 0.9);font-family: mp-quote, \"PingFang SC\", system-ui, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Hiragino Sans GB\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;letter-spacing: 0.034em;font-style: normal;font-weight: normal;'>这个公众号历史发布过很多有趣的开源项目，如果你懒得翻文章一个个找，你直接关注微信公众号：逛逛 GitHub ，后台对话聊天就行了。</span></p></div>", "content_text": "01AI 笔记工具Blinko 开源没多长时间，现在已经获得 4K+ 的 Star 了。堪称你的 AI 私人笔记神器，支持 macOS、Windows、Android 和 Linux 在内的多平台部署。老罗的锤子手机之前有一个软件，叫闪念胶囊。 有好的想法能快速记录下来。Blinko 这个开源神器也是帮助你记录一闪而过的灵感，基于卡片笔记的理念，非常简洁、高效。最重要的是它支持自己部署，可以把数据保存在你的本地，隐私保障非常安全。而且 Blinko 整合了 AI 技术，能够帮助你更好地组织和管理笔记，使用自然语言就能快速的搜索查找你的笔记，效率提升 Max！开源地址：https://github.com/blinkospace/blinkoBlinko：主页Blinko 笔记：ToDo：AI 加持：02AI 私人知识管理专家Reor 是一款开源本地运行的 AI 驱动的个人知识管理应用，专为那些信息量巨大、思维活跃的人设计。不仅仅是一个简单的笔记应用，更是一个强大的知识管理系统，能够帮助你高效地组织、搜索和利用你的知识，支持 Markdown 编辑。    关闭观看更多更多，时长00:090/000:00/00:09 切换到横屏模式 继续播放播放00:00/00:0900:09全屏 倍速播放中  0.5倍  0.75倍  1.0倍  1.5倍  2.0倍  超清  流畅  您的浏览器不支持 video 标签 继续观看 推荐 5 个 AI 笔记开源神器，起飞了啊。 观看更多,推荐 5 个 AI 笔记开源神器，起飞了啊。逛逛GitHub已关注点赞在看               视频详情          开源地址：https://github.com/reorproject/reor① 自组织（Self-organizing）：Reor 能够自动链接你的想法，将零散的笔记组织成一个有机的知识网络。② AI 驱动：利用AI技术，提供强大的语义搜索功能，让你能够快速找到你需要的任何信息，即使你只记得部分关键词。03基于 AI 的 Markdown笔记神器NoteGen 很轻量才十几兆、支持 Markdown、加持了 AI，目前支持 Mac、Windows 和 Linux 系统，未来还将支持 iOS 和 Android。能将碎片化知识转化为可读性强的笔记，让你轻松记录、高效写作。开源地址：https://github.com/codexu/note-gen分为“记录”和“写作”两个页面，在“记录”页面使用各种方式记录信息（截图、文本、插图、文件和链接等多种记录方式），然后将这些记录整理到“写作”页面，形成一篇完整的笔记。 AI 模型可以帮助你自动整理这些记录，并辅助你进行写作默认支持本地离线使用，你的数据安全可靠地存储在本地。    关闭观看更多更多，时长01:080/000:00/01:08 切换到横屏模式 继续播放播放00:00/01:0801:08全屏 倍速播放中  0.5倍  0.75倍  1.0倍  1.5倍  2.0倍  超清  流畅  您的浏览器不支持 video 标签 继续观看 推荐 5 个 AI 笔记开源神器，起飞了啊。 观看更多,推荐 5 个 AI 笔记开源神器，起飞了啊。逛逛GitHub已关注点赞在看               视频详情          04Open-NoteBookGoogle Notebook LM 的替代品。AI 驱动的高效创作，接入各大厂商的 AI 大模型。输入关键词，就能自动总结文献要点。PDF/EPUB/视频/音频等20+格式一键转文字分析，变成你的笔记。还能姜笔记秒变成播客，自定义语言与风格。开源地址：https://github.com/lfnovo/open-notebook05纯文本文件记事本和待办事项一款基于纯文本文件的笔记和待办事项管理器。支持 Markdown 语法，轻松创建格式丰富的笔记，插入图片、链接等，提升笔记的可读性和组织性。  除了笔记，还内置了待办事项管理功能，轻松创建和管理待办事项列表，设置截止日期和优先级，有效提升你的工作效率和时间管理能力。而且 QOwnNotes 支持与 Nextcloud 和 ownCloud 云端同步，无论是在电脑上还是手机上，随时随地访问你的笔记和待办事项。  开源地址：https://github.com/pbek/QOwnNotes05都看到这了，关注下吧。这个公众号历史发布过很多有趣的开源项目，如果你懒得翻文章一个个找，你直接关注微信公众号：逛逛 GitHub ，后台对话聊天就行了。", "images": [], "word_count": 1741, "url": "https://mp.weixin.qq.com/s/9N6YFL8t4JafLG6th6uxGg", "site_type": "微信公众号", "extracted_at": "2025-07-20T14:30:37.111563"}