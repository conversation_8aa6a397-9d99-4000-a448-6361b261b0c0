原理
--
[[预测性编码模型]]

流程
--
在已有的信息下，总结出新信息的核心主题，并判断它是否重要（是否感兴趣或是对理解已有信息是否有帮助），重要则去理解它与已有信息存在什么逻辑关系，新信息内部的各个部分又存在什么逻辑关系。
![[信息之间逻辑关系.png]]

步骤
--
[[框架]]和重构能够理清句子与句子之间的关系，模块与模块之间的关系，从而抓住主干部分，
[[组块]]负责将信息打包，以块为单位识别和记忆

技巧
--
1. 先整体后细节（先整理出大框架，再整理小框架，将信息逐步精细的处理）
2. 简化框架，抓住主干（根据信息之间的关系，将表达相同内容的信息整合到一起，注意表达不同内容的信息）
3. 看更多的内容（看更多的内容才对整体有个更完整的轮廓，更容易把握整体）
4. 阅读效率取决于你已有的心智框架（对某一领域越熟悉，对该领域的新信息的预测就会越准确，阅读效率就会越快，应用于主题阅读）
