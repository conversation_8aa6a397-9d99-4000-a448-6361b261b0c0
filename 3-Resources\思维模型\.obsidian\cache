{"files": {"并列型金字塔.md": {"mtime": 1598180918962.879, "size": 82, "hash": "c6732e272ff0f583a6b5e9647120a7ede807631be8f277b214dabc3792b1ca51"}, "立体思维、结构化思维.md": {"mtime": 1598198022494.771, "size": 179, "hash": "492d3f07f8224fa211a668bef0bc8b10c392e7b25657710d78a11c0a38ac2b05"}, "线性思维.md": {"mtime": 1597991270718.071, "size": 121, "hash": "6b203942e989410c65e9564096f7fa812f2aaad898619659d15799bcd5383184"}, "输入/Bjork记忆原理.md": {"mtime": 1596643458944.1077, "size": 823, "hash": "3f31f351bd5ae8fae81fa4109ac68d46ef9771f2b203359df8de3331f807cd47"}, "输入/FCR阅读法.md": {"mtime": 1596643465075.4087, "size": 1098, "hash": "d0bafd4e034c332fe244a314dba423bdae3d0322deed9ef132586b8b02e178a8"}, "输入/QR3T学习法.md": {"mtime": 1596645233294.692, "size": 1075, "hash": "82d067c88e8bb7131f4b479c6d27774a1a19c6bd70f325abf0d506043c4f0103"}, "输入/主动学习方法.md": {"mtime": 1596643435605.4453, "size": 679, "hash": "ffcf778489222add588fd653a4aea3769df9ac2b8ad912bb30dd265de0b4e4f0"}, "输入/信息之间逻辑关系.png": {"mtime": 1595864276527.3232, "size": 340684, "hash": "458df41b9257e7f3d563f1d74fee6cdf9210624721337d473584003d440ea958"}, "输入/信息存储流程图.png": {"mtime": 1595860908932.805, "size": 233914, "hash": "f78ffd8af16f41f75939a229017dfa116fa830feca11db805ec58d0625af45d1"}, "输入/信息提取流程图.png": {"mtime": 1595860970670.6824, "size": 79795, "hash": "bde5a661b26355e7d04815e96cdb7aeff9e26f99160043db070855a000e04806"}, "输入/反馈.md": {"mtime": 1596638427752.0598, "size": 0, "hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, "输入/唤醒状态.md": {"mtime": 1595863966430.3425, "size": 0, "hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, "输入/大脑休息.md": {"mtime": 1596639530188.7395, "size": 0, "hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, "输入/大脑存储和提取信息的过程.md": {"mtime": 1595864530236.1619, "size": 418, "hash": "5f55ae54a1701c0172a45a05081fbbbc59f6df8aabbe395dfa925bbabcedbfb9"}, "输入/大脑的投入强度.md": {"mtime": 1596558383897.9377, "size": 202, "hash": "5d67711bfcb0043900b0e8c55a78127dfab4f1e433b3562e2466d98ecb86e104"}, "输入/学习.md": {"mtime": 1596643465956.0886, "size": 1347, "hash": "729af9cbbe80b739a900356d5666f0c463e732f7764e43240b0919a7a6c7d3a1"}, "输入/康奈尔笔记法.md": {"mtime": 1596643465090.3694, "size": 1098, "hash": "5b7e548da5b19e33b68d78630c1207e020928c734c873740ff971e78508d79be"}, "输入/康奈尔笔记法模板.png": {"mtime": 1596593591984.5488, "size": 25298, "hash": "6a1d34ef7f0fcc180d39caad4c6cfcd541ad7ffd65d7e4acf4ada9741eb2ea94"}, "输入/心流状态.md": {"mtime": 1595863979697.9094, "size": 0, "hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, "输入/敏感化.md": {"mtime": 1596474609508.578, "size": 342, "hash": "f20015a4033cf4b9277e04fc7f390b31b4b0af9cab899b99f4d5bfe7e216fe74"}, "输入/睡眠.md": {"mtime": 1596643329010.8906, "size": 64, "hash": "e538369e8334d65390a826312246f67e632648e513dd6aee4b44e153348e55e5"}, "输入/知识.md": {"mtime": 1596638093327.6523, "size": 966, "hash": "5282b1f06063fd750c13af022b882d8090c0492cf7065439dc91a4c0e753a91b"}, "输入/知识卡片.md": {"mtime": 1596638296714.473, "size": 1091, "hash": "fa30a72b789c77e2dd75422788623e93d3912f0ef4d2bbfb2d34da28468c0aee"}, "输入/神经元.md": {"mtime": 1596529642875.2388, "size": 338, "hash": "97e0b7d623323bc0a993230ecd078931a95ebfe44681f9f87d3bb7b640a05605"}, "输入/笔记.md": {"mtime": 1596643462860.7036, "size": 186, "hash": "146c022ec77f6c2144f02725f056b37a63c9c6aa21ce103479bd31c42525dd09"}, "输入/组块.md": {"mtime": 1596213080678.392, "size": 462, "hash": "8f2fff126dccac7cc3dde77fe45d5ce7070004e361aa9fd28bebf66a03bca109"}, "输入/聚焦.md": {"mtime": 1596643463978.304, "size": 428, "hash": "e8a1cd419e7739ae470af721f04cdab52693d4011ff878bbff65cdd7723adb57"}, "输入/费曼学习法.md": {"mtime": 1596645046725.8235, "size": 363, "hash": "d68d55ffec677553d9bab816335561d71cc86be52e8829ed4f4afe9c9c729f2e"}, "输入/预测性编码模型.md": {"mtime": 1595864533587.2058, "size": 1113, "hash": "9825993a681b9383f2c78c2083b2bfea9266da59b67ade266826a859b94ee837"}, "输入/预测性编码模型图.png": {"mtime": 1595690961202.6443, "size": 173555, "hash": "603e4f54179216595a5475de3ed5a577a69b47bb0a0d44af61e0e3225625f227"}, "输入/高效阅读与学习法.md": {"mtime": 1596643466872.1592, "size": 175, "hash": "a696bffe567e1b141b35cac9ea38ac42c1ea52f3fac00c6740f5d8125d547677"}, "递进型金字塔.md": {"mtime": 1598197687526.1714, "size": 348, "hash": "a89ed9c49f53e2d81fee7c52065a0079e6ab95e43f8f65d3914cb81bd9e3d17b"}, "金字塔原则.md": {"mtime": 1598197640848.1465, "size": 1133, "hash": "f2e3319bec4614d1d22751d081283610ac610423908270960b23adc25750efef"}}, "metadata": {"c6732e272ff0f583a6b5e9647120a7ede807631be8f277b214dabc3792b1ca51": {"links": [], "embeds": [], "tags": [], "headings": []}, "492d3f07f8224fa211a668bef0bc8b10c392e7b25657710d78a11c0a38ac2b05": {"links": [{"line": 0, "link": "线性思维", "original": "[[线性思维]]", "displayText": "", "beforeContext": "将", "afterContext": "里的信息打散后再按照一定的结构将它们重组，用一种更加方便理解接受的方式"}, {"line": 2, "link": "金字塔原则", "original": "[[金字塔原则]]", "displayText": "", "beforeContext": "原则：", "afterContext": ""}, {"line": 6, "link": "WYH模型", "original": "[[WYH模型]]", "displayText": "", "beforeContext": "", "afterContext": ""}], "embeds": [], "tags": [], "headings": [{"line": 4, "heading": "模型", "level": 2}]}, "6b203942e989410c65e9564096f7fa812f2aaad898619659d15799bcd5383184": {"links": [], "embeds": [], "tags": [], "headings": []}, "3f31f351bd5ae8fae81fa4109ac68d46ef9771f2b203359df8de3331f807cd47": {"links": [{"line": 13, "link": "大脑的投入强度", "original": "[[大脑的投入强度]]", "displayText": "", "beforeContext": "影响因素：", "afterContext": "当提取信息非常费力时，即低提取强度，需要付出努力提取它，这个努力的过程会给大脑发信号，这个信息是重要的，大脑就会将它保留下来，反之，当你提取信息容易时，即高提取强度，就会缺失这个过程，则有可能被大脑当做垃圾清除掉，这个过程在睡眠过程中完成"}, {"line": 13, "link": "睡眠", "original": "[[睡眠]]", "displayText": "", "beforeContext": "影响因素：大脑的投入强度当提取信息非常费力时，即低提取强度，需要付出努力提取它，这个努力的过程会给大脑发信号，这个信息是重要的，大脑就会将它保留下来，反之，当你提取信息容易时，即高提取强度，就会缺失这个过程，则有可能被大脑当做垃圾清除掉，这个过程在", "afterContext": "过程中完成"}], "embeds": [], "tags": [], "headings": [{"line": 0, "heading": "概念", "level": 2}, {"line": 5, "heading": "原理", "level": 2}]}, "d0bafd4e034c332fe244a314dba423bdae3d0322deed9ef132586b8b02e178a8": {"links": [{"line": 0, "link": "预测性编码模型", "original": "[[预测性编码模型]]", "displayText": "", "beforeContext": "原理：", "afterContext": ""}, {"line": 5, "link": "组块", "original": "[[组块]]", "displayText": "", "beforeContext": "步骤：框架化和重构能够理清句子与句子之间的关系，模块与模块之间的关系从，而抓住主干部分，", "afterContext": "负责将信息打包，以块为单位识别和记忆"}], "embeds": [{"line": 2, "link": "信息之间逻辑关系.png", "original": "![[信息之间逻辑关系.png]]", "beforeContext": "流程：在已有的信息下，总结出新信息的核心主题，并判断它是否重要（是否感兴趣或是对理解已有信息是否有帮助），重要则去理解它与已有信息存在什么逻辑关系，新信息内部的各个部分又存在什么逻辑关系。", "afterContext": ""}], "tags": [], "headings": []}, "82d067c88e8bb7131f4b479c6d27774a1a19c6bd70f325abf0d506043c4f0103": {"links": [{"line": 2, "link": "敏感化", "original": "[[敏感化]]", "displayText": "", "beforeContext": "why：", "afterContext": "how：主动学习方法"}, {"line": 2, "link": "主动学习方法", "original": "[[主动学习方法]]", "displayText": "", "beforeContext": "why：敏感化how：", "afterContext": ""}, {"line": 7, "link": "神经元", "original": "[[神经元]]", "displayText": "", "beforeContext": "why：", "afterContext": " 知识how：学习策略"}, {"line": 7, "link": "知识", "original": "[[知识]]", "displayText": "", "beforeContext": "why：神经元 ", "afterContext": "how：学习策略"}, {"line": 7, "link": "学习", "original": "[[学习]]", "displayText": "", "beforeContext": "why：神经元 知识how：", "afterContext": "策略"}, {"line": 12, "link": "笔记", "original": "[[笔记]]", "displayText": "", "beforeContext": "用", "afterContext": "对知识，进行提炼浓缩"}, {"line": 16, "link": "反馈", "original": "[[反馈]]", "displayText": "", "beforeContext": "用自己的话把，知识讲出来，有", "afterContext": "则更好目的："}, {"line": 19, "link": "学习", "original": "[[学习]]", "displayText": "", "beforeContext": "不看原文的情况下，强迫自己去回想和提取来进行", "afterContext": "策略的间隔测试"}, {"line": 30, "link": "睡眠", "original": "[[睡眠]]", "displayText": "", "beforeContext": "保持良好的", "afterContext": ""}, {"line": 31, "link": "复盘", "original": "[[复盘]]", "displayText": "", "beforeContext": "采取「127法则」进行复述和间隔测试（当天晚上复述一次，第二天晚上复述一次，每周定一个时间对当周内容做一个总的回顾", "afterContext": "，在实践中不断的调试出适合自己的频率和规律）"}, {"line": 33, "link": "费曼学习法", "original": "[[费曼学习法]]", "displayText": "", "beforeContext": "", "afterContext": ""}], "embeds": [], "tags": [], "headings": [{"line": 0, "heading": "Question问题", "level": 2}, {"line": 5, "heading": "Rehearsal加工", "level": 2}, {"line": 10, "heading": "Take note笔记", "level": 2}, {"line": 14, "heading": "Tell复述", "level": 2}, {"line": 21, "heading": "Think调用", "level": 2}]}, "ffcf778489222add588fd653a4aea3769df9ac2b8ad912bb30dd265de0b4e4f0": {"links": [{"line": 2, "link": "FCR阅读法", "original": "[[FCR阅读法]]", "displayText": "", "beforeContext": "通读-大致了解内容主干，方便寻找和搭框架，原理：", "afterContext": "问题驱动-原理：聚焦主题阅读-当从文章中得到问题的答案不满意，可以在网上寻找资料或者相关主题的书籍，将它彻底攻克"}, {"line": 2, "link": "聚焦", "original": "[[聚焦]]", "displayText": "", "beforeContext": "通读-大致了解内容主干，方便寻找和搭框架，原理：FCR阅读法问题驱动-原理：", "afterContext": "主题阅读-当从文章中得到问题的答案不满意，可以在网上寻找资料或者相关主题的书籍，将它彻底攻克"}], "embeds": [], "tags": [], "headings": [{"line": 0, "heading": "步骤", "level": 2}, {"line": 6, "heading": "技巧", "level": 2}]}, "458df41b9257e7f3d563f1d74fee6cdf9210624721337d473584003d440ea958": {"links": [], "embeds": [], "tags": [], "headings": []}, "f78ffd8af16f41f75939a229017dfa116fa830feca11db805ec58d0625af45d1": {"links": [], "embeds": [], "tags": [], "headings": []}, "bde5a661b26355e7d04815e96cdb7aeff9e26f99160043db070855a000e04806": {"links": [], "embeds": [], "tags": [], "headings": []}, "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855": {"links": [], "embeds": [], "tags": [], "headings": []}, "5f55ae54a1701c0172a45a05081fbbbc59f6df8aabbe395dfa925bbabcedbfb9": {"links": [], "embeds": [{"line": 4, "link": "信息存储流程图.png", "original": "![[信息存储流程图.png]]", "beforeContext": "", "afterContext": ""}, {"line": 4, "link": "信息提取流程图.png", "original": "![[信息提取流程图.png]]", "beforeContext": "", "afterContext": ""}], "tags": [], "headings": []}, "5d67711bfcb0043900b0e8c55a78127dfab4f1e433b3562e2466d98ecb86e104": {"links": [], "embeds": [], "tags": [], "headings": []}, "729af9cbbe80b739a900356d5666f0c463e732f7764e43240b0919a7a6c7d3a1": {"links": [{"line": 0, "link": "神经元", "original": "[[神经元]]", "displayText": "", "beforeContext": "学习的本质：", "afterContext": "节点之间的新联系"}, {"line": 3, "link": "知识", "original": "[[知识]]", "displayText": "", "beforeContext": "学习就是获取新", "afterContext": "的过程，也就是在大脑里构建神经元的新联系"}, {"line": 5, "link": "神经元", "original": "[[神经元]]", "displayText": "", "beforeContext": "学习方式：在学习新知识时，回想联想旧知识，将新知识和旧知识建立初步连接，复习旧知识时，通过这个联系回溯联想新知识来双向激活强化连接，更好的记住新知识依据：", "afterContext": "的特性"}, {"line": 13, "link": "大脑的投入强度", "original": "[[大脑的投入强度]]", "displayText": "", "beforeContext": "点对点-新信息和旧信息建立联系，没有联系则努力建立相关性，就算是生搬硬凑，先建立连接记忆，后理清逻辑（努力的过程是增加", "afterContext": "）"}, {"line": 17, "link": "Bjork记忆原理", "original": "[[Bjork记忆原理]]", "displayText": "", "beforeContext": "间隔测试依据：", "afterContext": "间隔：学习一个东西不要短时间内不断的去学，而是每隔一段时间，忘的差不多，处于低提取强度时，再去学习和复习提高存储强度测试：学习知识时，把原文摘抄下来，会提高提取强度，因此只记录下关键词，复习时根据关键词将原文努力回想拼凑起来，降低提取强度，提高存储强度"}], "embeds": [], "tags": [], "headings": [{"line": 10, "heading": "学习策略", "level": 2}]}, "5b7e548da5b19e33b68d78630c1207e020928c734c873740ff971e78508d79be": {"links": [{"line": 11, "link": "学习", "original": "[[学习]]", "displayText": "", "beforeContext": "原理：提炼过程中是", "afterContext": "策略中的深度加工和组块，不断地将笔记内容跟旧知识建立联系，提炼出几个浓缩的关键词，再提炼成一句总结的话，将总结的话建立对笔记内容的映射展开过程中是学习策略中的间隔测试，通过关键词来回想记忆笔记内容，通过总结来代表这一页笔记这是一个双向互相强化连接的过程"}, {"line": 11, "link": "组块", "original": "[[组块]]", "displayText": "", "beforeContext": "原理：提炼过程中是学习策略中的深度加工和", "afterContext": "，不断地将笔记内容跟旧知识建立联系，提炼出几个浓缩的关键词，再提炼成一句总结的话，将总结的话建立对笔记内容的映射展开过程中是学习策略中的间隔测试，通过关键词来回想记忆笔记内容，通过总结来代表这一页笔记这是一个双向互相强化连接的过程"}, {"line": 11, "link": "学习", "original": "[[学习]]", "displayText": "", "beforeContext": "原理：提炼过程中是学习策略中的深度加工和组块，不断地将笔记内容跟旧知识建立联系，提炼出几个浓缩的关键词，再提炼成一句总结的话，将总结的话建立对笔记内容的映射展开过程中是", "afterContext": "策略中的间隔测试，通过关键词来回想记忆笔记内容，通过总结来代表这一页笔记这是一个双向互相强化连接的过程"}], "embeds": [{"line": 0, "link": "康奈尔笔记法模板.png", "original": "![[康奈尔笔记法模板.png]]", "beforeContext": "", "afterContext": ""}], "tags": [], "headings": []}, "6a1d34ef7f0fcc180d39caad4c6cfcd541ad7ffd65d7e4acf4ada9741eb2ea94": {"links": [], "embeds": [], "tags": [], "headings": []}, "f20015a4033cf4b9277e04fc7f390b31b4b0af9cab899b99f4d5bfe7e216fe74": {"links": [], "embeds": [], "tags": [], "headings": [{"line": 0, "heading": "敏感化", "level": 2}]}, "e538369e8334d65390a826312246f67e632648e513dd6aee4b44e153348e55e5": {"links": [], "embeds": [], "tags": [], "headings": []}, "5282b1f06063fd750c13af022b882d8090c0492cf7065439dc91a4c0e753a91b": {"links": [{"line": 7, "link": "预测性编码模型", "original": "[[预测性编码模型]]", "displayText": "", "beforeContext": "对于新知识的学习效率，取决于已有的背景储备，因为吸收的部分是信息的「新-旧联系」 相似", "afterContext": ""}], "embeds": [], "tags": [], "headings": []}, "fa30a72b789c77e2dd75422788623e93d3912f0ef4d2bbfb2d34da28468c0aee": {"links": [], "embeds": [], "tags": [], "headings": [{"line": 0, "heading": "模块", "level": 2}]}, "97e0b7d623323bc0a993230ecd078931a95ebfe44681f9f87d3bb7b640a05605": {"links": [], "embeds": [], "tags": [], "headings": [{"line": 0, "heading": "特性", "level": 2}]}, "146c022ec77f6c2144f02725f056b37a63c9c6aa21ce103479bd31c42525dd09": {"links": [{"line": 3, "link": "康奈尔笔记法", "original": "[[康奈尔笔记法]]", "displayText": "", "beforeContext": "", "afterContext": ""}, {"line": 4, "link": "知识卡片", "original": "[[知识卡片]]", "displayText": "", "beforeContext": "", "afterContext": ""}], "embeds": [], "tags": [], "headings": []}, "8f2fff126dccac7cc3dde77fe45d5ce7070004e361aa9fd28bebf66a03bca109": {"links": [], "embeds": [], "tags": [], "headings": []}, "e8a1cd419e7739ae470af721f04cdab52693d4011ff878bbff65cdd7723adb57": {"links": [{"line": 2, "link": "敏感化", "original": "[[敏感化]]", "displayText": "", "beforeContext": "原理：", "afterContext": ""}, {"line": 7, "link": "主动学习方法", "original": "[[主动学习方法]]", "displayText": "", "beforeContext": "作用：", "afterContext": "在任何情况下，主动学习必然优于被动学习，没有任何意外"}], "embeds": [], "tags": [], "headings": [{"line": 0, "heading": "聚焦", "level": 2}]}, "d68d55ffec677553d9bab816335561d71cc86be52e8829ed4f4afe9c9c729f2e": {"links": [], "embeds": [], "tags": [], "headings": []}, "9825993a681b9383f2c78c2083b2bfea9266da59b67ade266826a859b94ee837": {"links": [{"line": 17, "link": "心流状态", "original": "[[心流状态]]", "displayText": "", "beforeContext": "如何利用：始终在脑子里放着一个预测框架来对信息进行处理，如果符合预测则进入", "afterContext": "，不符合预测则进入唤醒状态，都能有效的提升阅读效率和理解信息"}, {"line": 17, "link": "唤醒状态", "original": "[[唤醒状态]]", "displayText": "", "beforeContext": "如何利用：始终在脑子里放着一个预测框架来对信息进行处理，如果符合预测则进入心流状态，不符合预测则进入", "afterContext": "，都能有效的提升阅读效率和理解信息"}], "embeds": [{"line": 4, "link": "预测性编码模型图.png", "original": "![[预测性编码模型图.png]]", "beforeContext": "", "afterContext": ""}], "tags": [], "headings": [{"line": 0, "heading": "what", "level": 2}, {"line": 11, "heading": "how", "level": 2}]}, "603e4f54179216595a5475de3ed5a577a69b47bb0a0d44af61e0e3225625f227": {"links": [], "embeds": [], "tags": [], "headings": []}, "a696bffe567e1b141b35cac9ea38ac42c1ea52f3fac00c6740f5d8125d547677": {"links": [{"line": 0, "link": "大脑存储和提取信息的过程", "original": "[[大脑存储和提取信息的过程]]", "displayText": "", "beforeContext": "了解", "afterContext": ""}, {"line": 4, "link": "预测性编码模型", "original": "[[预测性编码模型]]", "displayText": "", "beforeContext": "原理：", "afterContext": "方法：FCR阅读法"}, {"line": 4, "link": "FCR阅读法", "original": "[[FCR阅读法]]", "displayText": "", "beforeContext": "原理：预测性编码模型方法：", "afterContext": ""}, {"line": 9, "link": "QR3T学习法", "original": "[[QR3T学习法]]", "displayText": "", "beforeContext": "模型：", "afterContext": ""}], "embeds": [], "tags": [], "headings": [{"line": 2, "heading": "高效阅读", "level": 2}, {"line": 7, "heading": "高效学习方法", "level": 2}]}, "a89ed9c49f53e2d81fee7c52065a0079e6ab95e43f8f65d3914cb81bd9e3d17b": {"links": [{"line": 4, "link": "并列型金字塔", "original": "[[并列型金字塔]]", "displayText": "", "beforeContext": "前提（1~2个）：必须是一个事实，如果是观点则需要具有说服力的论据（使用", "afterContext": "）↓分析：对前提进行评论↓推论（1~2个）：根据分析得到结果导向↓结论/观点"}], "embeds": [], "tags": [], "headings": [{"line": 2, "heading": "模式", "level": 2}]}, "f2e3319bec4614d1d22751d081283610ac610423908270960b23adc25750efef": {"links": [{"line": 7, "link": "预测性编码模型", "original": "[[预测性编码模型]]", "displayText": "", "beforeContext": "信息是有层级的，且越重要越核心的信息位于越高层级-增加可预测性（原理：", "afterContext": "）"}, {"line": 12, "link": "FCR阅读法", "original": "[[FCR阅读法]]", "displayText": "", "beforeContext": "按照低层级信息的内在逻辑关系，分门别类分点进行阐述跟", "afterContext": "的组块本质一致"}, {"line": 30, "link": "并列型金字塔", "original": "[[并列型金字塔]]", "displayText": "", "beforeContext": "", "afterContext": "递进型金字塔"}, {"line": 30, "link": "递进型金字塔", "original": "[[递进型金字塔]]", "displayText": "", "beforeContext": "并列型金字塔", "afterContext": ""}], "embeds": [], "tags": [], "headings": [{"line": 0, "heading": "概念", "level": 2}, {"line": 21, "heading": "口诀", "level": 2}, {"line": 28, "heading": "类型", "level": 2}]}}, "algorithmVersion": 9}