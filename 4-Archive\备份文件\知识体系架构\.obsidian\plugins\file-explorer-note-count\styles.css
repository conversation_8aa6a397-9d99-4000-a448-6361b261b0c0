.nav-folder-title[data-count]::after {
    content: attr(data-count);
    display: inline-block;
    position: relative;
    font-size: calc(100% * 0.8);
    margin-right: 4px;
    /* border-radius: 3px; */
    padding: 2px 0;
    /* background-color: var(--background-secondary-alt); */
    transition: opacity 100ms ease-in-out;
}
.nav-folder-title.oz-root-hidden[data-count]::after {
    display: none;
}

body:not(.oz-show-all-num)
    .nav-folder:not(.is-collapsed)
    > .nav-folder-title.oz-with-subfolder[data-count]:not([data-path='/'])::after {
    opacity: 0;
}

.nav-folder-title[data-count='0']::after {
    display: none;
}

.nav-folder-title-content {
    flex-grow: 1;
}
