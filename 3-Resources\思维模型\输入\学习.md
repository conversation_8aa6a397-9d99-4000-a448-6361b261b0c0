学习的本质：
[[神经元]]节点之间的新联系

学习就是获取新[[知识]]的过程，也就是在大脑里构建神经元的新联系

学习方式：
在学习新知识时，回想联想旧知识，将新知识和旧知识建立初步连接，
复习旧知识时，通过这个联系回溯联想新知识来双向激活强化连接，更好的记住新知识
依据：[[神经元]]的特性

学习策略
--
深度加工
1. 点对点-新信息和旧信息建立联系，没有联系则努力建立相关性，就算是生搬硬凑，先建立连接记忆，后理清逻辑（努力的过程是增加[[大脑的投入强度]]）
2. 点对面-对某个知识领域有大致的框架时，将新的信息放入到框架当中，新信息与多个旧信息建立联系
	eg：当对计算机知识有大致的框架，学习到新的人物，则需要将他归类到相应的语言和所做的软件或成就

间隔测试
依据：[[Bjork记忆原理]]
间隔：学习一个东西不要短时间内不断的去学，而是每隔一段时间，忘的差不多，处于低提取强度时，再去学习和复习提高存储强度
测试：学习知识时，把原文摘抄下来，会提高提取强度，因此只记录下关键词，复习时根据关键词将原文努力回想拼凑起来，降低提取强度，提高存储强度

