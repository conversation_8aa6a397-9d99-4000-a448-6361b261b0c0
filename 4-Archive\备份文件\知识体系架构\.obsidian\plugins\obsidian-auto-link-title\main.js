/*
THIS IS A GENERATED/BUNDLED FILE BY ROLLUP
if you want to view the source visit the plugins github repository
*/

'use strict';

var obsidian = require('obsidian');

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON><PERSON>ENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

const DEFAULT_SETTINGS = {
    regex: /^(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})$/i,
    lineRegex: /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi,
    linkRegex: /^\[([^\[\]]*)\]\((https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})\)$/i,
    linkLineRegex: /\[([^\[\]]*)\]\((https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})\)/gi,
    imageRegex: /\.(gif|jpe?g|tiff?|png|webp|bmp|tga|psd|ai)$/i,
    shouldReplaceSelection: true,
    enhanceDefaultPaste: true,
    websiteBlacklist: "",
};
class AutoLinkTitleSettingTab extends obsidian.PluginSettingTab {
    constructor(app, plugin) {
        super(app, plugin);
        this.plugin = plugin;
    }
    display() {
        let { containerEl } = this;
        containerEl.empty();
        new obsidian.Setting(containerEl)
            .setName("Enhance Default Paste")
            .setDesc("Fetch the link title when pasting a link in the editor with the default paste command")
            .addToggle((val) => val
            .setValue(this.plugin.settings.enhanceDefaultPaste)
            .onChange((value) => __awaiter(this, void 0, void 0, function* () {
            console.log(value);
            this.plugin.settings.enhanceDefaultPaste = value;
            yield this.plugin.saveSettings();
        })));
        new obsidian.Setting(containerEl)
            .setName("Replace Selection")
            .setDesc("Whether to replace a text selection with link and fetched title")
            .addToggle((val) => val
            .setValue(this.plugin.settings.shouldReplaceSelection)
            .onChange((value) => __awaiter(this, void 0, void 0, function* () {
            console.log(value);
            this.plugin.settings.shouldReplaceSelection = value;
            yield this.plugin.saveSettings();
        })));
        new obsidian.Setting(containerEl)
            .setName("Website Blacklist")
            .setDesc("List of strings (comma separated) that disable autocompleting website titles. Can be URLs or arbitrary text.")
            .addTextArea((val) => val
            .setValue(this.plugin.settings.websiteBlacklist)
            .setPlaceholder("localhost, tiktok.com")
            .onChange((value) => __awaiter(this, void 0, void 0, function* () {
            this.plugin.settings.websiteBlacklist = value;
            yield this.plugin.saveSettings();
        })));
    }
}

class EditorExtensions {
    static getSelectedText(editor) {
        if (!editor.somethingSelected()) {
            let wordBoundaries = this.getWordBoundaries(editor);
            editor.setSelection(wordBoundaries.start, wordBoundaries.end);
        }
        return editor.getSelection();
    }
    static cursorWithinBoundaries(cursor, match) {
        let startIndex = match.index;
        let endIndex = match.index + match[0].length;
        return startIndex <= cursor.ch && cursor.ch <= endIndex;
    }
    static getWordBoundaries(editor) {
        let cursor = editor.getCursor();
        // If its a normal URL token this is not a markdown link
        // In this case we can simply overwrite the link boundaries as-is
        let lineText = editor.getLine(cursor.line);
        // First check if we're in a link
        let linksInLine = lineText.matchAll(DEFAULT_SETTINGS.linkLineRegex);
        for (let match of linksInLine) {
            if (this.cursorWithinBoundaries(cursor, match)) {
                return {
                    start: { line: cursor.line, ch: match.index },
                    end: { line: cursor.line, ch: match.index + match[0].length },
                };
            }
        }
        // If not, check if we're in just a standard ol' URL.
        let urlsInLine = lineText.matchAll(DEFAULT_SETTINGS.lineRegex);
        for (let match of urlsInLine) {
            if (this.cursorWithinBoundaries(cursor, match)) {
                return {
                    start: { line: cursor.line, ch: match.index },
                    end: { line: cursor.line, ch: match.index + match[0].length },
                };
            }
        }
        return {
            start: cursor,
            end: cursor,
        };
    }
    static getEditorPositionFromIndex(content, index) {
        let substr = content.substr(0, index);
        let l = 0;
        let offset = -1;
        let r = -1;
        for (; (r = substr.indexOf("\n", r + 1)) !== -1; l++, offset = r)
            ;
        offset += 1;
        let ch = content.substr(offset, index - offset).length;
        return { line: l, ch: ch };
    }
}

class CheckIf {
    static isMarkdownLinkAlready(editor) {
        let cursor = editor.getCursor();
        // Check if the characters before the url are ]( to indicate a markdown link
        var titleEnd = editor.getRange({ ch: cursor.ch - 2, line: cursor.line }, { ch: cursor.ch, line: cursor.line });
        return titleEnd == "](";
    }
    static isAfterQuote(editor) {
        let cursor = editor.getCursor();
        // Check if the characters before the url are " or ' to indicate we want the url directly
        // This is common in elements like <a href="linkhere"></a>
        var beforeChar = editor.getRange({ ch: cursor.ch - 1, line: cursor.line }, { ch: cursor.ch, line: cursor.line });
        return beforeChar == "\"" || beforeChar == "'";
    }
    static isUrl(text) {
        let urlRegex = new RegExp(DEFAULT_SETTINGS.regex);
        return urlRegex.test(text);
    }
    static isImage(text) {
        let imageRegex = new RegExp(DEFAULT_SETTINGS.imageRegex);
        return imageRegex.test(text);
    }
    static isLinkedUrl(text) {
        let urlRegex = new RegExp(DEFAULT_SETTINGS.linkRegex);
        return urlRegex.test(text);
    }
}

const electronPkg = require("electron");
function blank(text) {
    return text === undefined || text === null || text === "";
}
function notBlank(text) {
    return !blank(text);
}
// async wrapper to load a url and settle on load finish or fail
function load(window, url) {
    return __awaiter(this, void 0, void 0, function* () {
        return new Promise((resolve, reject) => {
            window.webContents.on("did-finish-load", (event) => resolve(event));
            window.webContents.on("did-fail-load", (event) => reject(event));
            window.loadURL(url);
        });
    });
}
function electronGetPageTitle(url) {
    return __awaiter(this, void 0, void 0, function* () {
        const { remote } = electronPkg;
        const { BrowserWindow } = remote;
        try {
            const window = new BrowserWindow({
                width: 1000,
                height: 600,
                webPreferences: {
                    webSecurity: false,
                    nodeIntegration: true,
                    images: false,
                },
                show: false,
            });
            window.webContents.setAudioMuted(true);
            yield load(window, url);
            try {
                const title = window.webContents.getTitle();
                window.destroy();
                if (notBlank(title)) {
                    return title;
                }
                else {
                    return url;
                }
            }
            catch (ex) {
                window.destroy();
                return url;
            }
        }
        catch (ex) {
            console.error(ex);
            return "Site Unreachable";
        }
    });
}
function nonElectronGetPageTitle(url) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const html = yield obsidian.request({ url });
            const doc = new DOMParser().parseFromString(html, "text/html");
            const title = doc.querySelectorAll("title")[0];
            if (title == null || blank(title === null || title === void 0 ? void 0 : title.innerText)) {
                // If site is javascript based and has a no-title attribute when unloaded, use it.
                var noTitle = title === null || title === void 0 ? void 0 : title.getAttr("no-title");
                if (notBlank(noTitle)) {
                    return noTitle;
                }
                // Otherwise if the site has no title/requires javascript simply return Title Unknown
                return url;
            }
            return title.innerText;
        }
        catch (ex) {
            console.error(ex);
            return "Site Unreachable";
        }
    });
}
function getUrlFinalSegment(url) {
    try {
        const segments = new URL(url).pathname.split('/');
        const last = segments.pop() || segments.pop(); // Handle potential trailing slash
        return last;
    }
    catch (_) {
        return "File";
    }
}
function tryGetFileType(url) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const response = yield fetch(url, { method: "HEAD" });
            // Ensure site returns an ok status code before scraping
            if (!response.ok) {
                return "Site Unreachable";
            }
            // Ensure site is an actual HTML page and not a pdf or 3 gigabyte video file.
            let contentType = response.headers.get("content-type");
            if (!contentType.includes("text/html")) {
                return getUrlFinalSegment(url);
            }
            return null;
        }
        catch (err) {
            return null;
        }
    });
}
function getPageTitle(url) {
    return __awaiter(this, void 0, void 0, function* () {
        // If we're on Desktop use the Electron scraper
        if (!(url.startsWith("http") || url.startsWith("https"))) {
            url = "https://" + url;
        }
        // Try to do a HEAD request to see if the site is reachable and if it's an HTML page
        // If we error out due to CORS, we'll just try to scrape the page anyway.
        let fileType = yield tryGetFileType(url);
        if (fileType) {
            return fileType;
        }
        if (electronPkg != null) {
            return electronGetPageTitle(url);
        }
        else {
            return nonElectronGetPageTitle(url);
        }
    });
}

class AutoLinkTitle extends obsidian.Plugin {
    onload() {
        return __awaiter(this, void 0, void 0, function* () {
            console.log("loading obsidian-auto-link-title");
            yield this.loadSettings();
            this.blacklist = this.settings.websiteBlacklist.split(",").map(s => s.trim()).filter(s => s.length > 0);
            // Listen to paste event
            this.pasteFunction = this.pasteUrlWithTitle.bind(this);
            this.addCommand({
                id: "auto-link-title-paste",
                name: "Paste URL and auto fetch title",
                editorCallback: (editor) => this.manualPasteUrlWithTitle(editor),
                hotkeys: [],
            });
            this.addCommand({
                id: "auto-link-title-normal-paste",
                name: "Normal paste (no fetching behavior)",
                editorCallback: (editor) => this.normalPaste(editor),
                hotkeys: [
                    {
                        modifiers: ["Mod", "Shift"],
                        key: "v",
                    },
                ],
            });
            this.registerEvent(this.app.workspace.on("editor-paste", this.pasteFunction));
            this.addCommand({
                id: "enhance-url-with-title",
                name: "Enhance existing URL with link and title",
                editorCallback: (editor) => this.addTitleToLink(editor),
                hotkeys: [
                    {
                        modifiers: ["Mod", "Shift"],
                        key: "e",
                    },
                ],
            });
            this.addSettingTab(new AutoLinkTitleSettingTab(this.app, this));
        });
    }
    addTitleToLink(editor) {
        // Only attempt fetch if online
        if (!navigator.onLine)
            return;
        let selectedText = (EditorExtensions.getSelectedText(editor) || "").trim();
        // If the cursor is on a raw html link, convert to a markdown link and fetch title
        if (CheckIf.isUrl(selectedText)) {
            this.convertUrlToTitledLink(editor, selectedText);
        }
        // If the cursor is on the URL part of a markdown link, fetch title and replace existing link title
        else if (CheckIf.isLinkedUrl(selectedText)) {
            var link = this.getUrlFromLink(selectedText);
            this.convertUrlToTitledLink(editor, link);
        }
    }
    normalPaste(editor) {
        return __awaiter(this, void 0, void 0, function* () {
            let clipboardText = yield navigator.clipboard.readText();
            if (clipboardText === null || clipboardText === "")
                return;
            editor.replaceSelection(clipboardText);
        });
    }
    // Simulate standard paste but using editor.replaceSelection with clipboard text since we can't seem to dispatch a paste event.
    manualPasteUrlWithTitle(editor) {
        return __awaiter(this, void 0, void 0, function* () {
            // Only attempt fetch if online
            if (!navigator.onLine) {
                editor.replaceSelection(clipboardText);
                return;
            }
            var clipboardText = yield navigator.clipboard.readText();
            if (clipboardText == null || clipboardText == "")
                return;
            // If its not a URL, we return false to allow the default paste handler to take care of it.
            // Similarly, image urls don't have a meaningful <title> attribute so downloading it
            // to fetch the title is a waste of bandwidth.
            if (!CheckIf.isUrl(clipboardText) || CheckIf.isImage(clipboardText)) {
                editor.replaceSelection(clipboardText);
                return;
            }
            let selectedText = (EditorExtensions.getSelectedText(editor) || "").trim();
            if (selectedText && !this.settings.shouldReplaceSelection) {
                // If there is selected text and shouldReplaceSelection is false, do not fetch title
                editor.replaceSelection(clipboardText);
                return;
            }
            // If it looks like we're pasting the url into a markdown link already, don't fetch title
            // as the user has already probably put a meaningful title, also it would lead to the title
            // being inside the link.
            if (CheckIf.isMarkdownLinkAlready(editor) || CheckIf.isAfterQuote(editor)) {
                editor.replaceSelection(clipboardText);
                return;
            }
            // At this point we're just pasting a link in a normal fashion, fetch its title.
            this.convertUrlToTitledLink(editor, clipboardText);
            return;
        });
    }
    pasteUrlWithTitle(clipboard, editor) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.settings.enhanceDefaultPaste) {
                return;
            }
            // Only attempt fetch if online
            if (!navigator.onLine)
                return;
            let clipboardText = clipboard.clipboardData.getData("text/plain");
            if (clipboardText === null || clipboardText === "")
                return;
            // If its not a URL, we return false to allow the default paste handler to take care of it.
            // Similarly, image urls don't have a meaningful <title> attribute so downloading it
            // to fetch the title is a waste of bandwidth.
            if (!CheckIf.isUrl(clipboardText) || CheckIf.isImage(clipboardText)) {
                return;
            }
            let selectedText = (EditorExtensions.getSelectedText(editor) || "").trim();
            if (selectedText && !this.settings.shouldReplaceSelection) {
                // If there is selected text and shouldReplaceSelection is false, do not fetch title
                return;
            }
            // We've decided to handle the paste, stop propagation to the default handler.
            clipboard.stopPropagation();
            clipboard.preventDefault();
            // If it looks like we're pasting the url into a markdown link already, don't fetch title
            // as the user has already probably put a meaningful title, also it would lead to the title
            // being inside the link.
            if (CheckIf.isMarkdownLinkAlready(editor) || CheckIf.isAfterQuote(editor)) {
                editor.replaceSelection(clipboardText);
                return;
            }
            // At this point we're just pasting a link in a normal fashion, fetch its title.
            this.convertUrlToTitledLink(editor, clipboardText);
            return;
        });
    }
    isBlacklisted(url) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.loadSettings();
            this.blacklist = this.settings.websiteBlacklist.split(/,|\n/).map(s => s.trim()).filter(s => s.length > 0);
            return this.blacklist.some(site => url.contains(site));
        });
    }
    convertUrlToTitledLink(editor, url) {
        return __awaiter(this, void 0, void 0, function* () {
            if (yield this.isBlacklisted(url)) {
                let domain = new URL(url).hostname;
                editor.replaceSelection(`[${domain}](${url})`);
                return;
            }
            // Generate a unique id for find/replace operations for the title.
            const pasteId = `Fetching Title#${this.createBlockHash()}`;
            // Instantly paste so you don't wonder if paste is broken
            editor.replaceSelection(`[${pasteId}](${url})`);
            // Fetch title from site, replace Fetching Title with actual title
            const title = yield this.fetchUrlTitle(url);
            const escapedTitle = this.escapeMarkdown(title);
            const text = editor.getValue();
            const start = text.indexOf(pasteId);
            if (start < 0) {
                console.log(`Unable to find text "${pasteId}" in current editor, bailing out; link ${url}`);
            }
            else {
                const end = start + pasteId.length;
                const startPos = EditorExtensions.getEditorPositionFromIndex(text, start);
                const endPos = EditorExtensions.getEditorPositionFromIndex(text, end);
                editor.replaceRange(escapedTitle, startPos, endPos);
            }
        });
    }
    escapeMarkdown(text) {
        var unescaped = text.replace(/\\(\*|_|`|~|\\)/g, '$1'); // unescape any "backslashed" character
        var escaped = unescaped.replace(/(\*|_|`|~|\\)/g, '\\$1'); // escape *, _, `, ~, \
        return escaped;
    }
    fetchUrlTitle(url) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const title = yield getPageTitle(url);
                return title.replace(/(\r\n|\n|\r)/gm, "").trim();
            }
            catch (error) {
                // console.error(error)
                return "Site Unreachable";
            }
        });
    }
    getUrlFromLink(link) {
        let urlRegex = new RegExp(DEFAULT_SETTINGS.linkRegex);
        return urlRegex.exec(link)[2];
    }
    // Custom hashid by @shabegom
    createBlockHash() {
        let result = "";
        var characters = "abcdefghijklmnopqrstuvwxyz0123456789";
        var charactersLength = characters.length;
        for (var i = 0; i < 4; i++) {
            result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        return result;
    }
    onunload() {
        console.log("unloading obsidian-auto-link-title");
    }
    loadSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            this.settings = Object.assign({}, DEFAULT_SETTINGS, yield this.loadData());
        });
    }
    saveSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.saveData(this.settings);
        });
    }
}

module.exports = AutoLinkTitle;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
