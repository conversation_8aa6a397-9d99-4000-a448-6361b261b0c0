# 提示词排版技术详解

## 发现背景
- **发现时间**：2025-07-20
- **来源**：三篇微信公众号文章
- **核心发现**：通过提示词主动控制文章排版，实现专业级排版效果

## 文章来源
1. **基础方法**：https://mp.weixin.qq.com/s/1Y-ef9ti-sVqmF5sV8LVMw
   - 标题：告别公众号排版劝退症！豆包/Deepseek免费「代码级」一键排版！
   - 作者：嘉哥的AIGC

2. **工具化升级**：https://mp.weixin.qq.com/s/lHlnzsqroWXGDNaV3-PuKg
   - 标题：上次的"AI排版公众号魔法"，现在我让Gemini-2.5-Pro把它做成了工具！
   - 作者：嘉哥的AIGC

3. **完整工作流**：https://mp.weixin.qq.com/s/6ONbB8JOrlySKO1MOfQs6A
   - 标题：我用AI+MCP一套工作流，搞定选题、写作、发布全自动化
   - 作者：生财有术

## 核心技术原理

### 排版痛点分析
1. **传统排版困难**：
   - 手动排版耗时费力
   - 需要专业排版知识
   - 工具复杂且多数收费
   - 排版效果不稳定

2. **AI排版优势**：
   - 一键生成专业排版
   - 免费使用（豆包/DeepSeek）
   - 可反复调整优化
   - 支持多种风格

### 技术实现方式

#### 方法一：超级提示词（基础版）
**核心原理**：通过精心设计的提示词，让AI理解微信公众号的HTML/CSS渲染规则

**关键要素**：
1. **微信编辑器兼容性**：
   - 只支持内联样式（style属性）
   - 严格的HTML标签白名单
   - 特定的CSS属性限制

2. **排版设计原则**：
   - 多样化风格支持
   - 极致可读性优化
   - 移动端适配

**使用流程**：
```
1. 复制超级提示词到豆包/DeepSeek
2. 输入要排版的文章内容
3. 指定排版风格要求
4. AI生成HTML代码
5. 在浏览器预览效果
6. 复制粘贴到公众号
```

#### 方法二：Web工具（进化版）
**技术升级**：将提示词封装成Web应用

**优势**：
- 告别复制粘贴提示词
- 可视化操作界面
- 实时预览效果
- 一键复制功能

**技术栈**：
- 前端：HTML/CSS/JavaScript
- 后端：Node.js + Express
- AI接口：支持多种大模型

#### 方法三：MCP扩展（自动化版）
**终极方案**：集成到扣子空间的完整工作流

**功能模块**：
1. **公众号爆文写作工作流**
2. **公众号配图工作流**
3. **公众号排版工作流**
4. **公众号发布工作流**

## 超级提示词详解

### 核心提示词结构
```markdown
# 系统提示词：微信公众号文章排版专家

## 【指令核心】
您是一名具备微信公众号编辑器"内核级"理解能力和卓越可读性设计理念的排版专家。

## 【微信编辑器兼容性规则】
### 支持的HTML标签（白名单）：
- 结构与内容：div, section, p, h1-h6
- 文本修饰：span, strong, em, del, code, pre
- 列表：ul, ol, li
- 引用：blockquote
- 链接与媒体：a, img, figure, figcaption
- 表格：table, thead, tbody, tr, td, th
- 分隔符：hr
- SVG：<svg>（静态图形）

### 支持的CSS属性：
- 字体与文本：font-size, color, background-color, font-weight, text-align, line-height
- 盒模型：padding, margin, border, border-radius
- 布局：display: block/inline-block/table
- 尺寸：width, height, max-width: 100%

### 禁止使用（黑名单）：
- JavaScript相关：<script>, onclick等
- 外部资源：<link>, background-image外部URL
- 复杂布局：float, position, flexbox, grid
- 动态效果：transition, animation, box-shadow
```

### 排版风格系统
**多主题支持**：
1. **商务/专业主题**：
   - 色彩：低饱和度、沉稳色调
   - 间距：规整间距，行高1.75
   - 标题：清晰分隔线装饰

2. **科技/极客主题**：
   - 色彩：深色背景，高亮强调色
   - 间距：略紧凑的行高1.7
   - 标题：鲜明边框或背景色

3. **生活/情感主题**：
   - 色彩：柔和温暖色彩
   - 间距：宽松行高1.8-2.0
   - 标题：圆角柔和边框

4. **教育/教程主题**：
   - 色彩：高对比度，突出重点
   - 结构：明确标题层次
   - 间距：适中偏大的行高

## 实际应用案例

### 案例1：基础排版
**输入**：普通文章内容
**输出**：带有标题层级、段落间距、颜色搭配的HTML代码
**效果**：专业美观的排版效果

### 案例2：风格调整
**指令**："在以上基础上增加些符号"
**结果**：AI自动添加装饰性符号，增强视觉效果

### 案例3：受众定制
**指令**："再修改为适合小学生阅读"
**结果**：调整为更活泼、有趣的排版风格

## 技术进化路径

### 第一阶段：提示词方法
- **特点**：手动操作，需要复制粘贴
- **优势**：免费，效果好
- **不足**：操作繁琐

### 第二阶段：Web工具
- **特点**：界面化操作，一键生成
- **优势**：用户体验好，开源可定制
- **技术**：GitHub开源项目

### 第三阶段：工作流集成
- **特点**：全自动化，一句话完成
- **优势**：效率最高，功能最全
- **平台**：扣子空间MCP扩展

## 开源工具资源

### GitHub项目
- **项目地址**：https://github.com/zhgarylu/wechat-formatter
- **功能**：微信公众号AI排版工具
- **技术栈**：Node.js + OpenAI API

### 本地部署步骤
```bash
# 1. 克隆项目
git clone https://github.com/zhgarylu/wechat-formatter.git

# 2. 安装依赖
npm install express openai dotenv cors

# 3. 配置API密钥
# 创建.env文件，添加：
OPENAI_API_KEY="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# 4. 启动服务
node server.js
```

## 实用技巧

### 提示词优化技巧
1. **明确风格要求**：具体描述期望的排版风格
2. **分步调整**：先生成基础版本，再逐步优化
3. **多轮对话**：通过对话不断完善排版效果

### 排版质量提升
1. **色彩搭配**：确保足够的对比度
2. **字体层级**：建立清晰的标题体系
3. **间距控制**：合理的段落和元素间距
4. **移动适配**：确保在手机端的显示效果

### 常见问题解决
1. **样式丢失**：确保使用内联样式
2. **兼容性问题**：避免使用黑名单中的属性
3. **效果不佳**：调整提示词描述更具体

## 应用价值

### 效率提升
- **时间节省**：从小时级降到分钟级
- **质量保证**：专业级排版效果
- **成本降低**：免费替代付费工具

### 适用场景
- **个人博主**：提升内容视觉效果
- **企业宣传**：标准化品牌形象
- **教育培训**：制作专业教学材料
- **营销团队**：批量内容生产

## 相关资源
- [[3-Resources/AI工具/智能体/Coze自动编写发布公众号文章智能体]] - 基础版本
- [[3-Resources/AI工具/智能体/Coze公众号文章生成工作流-排版优化版]] - 升级版本
- [[3-Resources/AI工具/提示词收集与管理实践]] - 提示词管理

## 标签
#提示词 #排版 #公众号 #HTML #CSS #豆包 #DeepSeek #自动化

---
*整理时间：2025-07-20*
*分类：3-Resources/AI工具*
*重要程度：⭐⭐⭐⭐⭐*
*状态：完整技术方案，可直接应用*
