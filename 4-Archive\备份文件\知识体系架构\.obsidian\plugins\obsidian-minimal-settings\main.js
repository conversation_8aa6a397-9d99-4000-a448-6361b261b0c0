/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// main.ts
__export(exports, {
  default: () => MinimalTheme
});
var import_obsidian = __toModule(require("obsidian"));
var MinimalTheme = class extends import_obsidian.Plugin {
  onload() {
    return __async(this, null, function* () {
      yield this.loadSettings();
      this.addSettingTab(new MinimalSettingTab(this.app, this));
      this.addStyle();
      let media = window.matchMedia("(prefers-color-scheme: dark)");
      let updateSystemTheme = () => {
        if (media.matches && this.settings.useSystemTheme) {
          console.log("Dark mode active");
          this.updateDarkStyle();
        } else if (this.settings.useSystemTheme) {
          console.log("Light mode active");
          this.updateLightStyle();
        }
      };
      media.addEventListener("change", updateSystemTheme);
      this.register(() => media.removeEventListener("change", updateSystemTheme));
      updateSystemTheme();
      let settingsUpdate = () => {
        const fontSize = this.app.vault.getConfig("baseFontSize");
        this.settings.textNormal = fontSize;
        if (this.app.vault.getConfig("foldHeading")) {
          this.settings.folding = true;
          this.saveData(this.settings);
          console.log("Folding is on");
        } else {
          this.settings.folding = false;
          this.saveData(this.settings);
          console.log("Folding is off");
        }
        document.body.classList.toggle("minimal-folding", this.settings.folding);
        if (this.app.vault.getConfig("showLineNumber")) {
          this.settings.lineNumbers = true;
          this.saveData(this.settings);
          console.log("Line numbers are on");
        } else {
          this.settings.lineNumbers = false;
          this.saveData(this.settings);
          console.log("Line numbers are off");
        }
        document.body.classList.toggle("minimal-line-nums", this.settings.lineNumbers);
        if (this.app.vault.getConfig("readableLineLength")) {
          this.settings.readableLineLength = true;
          this.saveData(this.settings);
          console.log("Readable line length is on");
        } else {
          this.settings.readableLineLength = false;
          this.saveData(this.settings);
          console.log("Readable line length is off");
        }
        document.body.classList.toggle("minimal-readable", this.settings.readableLineLength);
        document.body.classList.toggle("minimal-readable-off", !this.settings.readableLineLength);
      };
      let sidebarUpdate = () => {
        const sidebarEl = document.getElementsByClassName("mod-left-split")[0];
        const ribbonEl = document.getElementsByClassName("side-dock-ribbon")[0];
        if (sidebarEl && ribbonEl && this.app.vault.getConfig("theme") == "moonstone" && this.settings.lightStyle == "minimal-light-contrast") {
          sidebarEl.addClass("theme-dark");
          ribbonEl.addClass("theme-dark");
        } else if (sidebarEl && ribbonEl) {
          sidebarEl.removeClass("theme-dark");
          ribbonEl.removeClass("theme-dark");
        }
      };
      this.registerEvent(app.vault.on("config-changed", settingsUpdate));
      this.registerEvent(app.workspace.on("css-change", sidebarUpdate));
      settingsUpdate();
      app.workspace.onLayoutReady(() => {
        sidebarUpdate();
      });
      const lightStyles = ["minimal-light", "minimal-light-tonal", "minimal-light-contrast", "minimal-light-white"];
      const darkStyles = ["minimal-dark", "minimal-dark-tonal", "minimal-dark-black"];
      const imgGridStyles = ["img-grid", "img-grid-ratio", "img-nogrid"];
      const tableWidthStyles = ["table-100", "table-default-width", "table-wide", "table-max"];
      const iframeWidthStyles = ["iframe-100", "iframe-default-width", "iframe-wide", "iframe-max"];
      const imgWidthStyles = ["img-100", "img-default-width", "img-wide", "img-max"];
      const mapWidthStyles = ["map-100", "map-default-width", "map-wide", "map-max"];
      const chartWidthStyles = ["chart-100", "chart-default-width", "chart-wide", "chart-max"];
      const theme = ["moonstone", "obsidian"];
      this.addCommand({
        id: "increase-body-font-size",
        name: "Increase body font size",
        callback: () => {
          this.settings.textNormal = this.settings.textNormal + 0.5;
          this.saveData(this.settings);
          this.setFontSize();
        }
      });
      this.addCommand({
        id: "decrease-body-font-size",
        name: "Decrease body font size",
        callback: () => {
          this.settings.textNormal = this.settings.textNormal - 0.5;
          this.saveData(this.settings);
          this.setFontSize();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dark-cycle",
        name: "Cycle between dark mode styles",
        callback: () => {
          this.settings.darkStyle = darkStyles[(darkStyles.indexOf(this.settings.darkStyle) + 1) % darkStyles.length];
          this.saveData(this.settings);
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-cycle",
        name: "Cycle between light mode styles",
        callback: () => {
          this.settings.lightStyle = lightStyles[(lightStyles.indexOf(this.settings.lightStyle) + 1) % lightStyles.length];
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-hidden-borders",
        name: "Toggle sidebar borders",
        callback: () => {
          this.settings.bordersToggle = !this.settings.bordersToggle;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-colorful-headings",
        name: "Toggle colorful headings",
        callback: () => {
          this.settings.colorfulHeadings = !this.settings.colorfulHeadings;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-minimal-focus-mode",
        name: "Toggle focus mode",
        callback: () => {
          this.settings.focusMode = !this.settings.focusMode;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-minimal-colorful-frame",
        name: "Toggle colorful window frame",
        callback: () => {
          this.settings.colorfulFrame = !this.settings.colorfulFrame;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-table-width",
        name: "Cycle between table width options",
        callback: () => {
          this.settings.tableWidth = tableWidthStyles[(tableWidthStyles.indexOf(this.settings.tableWidth) + 1) % tableWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-image-width",
        name: "Cycle between image width options",
        callback: () => {
          this.settings.imgWidth = imgWidthStyles[(imgWidthStyles.indexOf(this.settings.imgWidth) + 1) % imgWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-iframe-width",
        name: "Cycle between iframe width options",
        callback: () => {
          this.settings.iframeWidth = iframeWidthStyles[(iframeWidthStyles.indexOf(this.settings.iframeWidth) + 1) % iframeWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-chart-width",
        name: "Cycle between chart width options",
        callback: () => {
          this.settings.chartWidth = chartWidthStyles[(chartWidthStyles.indexOf(this.settings.chartWidth) + 1) % chartWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-map-width",
        name: "Cycle between map width options",
        callback: () => {
          this.settings.mapWidth = mapWidthStyles[(mapWidthStyles.indexOf(this.settings.mapWidth) + 1) % mapWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-minimal-img-grid",
        name: "Toggle image grids",
        callback: () => {
          this.settings.imgGrid = !this.settings.imgGrid;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-minimal-switch",
        name: "Switch between light and dark mode",
        callback: () => {
          this.settings.theme = theme[(theme.indexOf(this.settings.theme) + 1) % theme.length];
          this.saveData(this.settings);
          this.updateTheme();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-default",
        name: "Use light mode (default)",
        callback: () => {
          this.settings.lightStyle = "minimal-light";
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-white",
        name: "Use light mode (all white)",
        callback: () => {
          this.settings.lightStyle = "minimal-light-white";
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-tonal",
        name: "Use light mode (low contrast)",
        callback: () => {
          this.settings.lightStyle = "minimal-light-tonal";
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-contrast",
        name: "Use light mode (high contrast)",
        callback: () => {
          this.settings.lightStyle = "minimal-light-contrast";
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dark-default",
        name: "Use dark mode (default)",
        callback: () => {
          this.settings.darkStyle = "minimal-dark";
          this.saveData(this.settings);
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dark-tonal",
        name: "Use dark mode (low contrast)",
        callback: () => {
          this.settings.darkStyle = "minimal-dark-tonal";
          this.saveData(this.settings);
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dark-black",
        name: "Use dark mode (true black)",
        callback: () => {
          this.settings.darkStyle = "minimal-dark-black";
          this.saveData(this.settings);
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-atom-light",
        name: "Switch light color scheme to Atom (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-atom-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-ayu-light",
        name: "Switch light color scheme to Ayu (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-ayu-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-catppuccin-light",
        name: "Switch light color scheme to Catppuccin (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-catppuccin-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-default-light",
        name: "Switch light color scheme to default (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-default-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-gruvbox-light",
        name: "Switch light color scheme to Gruvbox (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-gruvbox-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-everforest-light",
        name: "Switch light color scheme to Everforest (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-everforest-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-macos-light",
        name: "Switch light color scheme to macOS (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-macos-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-notion-light",
        name: "Switch light color scheme to Notion (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-notion-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-nord-light",
        name: "Switch light color scheme to Nord (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-nord-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-rose-pine-light",
        name: "Switch light color scheme to Ros\xE9 Pine (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-rose-pine-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-solarized-light",
        name: "Switch light color scheme to Solarized (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-solarized-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-things-light",
        name: "Switch light color scheme to Things (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-things-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-atom-dark",
        name: "Switch color scheme to Atom (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-atom-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-ayu-dark",
        name: "Switch color scheme to Ayu (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-ayu-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-catppuccin-dark",
        name: "Switch dark color scheme to Catppuccin (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-catppuccin-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dracula-dark",
        name: "Switch color scheme to Dracula (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-dracula-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-default-dark",
        name: "Switch dark color scheme to default (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-default-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-gruvbox-dark",
        name: "Switch dark color scheme to Gruvbox (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-gruvbox-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-everforest-dark",
        name: "Switch dark color scheme to Everforest (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-everforest-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-macos-dark",
        name: "Switch light color scheme to macOS (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-macos-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-nord-dark",
        name: "Switch dark color scheme to Nord (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-nord-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-notion-dark",
        name: "Switch dark color scheme to Notion (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-notion-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-rose-pine-dark",
        name: "Switch color scheme to Ros\xE9 Pine (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-rose-pine-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-solarized-dark",
        name: "Switch dark color scheme to Solarized (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-solarized-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-things-dark",
        name: "Switch dark color scheme to Things (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-things-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dev-block-width",
        name: "Dev \u2014 Show block widths",
        callback: () => {
          this.settings.devBlockWidth = !this.settings.devBlockWidth;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.refresh();
    });
  }
  onunload() {
    console.log("Unloading Minimal Theme Settings plugin");
  }
  loadSettings() {
    return __async(this, null, function* () {
      this.settings = Object.assign(DEFAULT_SETTINGS, yield this.loadData());
    });
  }
  saveSettings() {
    return __async(this, null, function* () {
      yield this.saveData(this.settings);
    });
  }
  refresh() {
    this.updateStyle();
  }
  addStyle() {
    const css = document.createElement("style");
    css.id = "minimal-theme";
    document.getElementsByTagName("head")[0].appendChild(css);
    document.body.classList.add("minimal-theme");
    this.updateStyle();
  }
  setFontSize() {
    this.app.vault.setConfig("baseFontSize", this.settings.textNormal);
    this.app.updateFontSize();
  }
  updateStyle() {
    this.removeStyle();
    document.body.addClass(this.settings.darkScheme);
    document.body.addClass(this.settings.lightScheme);
    document.body.classList.toggle("borders-none", !this.settings.bordersToggle);
    document.body.classList.toggle("colorful-headings", this.settings.colorfulHeadings);
    document.body.classList.toggle("colorful-frame", this.settings.colorfulFrame);
    document.body.classList.toggle("colorful-active", this.settings.colorfulActiveStates);
    document.body.classList.toggle("minimal-focus-mode", this.settings.focusMode);
    document.body.classList.toggle("links-int-on", this.settings.underlineInternal);
    document.body.classList.toggle("links-ext-on", this.settings.underlineExternal);
    document.body.classList.toggle("system-shade", this.settings.useSystemTheme);
    document.body.classList.toggle("full-width-media", this.settings.fullWidthMedia);
    document.body.classList.toggle("img-grid", this.settings.imgGrid);
    document.body.classList.toggle("minimal-dev-block-width", this.settings.devBlockWidth);
    document.body.classList.toggle("minimal-status-off", !this.settings.minimalStatus);
    document.body.classList.toggle("full-file-names", !this.settings.trimNames);
    document.body.classList.toggle("labeled-nav", this.settings.labeledNav);
    document.body.classList.toggle("minimal-folding", this.settings.folding);
    document.body.removeClass("table-wide", "table-max", "table-100", "table-default-width", "iframe-wide", "iframe-max", "iframe-100", "iframe-default-width", "img-wide", "img-max", "img-100", "img-default-width", "chart-wide", "chart-max", "chart-100", "chart-default-width", "map-wide", "map-max", "map-100", "map-default-width");
    document.body.addClass(this.settings.chartWidth);
    document.body.addClass(this.settings.tableWidth);
    document.body.addClass(this.settings.imgWidth);
    document.body.addClass(this.settings.iframeWidth);
    document.body.addClass(this.settings.mapWidth);
    const el = document.getElementById("minimal-theme");
    if (!el)
      throw "minimal-theme element not found!";
    else {
      el.innerText = "body.minimal-theme{--font-ui-small:" + this.settings.textSmall + "px;--line-height:" + this.settings.lineHeight + ";--line-width:" + this.settings.lineWidth + "rem;--line-width-wide:" + this.settings.lineWidthWide + "rem;--max-width:" + this.settings.maxWidth + "%;--font-editor-override:" + this.settings.editorFont + ";";
    }
  }
  refreshSystemTheme() {
    const isDarkMode = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches;
    if (isDarkMode && this.settings.useSystemTheme) {
      console.log("Dark mode active");
      this.updateDarkStyle();
    } else if (this.settings.useSystemTheme) {
      console.log("Light mode active");
      this.updateLightStyle();
    }
  }
  updateDarkStyle() {
    document.body.removeClass("theme-light", "minimal-dark", "minimal-dark-tonal", "minimal-dark-black");
    document.body.addClass(this.settings.darkStyle);
    this.app.setTheme("obsidian");
    this.app.vault.setConfig("theme", "obsidian");
    this.app.workspace.trigger("css-change");
  }
  updateLightStyle() {
    document.body.removeClass("theme-dark", "minimal-light", "minimal-light-tonal", "minimal-light-contrast", "minimal-light-white");
    document.body.addClass(this.settings.lightStyle);
    this.app.setTheme("moonstone");
    this.app.vault.setConfig("theme", "moonstone");
    this.app.workspace.trigger("css-change");
  }
  updateDarkScheme() {
    document.body.removeClass("minimal-atom-dark", "minimal-ayu-dark", "minimal-catppuccin-dark", "minimal-default-dark", "minimal-dracula-dark", "minimal-everforest-dark", "minimal-gruvbox-dark", "minimal-macos-dark", "minimal-nord-dark", "minimal-notion-dark", "minimal-rose-pine-dark", "minimal-solarized-dark", "minimal-things-dark");
    document.body.addClass(this.settings.darkScheme);
  }
  updateLightScheme() {
    document.body.removeClass("minimal-atom-light", "minimal-ayu-light", "minimal-catppuccin-light", "minimal-default-light", "minimal-everforest-light", "minimal-gruvbox-light", "minimal-macos-light", "minimal-nord-light", "minimal-notion-light", "minimal-rose-pine-light", "minimal-solarized-light", "minimal-things-light");
    document.body.addClass(this.settings.lightScheme);
  }
  updateTheme() {
    this.app.setTheme(this.settings.theme);
    this.app.vault.setConfig("theme", this.settings.theme);
    this.app.workspace.trigger("css-change");
  }
  removeStyle() {
    document.body.removeClass("minimal-light", "minimal-light-tonal", "minimal-light-contrast", "minimal-light-white", "minimal-dark", "minimal-dark-tonal", "minimal-dark-black");
    document.body.addClass(this.settings.lightStyle, this.settings.darkStyle);
  }
};
var DEFAULT_SETTINGS = {
  theme: "moonstone",
  lightStyle: "minimal-light",
  darkStyle: "minimal-dark",
  lightScheme: "minimal-default-light",
  darkScheme: "minimal-default-dark",
  editorFont: "",
  lineHeight: 1.5,
  lineWidth: 40,
  lineWidthWide: 50,
  maxWidth: 88,
  textNormal: 16,
  textSmall: 13,
  imgGrid: false,
  imgWidth: "img-default-width",
  tableWidth: "table-default-width",
  iframeWidth: "iframe-default-width",
  mapWidth: "map-default-width",
  chartWidth: "chart-default-width",
  colorfulHeadings: false,
  colorfulFrame: false,
  colorfulActiveStates: false,
  trimNames: true,
  labeledNav: false,
  fullWidthMedia: true,
  bordersToggle: true,
  minimalStatus: true,
  focusMode: false,
  underlineInternal: true,
  underlineExternal: true,
  useSystemTheme: false,
  folding: true,
  lineNumbers: false,
  readableLineLength: false,
  devBlockWidth: false
};
var MinimalSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app2, plugin) {
    super(app2, plugin);
    this.plugin = plugin;
  }
  display() {
    let { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h3", { text: "Minimal Theme Settings" });
    const mainDesc = containerEl.createEl("p");
    mainDesc.appendText("Need help? Explore the ");
    mainDesc.appendChild(createEl("a", {
      text: "Minimal documentation",
      href: "https://minimal.guide"
    }));
    mainDesc.appendText(" or visit the ");
    mainDesc.appendChild(createEl("strong", {
      text: "#minimal"
    }));
    mainDesc.appendText(" channel in the official Obsidian Discord. You can support continued development by ");
    mainDesc.appendChild(createEl("a", {
      text: "buying me a coffee",
      href: "https://www.buymeacoffee.com/kepano"
    }));
    mainDesc.appendText(" \u2615");
    containerEl.createEl("br");
    containerEl.createEl("h3", { text: "Color scheme" });
    const colorDesc = containerEl.createEl("p");
    colorDesc.appendChild(createEl("span", {
      text: "To create a completely custom color scheme use "
    }));
    colorDesc.appendChild(createEl("a", {
      text: "Style Settings plugin",
      href: "obsidian://show-plugin?id=obsidian-style-settings"
    }));
    colorDesc.appendText(".");
    new import_obsidian.Setting(containerEl).setName("Light mode color scheme").setDesc("Preset color options for light mode").addDropdown((dropdown) => dropdown.addOption("minimal-default-light", "Default").addOption("minimal-atom-light", "Atom").addOption("minimal-ayu-light", "Ayu").addOption("minimal-catppuccin-light", "Catppuccin").addOption("minimal-everforest-light", "Everforest").addOption("minimal-gruvbox-light", "Gruvbox").addOption("minimal-macos-light", "macOS").addOption("minimal-nord-light", "Nord").addOption("minimal-notion-light", "Notion").addOption("minimal-rose-pine-light", "Ros\xE9 Pine").addOption("minimal-solarized-light", "Solarized").addOption("minimal-things-light", "Things").setValue(this.plugin.settings.lightScheme).onChange((value) => {
      this.plugin.settings.lightScheme = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.updateLightScheme();
    }));
    new import_obsidian.Setting(containerEl).setName("Light mode background contrast").setDesc("Level of contrast between sidebar and main content").addDropdown((dropdown) => dropdown.addOption("minimal-light", "Default").addOption("minimal-light-white", "All white").addOption("minimal-light-tonal", "Low contrast").addOption("minimal-light-contrast", "High contrast").setValue(this.plugin.settings.lightStyle).onChange((value) => {
      this.plugin.settings.lightStyle = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.updateLightStyle();
    }));
    new import_obsidian.Setting(containerEl).setName("Dark mode color scheme").setDesc("Preset colors options for dark mode").addDropdown((dropdown) => dropdown.addOption("minimal-default-dark", "Default").addOption("minimal-atom-dark", "Atom").addOption("minimal-ayu-dark", "Ayu").addOption("minimal-catppuccin-dark", "Catppuccin").addOption("minimal-dracula-dark", "Dracula").addOption("minimal-everforest-dark", "Everforest").addOption("minimal-gruvbox-dark", "Gruvbox").addOption("minimal-macos-dark", "macOS").addOption("minimal-nord-dark", "Nord").addOption("minimal-notion-dark", "Notion").addOption("minimal-rose-pine-dark", "Ros\xE9 Pine").addOption("minimal-solarized-dark", "Solarized").addOption("minimal-things-dark", "Things").setValue(this.plugin.settings.darkScheme).onChange((value) => {
      this.plugin.settings.darkScheme = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.updateDarkScheme();
    }));
    new import_obsidian.Setting(containerEl).setName("Dark mode background contrast").setDesc("Level of contrast between sidebar and main content").addDropdown((dropdown) => dropdown.addOption("minimal-dark", "Default").addOption("minimal-dark-tonal", "Low contrast").addOption("minimal-dark-black", "True black").setValue(this.plugin.settings.darkStyle).onChange((value) => {
      this.plugin.settings.darkStyle = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.updateDarkStyle();
    }));
    containerEl.createEl("br");
    containerEl.createEl("h3");
    containerEl.createEl("h3", { text: "Features" });
    new import_obsidian.Setting(containerEl).setName("Match system setting for light or dark mode").setDesc("Automatically switch based on your OS setting").addToggle((toggle) => toggle.setValue(this.plugin.settings.useSystemTheme).onChange((value) => {
      this.plugin.settings.useSystemTheme = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refreshSystemTheme();
    }));
    new import_obsidian.Setting(containerEl).setName("Text labels for primary navigation").setDesc("Navigation in left sidebar uses text labels (see documentation for localization support)").addToggle((toggle) => toggle.setValue(this.plugin.settings.labeledNav).onChange((value) => {
      this.plugin.settings.labeledNav = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Colorful window frame").setDesc("The top area of the app uses your accent color").addToggle((toggle) => toggle.setValue(this.plugin.settings.colorfulFrame).onChange((value) => {
      this.plugin.settings.colorfulFrame = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Colorful active states").setDesc("Active file and menu items use your accent color").addToggle((toggle) => toggle.setValue(this.plugin.settings.colorfulActiveStates).onChange((value) => {
      this.plugin.settings.colorfulActiveStates = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Colorful headings").setDesc("Headings use a different color for each size").addToggle((toggle) => toggle.setValue(this.plugin.settings.colorfulHeadings).onChange((value) => {
      this.plugin.settings.colorfulHeadings = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Minimal status bar").setDesc("Use narrow status bar").addToggle((toggle) => toggle.setValue(this.plugin.settings.minimalStatus).onChange((value) => {
      this.plugin.settings.minimalStatus = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Trim file names in sidebars").setDesc("Use ellipses to fit file names on a single line").addToggle((toggle) => toggle.setValue(this.plugin.settings.trimNames).onChange((value) => {
      this.plugin.settings.trimNames = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Workspace borders").setDesc("Display divider lines between workspace elements").addToggle((toggle) => toggle.setValue(this.plugin.settings.bordersToggle).onChange((value) => {
      this.plugin.settings.bordersToggle = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Focus mode").setDesc("Hide tab bar and status bar, hover to display (can be toggled with hotkey)").addToggle((toggle) => toggle.setValue(this.plugin.settings.focusMode).onChange((value) => {
      this.plugin.settings.focusMode = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Underline internal links").setDesc("Show underlines on internal links").addToggle((toggle) => toggle.setValue(this.plugin.settings.underlineInternal).onChange((value) => {
      this.plugin.settings.underlineInternal = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Underline external links").setDesc("Show underlines on external links").addToggle((toggle) => toggle.setValue(this.plugin.settings.underlineExternal).onChange((value) => {
      this.plugin.settings.underlineExternal = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Maximize media").setDesc("Images and videos fill the width of the line").addToggle((toggle) => toggle.setValue(this.plugin.settings.fullWidthMedia).onChange((value) => {
      this.plugin.settings.fullWidthMedia = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    containerEl.createEl("br");
    containerEl.createEl("h3");
    containerEl.createEl("h3", { text: "Layout" });
    const layoutDesc = containerEl.createEl("p");
    layoutDesc.appendChild(createEl("span", {
      text: "The following options require the "
    }));
    layoutDesc.appendChild(createEl("a", {
      text: "Contextual Typography plugin",
      href: "obsidian://show-plugin?id=obsidian-contextual-typography"
    }));
    layoutDesc.appendText(". These options can also be defined on a per-file basis using YAML, ");
    layoutDesc.appendChild(createEl("a", {
      text: "see documentation",
      href: "https://minimal.guide/Features/Block+width"
    }));
    layoutDesc.appendText(" for details.");
    new import_obsidian.Setting(containerEl).setName("Image grids").setDesc("Turn consecutive images into columns \u2014 to make a new row, add an extra line break between images").addToggle((toggle) => toggle.setValue(this.plugin.settings.imgGrid).onChange((value) => {
      this.plugin.settings.imgGrid = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Chart width").setDesc("Default width for chart blocks").addDropdown((dropdown) => dropdown.addOption("chart-default-width", "Default").addOption("chart-wide", "Wide line width").addOption("chart-max", "Maximum line width").addOption("chart-100", "100% pane width").setValue(this.plugin.settings.chartWidth).onChange((value) => {
      this.plugin.settings.chartWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Iframe width").setDesc("Default width for iframe blocks").addDropdown((dropdown) => dropdown.addOption("iframe-default-width", "Default").addOption("iframe-wide", "Wide line width").addOption("iframe-max", "Maximum line width").addOption("iframe-100", "100% pane width").setValue(this.plugin.settings.iframeWidth).onChange((value) => {
      this.plugin.settings.iframeWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Image width").setDesc("Default width for image blocks").addDropdown((dropdown) => dropdown.addOption("img-default-width", "Default").addOption("img-wide", "Wide line width").addOption("img-max", "Maximum line width").addOption("img-100", "100% pane width").setValue(this.plugin.settings.imgWidth).onChange((value) => {
      this.plugin.settings.imgWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Map width").setDesc("Default width for map blocks").addDropdown((dropdown) => dropdown.addOption("map-default-width", "Default").addOption("map-wide", "Wide line width").addOption("map-max", "Maximum line width").addOption("map-100", "100% pane width").setValue(this.plugin.settings.mapWidth).onChange((value) => {
      this.plugin.settings.mapWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Table width").setDesc("Default width for table and Dataview blocks").addDropdown((dropdown) => dropdown.addOption("table-default-width", "Default").addOption("table-wide", "Wide line width").addOption("table-max", "Maximum line width").addOption("table-100", "100% pane width").setValue(this.plugin.settings.tableWidth).onChange((value) => {
      this.plugin.settings.tableWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    containerEl.createEl("br");
    containerEl.createEl("h3");
    containerEl.createEl("h3", { text: "Typography" });
    new import_obsidian.Setting(containerEl).setName("Text font size").setDesc("Used for the main text (default 16)").addText((text) => text.setPlaceholder("16").setValue((this.plugin.settings.textNormal || "") + "").onChange((value) => {
      this.plugin.settings.textNormal = parseFloat(value);
      this.plugin.saveData(this.plugin.settings);
      this.plugin.setFontSize();
    }));
    new import_obsidian.Setting(containerEl).setName("Small font size").setDesc("Used for text in the sidebars and tabs (default 13)").addText((text) => text.setPlaceholder("13").setValue((this.plugin.settings.textSmall || "") + "").onChange((value) => {
      this.plugin.settings.textSmall = parseFloat(value);
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Line height").setDesc("Line height of text (default 1.5)").addText((text) => text.setPlaceholder("1.5").setValue((this.plugin.settings.lineHeight || "") + "").onChange((value) => {
      this.plugin.settings.lineHeight = parseFloat(value);
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Normal line width").setDesc("Number of characters per line (default 40)").addText((text) => text.setPlaceholder("40").setValue((this.plugin.settings.lineWidth || "") + "").onChange((value) => {
      this.plugin.settings.lineWidth = parseInt(value.trim());
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Wide line width").setDesc("Number of characters per line for wide elements (default 50)").addText((text) => text.setPlaceholder("50").setValue((this.plugin.settings.lineWidthWide || "") + "").onChange((value) => {
      this.plugin.settings.lineWidthWide = parseInt(value.trim());
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Maximum line width %").setDesc("Percentage of space inside a pane that a line can fill (default 88)").addText((text) => text.setPlaceholder("88").setValue((this.plugin.settings.maxWidth || "") + "").onChange((value) => {
      this.plugin.settings.maxWidth = parseInt(value.trim());
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Editor font").setDesc("Overrides the text font defined in Obsidian Appearance settings when in edit mode").addText((text) => text.setPlaceholder("").setValue((this.plugin.settings.editorFont || "") + "").onChange((value) => {
      this.plugin.settings.editorFont = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    containerEl.createEl("br");
    containerEl.createEl("h3", { text: "Support development" });
    const donateText = containerEl.createEl("p");
    donateText.appendChild(createEl("span", {
      text: "If you enjoy Minimal, consider "
    }));
    donateText.appendChild(createEl("a", {
      text: "buying me a coffee",
      href: "https://www.buymeacoffee.com/kepano"
    }));
    donateText.appendChild(createEl("span", {
      text: ", and following me on Twitter "
    }));
    donateText.appendChild(createEl("a", {
      text: "@kepano",
      href: "https://twitter.com/kepano"
    }));
    const div = containerEl.createEl("div", {
      cls: "minimal-donation"
    });
    const parser = new DOMParser();
    div.appendChild(createDonateButton("https://www.buymeacoffee.com/kepano", parser.parseFromString(buyMeACoffee, "text/xml").documentElement));
  }
};
var createDonateButton = (link, img) => {
  const a = document.createElement("a");
  a.setAttribute("href", link);
  a.addClass("minimal-donate-button");
  a.appendChild(img);
  return a;
};
var buyMeACoffee = `
<svg width="150" height="42" viewBox="0 0 260 73" style="margin-right:10px" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 11.68C0 5.22932 5.22931 0 11.68 0H248.2C254.651 0 259.88 5.22931 259.88 11.68V61.32C259.88 67.7707 254.651 73 248.2 73H11.68C5.22931 73 0 67.7707 0 61.32V11.68Z" fill="#FFDD00"/>
<path d="M52.2566 24.0078L52.2246 23.9889L52.1504 23.9663C52.1802 23.9915 52.2176 24.0061 52.2566 24.0078Z" fill="#0D0C22"/>
<path d="M52.7248 27.3457L52.6895 27.3556L52.7248 27.3457Z" fill="#0D0C22"/>
<path d="M52.2701 24.0024C52.266 24.0019 52.2619 24.0009 52.258 23.9995C52.2578 24.0022 52.2578 24.0049 52.258 24.0076C52.2624 24.007 52.2666 24.0052 52.2701 24.0024Z" fill="#0D0C22"/>
<path d="M52.2578 24.0094H52.2643V24.0054L52.2578 24.0094Z" fill="#0D0C22"/>
<path d="M52.6973 27.3394L52.7513 27.3086L52.7714 27.2973L52.7897 27.2778C52.7554 27.2926 52.7241 27.3135 52.6973 27.3394Z" fill="#0D0C22"/>
<path d="M52.3484 24.0812L52.2956 24.031L52.2598 24.0115C52.279 24.0454 52.3108 24.0705 52.3484 24.0812Z" fill="#0D0C22"/>
<path d="M39.0684 56.469C39.0262 56.4872 38.9893 56.5158 38.9609 56.552L38.9943 56.5306C39.0169 56.5098 39.0489 56.4853 39.0684 56.469Z" fill="#0D0C22"/>
<path d="M46.7802 54.9518C46.7802 54.9041 46.7569 54.9129 46.7626 55.0826C46.7626 55.0687 46.7683 55.0549 46.7708 55.0417C46.7739 55.0115 46.7764 54.982 46.7802 54.9518Z" fill="#0D0C22"/>
<path d="M45.9844 56.469C45.9422 56.4872 45.9053 56.5158 45.877 56.552L45.9103 56.5306C45.9329 56.5098 45.9649 56.4853 45.9844 56.469Z" fill="#0D0C22"/>
<path d="M33.6307 56.8301C33.5987 56.8023 33.5595 56.784 33.5176 56.7773C33.5515 56.7937 33.5855 56.81 33.6081 56.8226L33.6307 56.8301Z" fill="#0D0C22"/>
<path d="M32.4118 55.6598C32.4068 55.6103 32.3916 55.5624 32.3672 55.519C32.3845 55.5642 32.399 55.6104 32.4106 55.6573L32.4118 55.6598Z" fill="#0D0C22"/>
<path d="M40.623 34.7221C38.9449 35.4405 37.0404 36.2551 34.5722 36.2551C33.5397 36.2531 32.5122 36.1114 31.5176 35.834L33.2247 53.3605C33.2851 54.093 33.6188 54.7761 34.1595 55.2739C34.7003 55.7718 35.4085 56.0482 36.1435 56.048C36.1435 56.048 38.564 56.1737 39.3716 56.1737C40.2409 56.1737 42.8474 56.048 42.8474 56.048C43.5823 56.048 44.2904 55.7716 44.831 55.2737C45.3716 54.7759 45.7052 54.0929 45.7656 53.3605L47.594 33.993C46.7769 33.714 45.9523 33.5286 45.0227 33.5286C43.415 33.5279 42.1196 34.0817 40.623 34.7221Z" fill="white"/>
<path d="M26.2344 27.2449L26.2633 27.2719L26.2821 27.2832C26.2676 27.2688 26.2516 27.2559 26.2344 27.2449Z" fill="#0D0C22"/>
<path d="M55.4906 25.6274L55.2336 24.3307C55.0029 23.1673 54.4793 22.068 53.2851 21.6475C52.9024 21.513 52.468 21.4552 52.1745 21.1768C51.881 20.8983 51.7943 20.4659 51.7264 20.0649C51.6007 19.3289 51.4825 18.5923 51.3537 17.8575C51.2424 17.2259 51.1544 16.5163 50.8647 15.9368C50.4876 15.1586 49.705 14.7036 48.9269 14.4025C48.5282 14.2537 48.1213 14.1278 47.7082 14.0254C45.7642 13.5125 43.7202 13.324 41.7202 13.2165C39.3197 13.084 36.9128 13.1239 34.518 13.3359C32.7355 13.4981 30.8581 13.6942 29.1642 14.3108C28.5451 14.5364 27.9071 14.8073 27.4364 15.2856C26.8587 15.8733 26.6702 16.7821 27.0919 17.515C27.3917 18.0354 27.8996 18.4031 28.4382 18.6463C29.1398 18.9597 29.8726 19.1982 30.6242 19.3578C32.7172 19.8204 34.885 20.0021 37.0233 20.0794C39.3932 20.175 41.767 20.0975 44.1256 19.8474C44.7089 19.7833 45.2911 19.7064 45.8723 19.6168C46.5568 19.5118 46.9961 18.6168 46.7943 17.9933C46.553 17.2479 45.9044 16.9587 45.1709 17.0712C45.0628 17.0882 44.9553 17.1039 44.8472 17.1196L44.7692 17.131C44.5208 17.1624 44.2723 17.1917 44.0238 17.219C43.5105 17.2743 42.9959 17.3195 42.4801 17.3547C41.3249 17.4352 40.1665 17.4722 39.0088 17.4741C37.8712 17.4741 36.7329 17.4421 35.5978 17.3673C35.0799 17.3333 34.5632 17.2902 34.0478 17.2378C33.8134 17.2133 33.5796 17.1875 33.3458 17.1586L33.1233 17.1303L33.0749 17.1234L32.8442 17.0901C32.3728 17.0191 31.9014 16.9374 31.435 16.8387C31.388 16.8283 31.3459 16.8021 31.3157 16.7645C31.2856 16.7269 31.2691 16.6801 31.2691 16.6319C31.2691 16.5837 31.2856 16.5369 31.3157 16.4993C31.3459 16.4617 31.388 16.4356 31.435 16.4251H31.4438C31.848 16.339 32.2553 16.2655 32.6638 16.2014C32.8 16.18 32.9366 16.159 33.0736 16.1385H33.0774C33.3332 16.1215 33.5903 16.0757 33.8448 16.0455C36.0595 15.8151 38.2874 15.7366 40.5128 15.8104C41.5933 15.8419 42.6731 15.9053 43.7485 16.0147C43.9798 16.0386 44.2098 16.0637 44.4399 16.092C44.5279 16.1027 44.6165 16.1153 44.7051 16.1259L44.8836 16.1517C45.404 16.2292 45.9217 16.3233 46.4367 16.4339C47.1997 16.5999 48.1796 16.6539 48.519 17.4898C48.6271 17.7551 48.6761 18.0499 48.7359 18.3283L48.8119 18.6834C48.8139 18.6898 48.8154 18.6963 48.8163 18.7029C48.9961 19.5409 49.176 20.379 49.3562 21.217C49.3694 21.2789 49.3697 21.3429 49.3571 21.4049C49.3445 21.4669 49.3193 21.5257 49.2829 21.5776C49.2466 21.6294 49.2 21.6732 49.146 21.7062C49.092 21.7392 49.0317 21.7608 48.969 21.7695H48.964L48.854 21.7846L48.7453 21.799C48.4009 21.8439 48.056 21.8858 47.7107 21.9247C47.0307 22.0022 46.3496 22.0693 45.6674 22.1259C44.3119 22.2386 42.9536 22.3125 41.5927 22.3477C40.8992 22.3662 40.2059 22.3748 39.5129 22.3735C36.7543 22.3713 33.9981 22.211 31.2578 21.8933C30.9611 21.8581 30.6645 21.8204 30.3678 21.7821C30.5978 21.8116 30.2006 21.7594 30.1202 21.7481C29.9316 21.7217 29.7431 21.6943 29.5545 21.6658C28.9216 21.5709 28.2924 21.454 27.6607 21.3515C26.8971 21.2258 26.1667 21.2887 25.476 21.6658C24.909 21.976 24.4501 22.4518 24.1605 23.0297C23.8626 23.6456 23.7739 24.3163 23.6407 24.9781C23.5074 25.6399 23.3 26.3521 23.3786 27.0315C23.5477 28.4979 24.5728 29.6895 26.0473 29.956C27.4345 30.2074 28.8292 30.4111 30.2276 30.5846C35.7212 31.2574 41.2711 31.3379 46.7818 30.8247C47.2305 30.7828 47.6787 30.7371 48.1262 30.6876C48.266 30.6723 48.4074 30.6884 48.5401 30.7348C48.6729 30.7812 48.7936 30.8566 48.8934 30.9557C48.9932 31.0548 49.0695 31.1749 49.1169 31.3073C49.1642 31.4397 49.1814 31.5811 49.167 31.7209L49.0275 33.0773C48.7463 35.8181 48.4652 38.5587 48.184 41.299C47.8907 44.1769 47.5955 47.0545 47.2984 49.9319C47.2146 50.7422 47.1308 51.5524 47.047 52.3624C46.9666 53.16 46.9552 53.9827 46.8038 54.7709C46.5649 56.0103 45.7258 56.7715 44.5015 57.0499C43.3798 57.3052 42.2339 57.4392 41.0836 57.4497C39.8083 57.4566 38.5336 57.4 37.2583 57.4069C35.897 57.4145 34.2295 57.2887 33.1786 56.2756C32.2553 55.3856 32.1277 53.9921 32.002 52.7872C31.8344 51.192 31.6682 49.5971 31.5036 48.0023L30.5796 39.1344L29.9819 33.3966C29.9718 33.3017 29.9618 33.208 29.9524 33.1125C29.8807 32.428 29.3961 31.758 28.6324 31.7926C27.9788 31.8215 27.2359 32.3771 27.3125 33.1125L27.7557 37.3664L28.672 46.1657C28.9331 48.6652 29.1935 51.165 29.4533 53.6653C29.5036 54.1442 29.5507 54.6244 29.6035 55.1034C29.8908 57.7205 31.8895 59.131 34.3646 59.5282C35.8102 59.7607 37.291 59.8085 38.758 59.8324C40.6386 59.8626 42.538 59.9348 44.3877 59.5942C47.1287 59.0914 49.1853 57.2611 49.4788 54.422C49.5626 53.6024 49.6464 52.7826 49.7302 51.9626C50.0088 49.2507 50.2871 46.5386 50.5649 43.8263L51.4737 34.9641L51.8904 30.9026C51.9112 30.7012 51.9962 30.5118 52.133 30.3625C52.2697 30.2132 52.4509 30.1119 52.6497 30.0736C53.4335 29.9208 54.1827 29.66 54.7402 29.0635C55.6277 28.1138 55.8043 26.8756 55.4906 25.6274ZM26.0071 26.5035C26.019 26.4979 25.997 26.6003 25.9876 26.6481C25.9857 26.5758 25.9895 26.5117 26.0071 26.5035ZM26.0831 27.0918C26.0894 27.0874 26.1083 27.1126 26.1278 27.1428C26.0982 27.1151 26.0794 27.0944 26.0825 27.0918H26.0831ZM26.1579 27.1905C26.185 27.2364 26.1994 27.2653 26.1579 27.1905V27.1905ZM26.3082 27.3125H26.3119C26.3119 27.3169 26.3188 27.3213 26.3214 27.3257C26.3172 27.3208 26.3126 27.3164 26.3075 27.3125H26.3082ZM52.6132 27.1302C52.3317 27.3979 51.9074 27.5224 51.4882 27.5846C46.7868 28.2823 42.0169 28.6355 37.264 28.4796C33.8624 28.3633 30.4967 27.9856 27.129 27.5098C26.799 27.4633 26.4414 27.403 26.2145 27.1597C25.7871 26.7009 25.997 25.777 26.1083 25.2226C26.2101 24.7148 26.405 24.0378 27.009 23.9656C27.9518 23.8549 29.0466 24.2528 29.9794 24.3942C31.1023 24.5656 32.2295 24.7028 33.3609 24.8059C38.1892 25.2459 43.0986 25.1774 47.9056 24.5337C48.7817 24.416 49.6548 24.2792 50.5246 24.1233C51.2996 23.9844 52.1588 23.7236 52.6271 24.5262C52.9482 25.073 52.991 25.8046 52.9413 26.4225C52.926 26.6917 52.8084 26.9448 52.6126 27.1302H52.6132Z" fill="#0D0C22"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M81.1302 40.1929C80.8556 40.7169 80.4781 41.1732 79.9978 41.5604C79.5175 41.9479 78.9571 42.2633 78.3166 42.5062C77.6761 42.7497 77.0315 42.9131 76.3835 42.9964C75.7352 43.0799 75.106 43.0727 74.4963 42.9735C73.8863 42.8749 73.3674 42.6737 72.9408 42.3695L73.4214 37.3779C73.8633 37.2261 74.4197 37.0703 75.0909 36.9107C75.7619 36.7513 76.452 36.6371 77.1613 36.5689C77.8705 36.5003 78.5412 36.5084 79.1744 36.5917C79.8068 36.6753 80.3065 36.8765 80.6725 37.1958C80.8707 37.378 81.0387 37.5754 81.176 37.7883C81.313 38.0011 81.3969 38.2214 81.4276 38.4493C81.5037 39.0875 81.4047 39.6687 81.1302 40.1929ZM74.153 29.5602C74.4734 29.3627 74.8585 29.1877 75.3083 29.0356C75.7581 28.8841 76.2195 28.7774 76.6923 28.7167C77.1648 28.6562 77.6262 28.6481 78.0763 28.6938C78.5258 28.7395 78.9228 28.8647 79.2659 29.0697C79.6089 29.2751 79.8643 29.5714 80.032 29.9586C80.1997 30.3464 80.2456 30.8365 80.1693 31.429C80.1083 31.9001 79.9211 32.2991 79.6089 32.6256C79.2963 32.9526 78.9147 33.2259 78.4652 33.4462C78.0154 33.6668 77.5388 33.8415 77.0356 33.9702C76.5321 34.0997 76.0477 34.1949 75.5828 34.2553C75.1176 34.3163 74.7137 34.3545 74.3706 34.3692C74.0273 34.3845 73.8021 34.3921 73.6956 34.3921L74.153 29.5602ZM83.6007 36.9676C83.3566 36.4361 83.0287 35.9689 82.6172 35.5658C82.2054 35.1633 81.717 34.8709 81.1531 34.6885C81.3969 34.491 81.6371 34.1795 81.8737 33.7539C82.1099 33.3288 82.3119 32.865 82.4796 32.3636C82.6474 31.8619 82.762 31.357 82.8229 30.8478C82.8836 30.3389 82.8607 29.902 82.7544 29.537C82.4947 28.6256 82.087 27.9114 81.5303 27.3946C80.9734 26.8782 80.3257 26.5211 79.586 26.3233C78.8462 26.1264 78.0304 26.0842 77.1383 26.1981C76.2462 26.312 75.3347 26.5361 74.4049 26.8704C74.4049 26.7946 74.4124 26.7148 74.4278 26.6312C74.4426 26.548 74.4504 26.4604 74.4504 26.369C74.4504 26.1411 74.3361 25.9439 74.1074 25.7765C73.8787 25.6093 73.6155 25.5107 73.3183 25.4801C73.0209 25.45 72.731 25.5142 72.4489 25.6738C72.1665 25.8334 71.9721 26.1264 71.8656 26.5511C71.7434 27.9189 71.6215 29.3398 71.4996 30.8134C71.3774 32.2875 71.248 33.7767 71.1107 35.2812C70.9735 36.7855 70.8362 38.2784 70.6989 39.7598C70.5616 41.2414 70.4244 42.6659 70.2871 44.0333C70.333 44.4436 70.4473 44.7629 70.6304 44.9907C70.8133 45.2189 71.0268 45.3556 71.2709 45.401C71.5147 45.4467 71.7704 45.4045 72.0371 45.2755C72.3038 45.1469 72.5365 44.9222 72.735 44.6032C73.3447 44.9375 74.0311 45.1541 74.7938 45.253C75.5561 45.3516 76.3298 45.3516 77.1157 45.253C77.9007 45.1541 78.6747 44.9682 79.4374 44.6943C80.1997 44.4211 80.8936 44.079 81.519 43.669C82.1441 43.2586 82.6703 42.7911 83.0975 42.2671C83.5244 41.7426 83.8065 41.1767 83.9437 40.5691C84.081 39.946 84.119 39.3231 84.0581 38.7C83.9971 38.0771 83.8445 37.5 83.6007 36.9676Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M105.915 49.0017C105.832 49.5031 105.713 50.0311 105.561 50.586C105.408 51.1403 105.229 51.6458 105.023 52.1018C104.818 52.5575 104.589 52.9256 104.337 53.207C104.085 53.488 103.815 53.606 103.525 53.5606C103.296 53.5297 103.151 53.3854 103.091 53.1274C103.029 52.8686 103.029 52.5497 103.091 52.17C103.151 51.7901 103.269 51.3607 103.445 50.8821C103.62 50.4035 103.834 49.9284 104.085 49.4577C104.337 48.9864 104.623 48.5347 104.943 48.1015C105.264 47.6686 105.599 47.3075 105.95 47.0189C106.026 47.11 106.06 47.3378 106.053 47.7028C106.045 48.0674 105.999 48.5006 105.915 49.0017ZM113.67 39.1097C113.464 38.8819 113.213 38.7529 112.915 38.7223C112.618 38.6919 112.317 38.859 112.012 39.2237C111.813 39.5883 111.562 39.9379 111.257 40.2722C110.952 40.6067 110.635 40.9103 110.307 41.1839C109.98 41.4572 109.667 41.6931 109.37 41.8903C109.072 42.0881 108.84 42.2324 108.672 42.3235C108.611 41.8374 108.576 41.3132 108.569 40.7507C108.561 40.1886 108.573 39.619 108.603 39.0415C108.649 38.2209 108.744 37.393 108.889 36.557C109.034 35.7213 109.244 34.9007 109.518 34.0951C109.518 33.67 109.419 33.3242 109.221 33.0582C109.022 32.7924 108.782 32.625 108.5 32.5567C108.218 32.4885 107.929 32.5264 107.631 32.6707C107.334 32.8153 107.078 33.0775 106.865 33.4569C106.682 33.9586 106.472 34.5207 106.236 35.1436C105.999 35.7667 105.732 36.4012 105.435 37.0469C105.138 37.6931 104.806 38.3197 104.44 38.9273C104.074 39.5354 103.674 40.075 103.239 40.5457C102.804 41.0168 102.331 41.3854 101.821 41.6512C101.31 41.9172 100.757 42.0349 100.162 42.0045C99.8876 41.9285 99.6893 41.7235 99.5675 41.3889C99.4453 41.0549 99.373 40.6368 99.3504 40.1354C99.3275 39.634 99.3504 39.0831 99.4189 38.4828C99.4877 37.8828 99.5791 37.2863 99.6934 36.6938C99.8078 36.101 99.9337 35.5389 100.071 35.0071C100.208 34.4753 100.337 34.0268 100.46 33.6622C100.643 33.2218 100.643 32.8529 100.46 32.5567C100.277 32.2604 100.025 32.0631 99.705 31.964C99.3846 31.8654 99.0489 31.8694 98.6983 31.9755C98.3474 32.0819 98.0958 32.3173 97.9435 32.682C97.684 33.3054 97.4475 34.004 97.2342 34.779C97.0206 35.5539 96.8491 36.3558 96.7197 37.1836C96.5896 38.0121 96.5171 38.8327 96.502 39.6456C96.5011 39.6985 96.5037 39.7488 96.5034 39.8014C96.1709 40.6848 95.854 41.3525 95.553 41.7992C95.1641 42.377 94.7253 42.6277 94.2375 42.5513C94.0236 42.4603 93.8832 42.2477 93.8147 41.9132C93.7453 41.5792 93.7227 41.1689 93.7453 40.6822C93.7688 40.1964 93.826 39.6456 93.9171 39.0299C94.0091 38.4146 94.1229 37.7764 94.2601 37.1154C94.3977 36.4541 94.5425 35.7899 94.6949 35.121C94.8472 34.4525 94.9845 33.8218 95.107 33.2291C95.0916 32.6973 94.9352 32.291 94.6377 32.0097C94.3405 31.7289 93.9247 31.6187 93.3913 31.6791C93.0253 31.8312 92.7542 32.029 92.579 32.2719C92.4034 32.5148 92.2623 32.8265 92.1558 33.2062C92.0946 33.404 92.0032 33.799 91.8813 34.3918C91.7591 34.984 91.603 35.6644 91.4123 36.4315C91.2217 37.1992 90.9967 38.0005 90.7376 38.8362C90.4781 39.6719 90.1885 40.4283 89.8684 41.1041C89.548 41.7801 89.1972 42.3235 88.8161 42.7338C88.4348 43.1438 88.023 43.3113 87.5807 43.2352C87.3366 43.1895 87.1805 42.9388 87.112 42.4831C87.0432 42.0271 87.0319 41.4653 87.0775 40.7964C87.1233 40.1279 87.2148 39.3946 87.352 38.5971C87.4893 37.7993 87.63 37.0434 87.7752 36.3289C87.92 35.6149 88.0535 34.984 88.1756 34.4372C88.2975 33.8901 88.3814 33.5254 88.4272 33.3433C88.4272 32.9026 88.3277 32.5495 88.1298 32.2832C87.9313 32.0178 87.6913 31.8503 87.4092 31.7818C87.1268 31.7136 86.8372 31.7514 86.54 31.8957C86.2426 32.0403 85.9872 32.3026 85.7736 32.682C85.6973 33.0923 85.598 33.5674 85.4761 34.1067C85.3539 34.6459 85.2361 35.2006 85.1218 35.7705C85.0074 36.3404 84.9003 36.8988 84.8014 37.4459C84.7021 37.993 84.6299 38.4716 84.584 38.8819C84.5536 39.2008 84.519 39.5923 84.4813 40.0556C84.443 40.5194 84.4238 41.0092 84.4238 41.5257C84.4238 42.0427 84.4618 42.5554 84.5385 43.0643C84.6145 43.5735 84.7518 44.0408 84.95 44.4659C85.1482 44.8915 85.4265 45.2408 85.7852 45.5144C86.1433 45.7879 86.5972 45.9397 87.1463 45.9704C87.7101 46.0005 88.202 45.9591 88.6217 45.8449C89.041 45.731 89.4221 45.5523 89.7654 45.3091C90.1084 45.0665 90.421 44.7776 90.7033 44.443C90.9851 44.1091 91.2637 43.7444 91.5383 43.3491C91.7974 43.9269 92.1329 44.3748 92.5447 44.694C92.9565 45.013 93.3913 45.2032 93.8486 45.2637C94.306 45.3241 94.7715 45.2602 95.2442 45.0699C95.7167 44.8803 96.1436 44.5573 96.5252 44.1012C96.7762 43.8216 97.0131 43.5038 97.2354 43.1525C97.3297 43.317 97.4301 43.4758 97.543 43.6224C97.9168 44.1091 98.424 44.443 99.0645 44.6255C99.7506 44.808 100.421 44.8386 101.077 44.7169C101.733 44.5954 102.358 44.3748 102.953 44.0559C103.548 43.7366 104.101 43.3532 104.612 42.9047C105.122 42.4565 105.568 41.9895 105.95 41.5028C105.934 41.8524 105.927 42.1832 105.927 42.4944C105.927 42.8061 105.919 43.1438 105.904 43.5088C105.141 44.0408 104.421 44.679 103.742 45.4233C103.064 46.1676 102.469 46.9616 101.958 47.8051C101.447 48.6483 101.047 49.5031 100.757 50.3691C100.467 51.2357 100.326 52.0445 100.334 52.7969C100.341 53.549 100.521 54.206 100.871 54.7681C101.222 55.3306 101.794 55.7331 102.587 55.9763C103.411 56.2348 104.135 56.242 104.76 55.9991C105.386 55.7559 105.931 55.3531 106.396 54.791C106.861 54.2289 107.242 53.549 107.54 52.7512C107.837 51.9534 108.073 51.1215 108.249 50.2555C108.424 49.3894 108.535 48.5379 108.58 47.7028C108.626 46.8668 108.626 46.1219 108.58 45.4687C109.892 44.9219 110.967 44.2305 111.806 43.3945C112.645 42.5594 113.338 41.6778 113.887 40.7507C114.055 40.5229 114.112 40.2493 114.059 39.9304C114.006 39.6111 113.876 39.3376 113.67 39.1097Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M142.53 37.6515C142.575 37.3022 142.644 36.9335 142.735 36.546C142.827 36.1585 142.941 35.7823 143.079 35.4177C143.216 35.0531 143.376 34.7379 143.559 34.4718C143.742 34.2061 143.937 34.0161 144.142 33.9019C144.348 33.7883 144.558 33.7995 144.771 33.936C145 34.0731 145.141 34.3617 145.195 34.8021C145.248 35.2433 145.195 35.7141 145.034 36.2155C144.874 36.7172 144.588 37.1879 144.177 37.6286C143.765 38.0696 143.208 38.3579 142.507 38.4947C142.476 38.2824 142.484 38.0011 142.53 37.6515ZM150.456 38.5857C150.204 38.5103 149.964 38.5025 149.735 38.5632C149.506 38.6239 149.361 38.7835 149.301 39.042C149.178 39.5281 148.984 40.0258 148.717 40.5347C148.45 41.0439 148.122 41.5262 147.734 41.9822C147.345 42.438 146.906 42.8408 146.418 43.1901C145.93 43.5397 145.419 43.7904 144.886 43.9422C144.351 44.1096 143.91 44.1284 143.559 43.9991C143.208 43.8705 142.93 43.6498 142.724 43.3384C142.518 43.027 142.369 42.6508 142.278 42.2101C142.186 41.7694 142.133 41.3137 142.118 40.8424C142.987 40.9034 143.761 40.7478 144.44 40.3751C145.118 40.0032 145.694 39.509 146.167 38.8937C146.639 38.2784 146.998 37.587 147.242 36.8195C147.485 36.0524 147.623 35.2887 147.653 34.5288C147.669 33.8146 147.562 33.2108 147.333 32.7169C147.105 32.2233 146.796 31.839 146.407 31.5658C146.018 31.2922 145.572 31.1326 145.069 31.0872C144.566 31.0415 144.054 31.11 143.536 31.2922C142.91 31.505 142.381 31.8506 141.946 32.3294C141.512 32.808 141.149 33.3629 140.86 33.9933C140.57 34.6239 140.341 35.3038 140.173 36.033C140.005 36.7626 139.883 37.4806 139.807 38.1873C139.739 38.8214 139.702 39.4278 139.689 40.013C139.657 40.0874 139.625 40.1588 139.59 40.2383C139.354 40.7782 139.079 41.3062 138.766 41.8226C138.454 42.3394 138.107 42.7725 137.726 43.1218C137.344 43.4714 136.948 43.5929 136.536 43.4865C136.292 43.426 136.159 43.1444 136.136 42.6433C136.113 42.1416 136.139 41.5187 136.216 40.7741C136.292 40.0298 136.38 39.2239 136.479 38.3579C136.578 37.4918 136.628 36.664 136.628 35.8737C136.628 35.1898 136.498 34.5329 136.239 33.9019C135.979 33.2718 135.625 32.7473 135.175 32.3294C134.725 31.9113 134.203 31.634 133.608 31.4975C133.013 31.3605 132.373 31.4518 131.687 31.7708C131 32.09 130.455 32.5382 130.051 33.1157C129.647 33.6934 129.277 34.3009 128.942 34.9391C128.819 34.4528 128.641 34.0011 128.404 33.583C128.167 33.1651 127.878 32.8005 127.535 32.4888C127.191 32.1776 126.806 31.9344 126.38 31.7595C125.953 31.5851 125.502 31.4975 125.03 31.4975C124.572 31.4975 124.149 31.5851 123.76 31.7595C123.371 31.9344 123.017 32.1583 122.696 32.4318C122.376 32.7056 122.087 33.013 121.827 33.3551C121.568 33.6969 121.339 34.0352 121.141 34.3692C121.11 33.9742 121.076 33.6286 121.038 33.332C121 33.0359 120.931 32.7852 120.832 32.5801C120.733 32.3748 120.592 32.2193 120.409 32.1129C120.226 32.0067 119.967 31.9532 119.632 31.9532C119.464 31.9532 119.296 31.9874 119.128 32.0556C118.96 32.1241 118.811 32.2193 118.682 32.3407C118.552 32.4627 118.453 32.6105 118.385 32.7852C118.316 32.9598 118.297 33.1614 118.327 33.3892C118.342 33.5566 118.385 33.7576 118.453 33.9933C118.522 34.2289 118.587 34.5369 118.648 34.9163C118.708 35.2962 118.758 35.756 118.796 36.2953C118.834 36.8349 118.846 37.4959 118.831 38.2784C118.815 39.0611 118.758 39.9763 118.659 41.0248C118.56 42.0733 118.403 43.289 118.19 44.6714C118.16 44.9907 118.282 45.2492 118.556 45.4467C118.831 45.6439 119.143 45.7578 119.494 45.7885C119.845 45.8188 120.177 45.7578 120.489 45.6063C120.802 45.4539 120.981 45.1882 121.027 44.8085C121.072 44.0943 121.16 43.3347 121.29 42.529C121.419 41.724 121.579 40.9262 121.77 40.1359C121.961 39.346 122.178 38.5938 122.422 37.8793C122.666 37.1651 122.937 36.5347 123.234 35.9876C123.532 35.4405 123.84 35.0039 124.161 34.6771C124.481 34.3504 124.816 34.187 125.167 34.187C125.594 34.187 125.926 34.3805 126.162 34.7679C126.398 35.1557 126.566 35.6536 126.666 36.2609C126.765 36.869 126.81 37.5341 126.803 38.2555C126.795 38.9773 126.765 39.6724 126.711 40.341C126.658 41.0098 126.597 41.606 126.528 42.1303C126.46 42.6545 126.41 43.0157 126.38 43.2129C126.38 43.5625 126.513 43.8395 126.78 44.0448C127.046 44.2498 127.344 44.3716 127.672 44.4095C128 44.4476 128.309 44.3866 128.598 44.227C128.888 44.0674 129.056 43.7982 129.102 43.4179C129.254 42.324 129.464 41.2264 129.731 40.1247C129.997 39.023 130.303 38.0355 130.646 37.1616C130.989 36.2878 131.37 35.5735 131.79 35.0189C132.209 34.4646 132.655 34.187 133.128 34.187C133.371 34.187 133.559 34.3544 133.688 34.6884C133.818 35.0227 133.883 35.4784 133.883 36.0559C133.883 36.4815 133.848 36.9184 133.78 37.3666C133.711 37.8148 133.631 38.2784 133.54 38.7569C133.448 39.2358 133.368 39.7256 133.299 40.227C133.231 40.7287 133.196 41.2527 133.196 41.7998C133.196 42.1797 133.235 42.6204 133.311 43.1218C133.387 43.6229 133.532 44.0983 133.745 44.5462C133.959 44.9947 134.252 45.3744 134.626 45.6858C135 45.9973 135.476 46.1531 136.056 46.1531C136.925 46.1531 137.695 45.9669 138.366 45.5947C139.037 45.2226 139.613 44.7365 140.093 44.1362C140.118 44.1047 140.141 44.0711 140.165 44.0399C140.202 44.1287 140.235 44.2227 140.276 44.3071C140.604 44.9756 141.05 45.4921 141.615 45.857C142.178 46.2216 142.842 46.4229 143.605 46.4611C144.367 46.4987 145.198 46.3581 146.098 46.0392C146.769 45.796 147.352 45.4921 147.848 45.1275C148.343 44.7628 148.789 44.3184 149.186 43.7941C149.583 43.2699 149.945 42.6658 150.273 41.9822C150.601 41.2981 150.932 40.5159 151.268 39.6342C151.329 39.3916 151.272 39.1751 151.097 38.9848C150.921 38.7951 150.708 38.6621 150.456 38.5857Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M162.887 36.0434C162.81 36.4918 162.707 36.986 162.578 37.525C162.448 38.0646 162.284 38.623 162.086 39.2004C161.888 39.7779 161.644 40.2984 161.354 40.7616C161.064 41.2254 160.733 41.5935 160.359 41.8671C159.985 42.1406 159.555 42.2546 159.066 42.2089C158.822 42.1788 158.635 42.0117 158.506 41.7075C158.376 41.4038 158.308 41.0161 158.3 40.545C158.292 40.0743 158.334 39.5575 158.426 38.9951C158.517 38.4333 158.658 37.8821 158.849 37.3426C159.04 36.8036 159.272 36.3056 159.547 35.8496C159.821 35.3939 160.138 35.0405 160.496 34.7898C160.854 34.5391 161.247 34.4217 161.674 34.4365C162.101 34.4518 162.559 34.6643 163.047 35.0747C163.016 35.2725 162.963 35.5954 162.887 36.0434ZM171.019 37.787C170.782 37.6656 170.538 37.6392 170.287 37.7075C170.035 37.7757 169.856 38.0076 169.749 38.4026C169.688 38.8283 169.551 39.3294 169.338 39.9069C169.124 40.4843 168.861 41.0317 168.548 41.5478C168.236 42.0646 167.877 42.494 167.473 42.8358C167.069 43.1778 166.638 43.3337 166.181 43.3028C165.799 43.2727 165.532 43.079 165.38 42.7218C165.227 42.3647 165.147 41.9168 165.14 41.3769C165.132 40.838 165.186 40.2301 165.3 39.5538C165.414 38.8777 165.552 38.2054 165.712 37.5363C165.872 36.868 166.036 36.2258 166.204 35.6105C166.371 34.9951 166.508 34.4747 166.616 34.0493C166.738 33.6693 166.699 33.3466 166.501 33.0803C166.303 32.8149 166.055 32.6246 165.758 32.5107C165.46 32.3967 165.159 32.3664 164.854 32.4196C164.549 32.4728 164.351 32.6362 164.259 32.9094C163.359 32.1345 162.494 31.7166 161.663 31.6559C160.831 31.5952 160.065 31.7776 159.364 32.203C158.662 32.6284 158.041 33.2437 157.5 34.0493C156.958 34.8549 156.52 35.7322 156.184 36.6818C155.849 37.6314 155.639 38.6004 155.555 39.5879C155.471 40.5757 155.536 41.4761 155.75 42.289C155.963 43.1018 156.34 43.7669 156.882 44.283C157.423 44.7998 158.159 45.0583 159.089 45.0583C159.501 45.0583 159.898 44.9747 160.279 44.8076C160.66 44.6401 161.011 44.4426 161.331 44.2148C161.651 43.9869 161.933 43.7475 162.178 43.4968C162.421 43.2461 162.612 43.0373 162.749 42.8699C162.856 43.417 163.032 43.8808 163.276 44.2605C163.519 44.6401 163.798 44.9521 164.111 45.1948C164.423 45.4376 164.751 45.6164 165.094 45.7306C165.437 45.8445 165.769 45.9015 166.089 45.9015C166.806 45.9015 167.477 45.6583 168.102 45.1719C168.727 44.6861 169.288 44.0893 169.784 43.3829C170.279 42.6762 170.687 41.9319 171.007 41.1491C171.328 40.3666 171.541 39.6715 171.648 39.0634C171.755 38.8355 171.735 38.5964 171.591 38.3457C171.446 38.095 171.255 37.909 171.019 37.787Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M212.194 50.3701C212.064 50.8866 211.862 51.3238 211.587 51.6806C211.313 52.0377 210.97 52.2239 210.558 52.2393C210.299 52.2543 210.101 52.1175 209.963 51.8289C209.826 51.5401 209.731 51.1679 209.678 50.7122C209.624 50.2562 209.601 49.747 209.609 49.1849C209.616 48.6227 209.639 48.0681 209.678 47.521C209.715 46.9742 209.761 46.4647 209.815 45.9939C209.868 45.5226 209.91 45.1586 209.94 44.9C210.459 44.9608 210.89 45.1846 211.233 45.5723C211.576 45.9598 211.839 46.4193 212.022 46.9514C212.205 47.4831 212.312 48.0568 212.343 48.6722C212.373 49.2875 212.323 49.8534 212.194 50.3701ZM203.913 50.3701C203.783 50.8866 203.581 51.3238 203.307 51.6806C203.032 52.0377 202.689 52.2239 202.277 52.2393C202.018 52.2543 201.82 52.1175 201.683 51.8289C201.545 51.5401 201.45 51.1679 201.397 50.7122C201.343 50.2562 201.32 49.747 201.328 49.1849C201.336 48.6227 201.358 48.0681 201.397 47.521C201.434 46.9742 201.48 46.4647 201.534 45.9939C201.587 45.5226 201.629 45.1586 201.66 44.9C202.178 44.9608 202.609 45.1846 202.952 45.5723C203.295 45.9598 203.558 46.4193 203.741 46.9514C203.924 47.4831 204.031 48.0568 204.062 48.6722C204.092 49.2875 204.042 49.8534 203.913 50.3701ZM195.415 37.4241C195.399 37.7884 195.365 38.1114 195.312 38.3925C195.258 38.6741 195.186 38.8522 195.095 38.9283C194.927 38.8369 194.721 38.6018 194.477 38.2216C194.233 37.8419 194.042 37.4122 193.905 36.9336C193.768 36.4551 193.725 35.9843 193.779 35.5205C193.832 35.0573 194.073 34.6967 194.5 34.4379C194.667 34.3468 194.812 34.3809 194.934 34.5405C195.056 34.7001 195.155 34.9318 195.232 35.2357C195.308 35.5399 195.361 35.8892 195.392 36.2842C195.422 36.6795 195.43 37.0591 195.415 37.4241ZM193.39 41.9711C193.154 42.2215 192.89 42.4381 192.601 42.6206C192.311 42.803 192.014 42.9398 191.709 43.0309C191.404 43.1223 191.129 43.1448 190.885 43.0991C190.199 42.9627 189.673 42.666 189.307 42.2103C188.941 41.7545 188.708 41.219 188.609 40.6037C188.51 39.9881 188.521 39.3308 188.644 38.6319C188.765 37.933 188.971 37.2835 189.261 36.6832C189.551 36.0829 189.902 35.5662 190.313 35.1333C190.725 34.7001 191.175 34.4306 191.663 34.3239C191.48 35.0989 191.419 35.9007 191.48 36.7286C191.541 37.5568 191.739 38.3355 192.075 39.0648C192.288 39.506 192.544 39.9082 192.841 40.2729C193.139 40.6378 193.501 40.9492 193.928 41.2075C193.806 41.466 193.626 41.7204 193.39 41.9711ZM218.702 37.6519C218.747 37.3026 218.816 36.9336 218.908 36.5462C218.999 36.159 219.114 35.7828 219.251 35.4181C219.388 35.0532 219.548 34.738 219.731 34.4723C219.914 34.2065 220.108 34.0163 220.314 33.9024C220.52 33.7884 220.73 33.7997 220.943 33.9365C221.172 34.0735 221.313 34.3621 221.367 34.8025C221.42 35.2435 221.367 35.7142 221.207 36.2159C221.046 36.7173 220.761 37.1884 220.349 37.6288C219.937 38.07 219.38 38.3583 218.679 38.4951C218.648 38.2826 218.656 38.0015 218.702 37.6519ZM227.921 37.6519C227.966 37.3026 228.035 36.9336 228.126 36.5462C228.218 36.159 228.332 35.7828 228.47 35.4181C228.607 35.0532 228.767 34.738 228.95 34.4723C229.133 34.2065 229.328 34.0163 229.533 33.9024C229.739 33.7884 229.949 33.7997 230.162 33.9365C230.391 34.0735 230.532 34.3621 230.586 34.8025C230.639 35.2435 230.586 35.7142 230.425 36.2159C230.265 36.7173 229.979 37.1884 229.568 37.6288C229.156 38.07 228.599 38.3583 227.898 38.4951C227.867 38.2826 227.875 38.0015 227.921 37.6519ZM236.488 38.9852C236.312 38.7955 236.099 38.6625 235.847 38.5862C235.595 38.5104 235.355 38.5029 235.126 38.5636C234.897 38.6244 234.752 38.784 234.692 39.0422C234.57 39.5286 234.375 40.0262 234.108 40.5349C233.841 41.0444 233.514 41.5267 233.125 41.9824C232.736 42.4381 232.297 42.8412 231.81 43.1905C231.321 43.5401 230.81 43.7908 230.277 43.9423C229.743 44.1101 229.301 44.1289 228.95 43.9996C228.599 43.8706 228.321 43.6503 228.115 43.3389C227.909 43.0271 227.761 42.6512 227.669 42.2103C227.578 41.7699 227.524 41.3142 227.509 40.8428C228.378 40.9038 229.152 40.7483 229.831 40.3755C230.509 40.0034 231.085 39.5092 231.558 38.8939C232.031 38.2788 232.389 37.5874 232.633 36.82C232.877 36.0526 233.014 35.2892 233.045 34.5293C233.06 33.815 232.953 33.211 232.724 32.7171C232.496 32.2235 232.187 31.8395 231.798 31.5662C231.409 31.2924 230.963 31.133 230.46 31.0874C229.957 31.0417 229.445 31.1105 228.927 31.2924C228.302 31.5055 227.772 31.851 227.338 32.3296C226.903 32.8085 226.54 33.3634 226.251 33.9934C225.961 34.6244 225.732 35.3039 225.564 36.0335C225.396 36.7627 225.274 37.481 225.199 38.1874C225.124 38.873 225.084 39.5292 225.075 40.1572C225.017 40.2824 224.956 40.4082 224.889 40.5349C224.622 41.0444 224.295 41.5267 223.906 41.9824C223.517 42.4381 223.078 42.8412 222.591 43.1905C222.102 43.5401 221.592 43.7908 221.058 43.9423C220.524 44.1101 220.082 44.1289 219.731 43.9996C219.38 43.8706 219.102 43.6503 218.896 43.3389C218.691 43.0271 218.542 42.6512 218.45 42.2103C218.359 41.7699 218.305 41.3142 218.29 40.8428C219.159 40.9038 219.933 40.7483 220.612 40.3755C221.29 40.0034 221.866 39.5092 222.339 38.8939C222.811 38.2788 223.17 37.5874 223.414 36.82C223.658 36.0526 223.795 35.2892 223.826 34.5293C223.841 33.815 223.734 33.211 223.506 32.7171C223.277 32.2235 222.968 31.8395 222.579 31.5662C222.19 31.2924 221.744 31.133 221.241 31.0874C220.738 31.0417 220.227 31.1105 219.708 31.2924C219.083 31.5055 218.553 31.851 218.119 32.3296C217.684 32.8085 217.321 33.3634 217.032 33.9934C216.742 34.6244 216.513 35.3039 216.346 36.0335C216.178 36.7627 216.056 37.481 215.98 38.1874C215.936 38.5859 215.907 38.9722 215.886 39.3516C215.739 39.4765 215.595 39.6023 215.442 39.7258C214.916 40.1514 214.363 40.5349 213.784 40.8769C213.204 41.219 212.601 41.5001 211.977 41.7204C211.351 41.9408 210.71 42.0738 210.055 42.1192L211.473 26.9847C211.565 26.6655 211.519 26.3847 211.336 26.1415C211.153 25.8983 210.916 25.7312 210.627 25.6401C210.337 25.5488 210.028 25.5566 209.7 25.6627C209.372 25.7694 209.102 26.0126 208.888 26.3919C208.781 26.9697 208.671 27.7597 208.557 28.7625C208.442 29.7653 208.328 30.8595 208.213 32.0448C208.099 33.23 207.985 34.4532 207.87 35.7142C207.756 36.9759 207.657 38.1533 207.573 39.2472C207.569 39.2958 207.566 39.3398 207.562 39.3878C207.429 39.5005 207.299 39.6142 207.161 39.7258C206.635 40.1514 206.082 40.5349 205.503 40.8769C204.923 41.219 204.321 41.5001 203.696 41.7204C203.07 41.9408 202.429 42.0738 201.774 42.1192L203.192 26.9847C203.284 26.6655 203.238 26.3847 203.055 26.1415C202.872 25.8983 202.635 25.7312 202.346 25.6401C202.056 25.5488 201.747 25.5566 201.419 25.6627C201.091 25.7694 200.821 26.0126 200.607 26.3919C200.501 26.9697 200.39 27.7597 200.276 28.7625C200.161 29.7653 200.047 30.8595 199.933 32.0448C199.818 33.23 199.704 34.4532 199.589 35.7142C199.475 36.9759 199.376 38.1533 199.292 39.2472C199.29 39.2692 199.289 39.2891 199.287 39.3111C199.048 39.4219 198.786 39.519 198.503 39.6006C198.213 39.6844 197.885 39.7339 197.519 39.7489C197.58 39.4751 197.63 39.1712 197.668 38.8369C197.706 38.5029 197.737 38.1533 197.76 37.7884C197.782 37.4241 197.79 37.0591 197.782 36.6945C197.774 36.3296 197.755 35.9956 197.725 35.6914C197.649 35.0385 197.508 34.4191 197.302 33.8338C197.096 33.2491 196.818 32.7593 196.467 32.3637C196.116 31.9687 195.678 31.7027 195.151 31.5662C194.626 31.4294 194.012 31.4748 193.31 31.7027C192.273 31.5662 191.339 31.6613 190.508 31.9878C189.677 32.3149 188.956 32.7894 188.346 33.4122C187.736 34.0357 187.237 34.7684 186.848 35.6119C186.459 36.4551 186.2 37.3214 186.07 38.21C186.015 38.5868 185.988 38.9618 185.98 39.336C185.744 39.8177 185.486 40.2388 185.201 40.5921C184.797 41.0935 184.377 41.5038 183.943 41.8228C183.508 42.142 183.077 42.3852 182.65 42.5523C182.223 42.7198 181.842 42.8337 181.507 42.8941C181.11 42.9702 180.729 42.978 180.363 42.917C179.997 42.8565 179.661 42.6816 179.357 42.3927C179.112 42.1802 178.925 41.8381 178.796 41.3671C178.666 40.896 178.59 40.3608 178.567 39.7602C178.544 39.1599 178.567 38.533 178.636 37.8798C178.705 37.2266 178.822 36.6072 178.99 36.0222C179.158 35.4372 179.371 34.913 179.631 34.4492C179.89 33.9862 180.195 33.6554 180.546 33.4579C180.744 33.4886 180.866 33.606 180.912 33.811C180.958 34.0163 180.969 34.2595 180.946 34.5405C180.923 34.8219 180.889 35.1105 180.843 35.4066C180.797 35.703 180.775 35.9502 180.775 36.1474C180.851 36.5577 180.999 36.877 181.221 37.1048C181.441 37.3327 181.69 37.466 181.964 37.5036C182.239 37.5417 182.509 37.4773 182.776 37.3098C183.043 37.143 183.26 36.877 183.428 36.512C183.443 36.5274 183.466 36.5349 183.497 36.5349L183.817 33.6404C183.909 33.2451 183.847 32.8958 183.634 32.5919C183.42 32.288 183.138 32.113 182.788 32.0676C182.345 31.4294 181.747 31.0914 180.992 31.0532C180.237 31.0154 179.463 31.2623 178.67 31.7941C178.182 32.144 177.751 32.626 177.378 33.2413C177.004 33.857 176.699 34.5405 176.463 35.2926C176.226 36.0448 176.058 36.8391 175.959 37.6748C175.86 38.5104 175.841 39.3236 175.902 40.1133C175.963 40.9038 176.104 41.6484 176.325 42.347C176.546 43.0462 176.855 43.6312 177.252 44.102C177.587 44.5123 177.968 44.8127 178.395 45.0027C178.822 45.1927 179.268 45.3101 179.734 45.3558C180.199 45.4012 180.66 45.3821 181.118 45.2988C181.575 45.2155 182.01 45.0978 182.421 44.9454C182.955 44.7482 183.505 44.4972 184.069 44.1933C184.633 43.8897 185.174 43.5248 185.693 43.0991C185.966 42.8753 186.228 42.6313 186.482 42.3696C186.598 42.6553 186.727 42.9317 186.882 43.1905C187.294 43.8741 187.85 44.429 188.552 44.8544C189.253 45.2797 190.115 45.4844 191.137 45.4697C192.235 45.4544 193.249 45.1774 194.18 44.6378C195.11 44.0988 195.872 43.3042 196.467 42.256C197.358 42.256 198.234 42.1096 199.096 41.819C199.089 41.911 199.081 42.0079 199.075 42.0966C199.014 42.9019 198.983 43.4487 198.983 43.7376C198.968 44.239 198.934 44.8581 198.88 45.5949C198.827 46.332 198.793 47.1069 198.778 47.9198C198.763 48.7326 198.793 49.5532 198.869 50.3817C198.945 51.2096 199.105 51.962 199.349 52.6383C199.593 53.3141 199.94 53.8878 200.39 54.3591C200.84 54.8299 201.431 55.1112 202.163 55.2023C202.941 55.3084 203.612 55.1717 204.176 54.792C204.74 54.412 205.198 53.8918 205.549 53.2308C205.899 52.5695 206.147 51.8061 206.292 50.9401C206.437 50.074 206.479 49.2039 206.418 48.3301C206.357 47.4562 206.196 46.6321 205.937 45.8575C205.678 45.0822 205.319 44.444 204.862 43.9423C205.137 43.8669 205.465 43.7226 205.846 43.5095C206.227 43.2969 206.62 43.0575 207.024 42.7915C207.123 42.7261 207.221 42.6573 207.32 42.5902C207.283 43.1286 207.264 43.5126 207.264 43.7376C207.249 44.239 207.215 44.8581 207.161 45.5949C207.108 46.332 207.073 47.1069 207.058 47.9198C207.043 48.7326 207.073 49.5532 207.15 50.3817C207.226 51.2096 207.386 51.962 207.63 52.6383C207.874 53.3141 208.221 53.8878 208.671 54.3591C209.121 54.8299 209.712 55.1112 210.444 55.2023C211.221 55.3084 211.892 55.1717 212.457 54.792C213.021 54.412 213.478 53.8918 213.83 53.2308C214.18 52.5695 214.428 51.8061 214.573 50.9401C214.718 50.074 214.759 49.2039 214.699 48.3301C214.637 47.4562 214.477 46.6321 214.218 45.8575C213.959 45.0822 213.601 44.444 213.143 43.9423C213.418 43.8669 213.745 43.7226 214.127 43.5095C214.508 43.2969 214.9 43.0575 215.305 42.7915C215.515 42.6533 215.724 42.5107 215.932 42.3641C216.01 43.1072 216.179 43.759 216.448 44.3073C216.776 44.9761 217.222 45.4925 217.787 45.8575C218.351 46.2218 219.014 46.4234 219.777 46.4612C220.539 46.4988 221.37 46.3586 222.271 46.0393C222.941 45.7965 223.525 45.4925 224.02 45.1279C224.516 44.763 224.962 44.3185 225.358 43.7946C225.381 43.7642 225.403 43.7313 225.425 43.7006C225.496 43.9134 225.574 44.1179 225.667 44.3073C225.995 44.9761 226.441 45.4925 227.006 45.8575C227.569 46.2218 228.233 46.4234 228.996 46.4612C229.758 46.4988 230.589 46.3586 231.489 46.0393C232.16 45.7965 232.744 45.4925 233.239 45.1279C233.735 44.763 234.181 44.3185 234.577 43.7946C234.974 43.27 235.336 42.666 235.664 41.9824C235.992 41.2985 236.323 40.5164 236.659 39.6347C236.72 39.3918 236.663 39.1752 236.488 38.9852Z" fill="#0D0C23"/>
</svg>`;
//# sourceMappingURL=data:application/json;base64,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
