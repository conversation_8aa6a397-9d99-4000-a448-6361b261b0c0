# Coze自动编写发布公众号文章智能体

## 发现背景
- **发现时间**：2025-07-17
- **来源**：微信公众号文章
- **文章链接**：https://mp.weixin.qq.com/s/SbOMEcZ3I_JvWVvbAe-W4Q
- **作者**：Liz
- **查找过程**：查询了一圈才找到有完整代码的文章
- **更新时间**：2025-07-17（获得完整技术实现细节）

## 智能体功能

### 核心能力
- **自动编写文章**：基于输入主题或要求生成文章内容
- **自动发布**：将生成的文章直接发布到公众号草稿箱
- **一站式流程**：从创作到发布的完整自动化

### 当前实现
- ✅ **文案生成**：能够生成基本的文章文案
- ✅ **配图功能**：包含简单的配图功能
- ✅ **草稿箱发布**：可以发布到公众号草稿箱
- ❌ **排版优化**：缺少专业的排版功能

## 技术实现

### 核心原理
- **格式转换**：公众号文章使用HTML格式渲染
- **生成流程**：标题 → Markdown文章 → HTML格式 → 公众号草稿箱
- **图片集成**：自动生成相关配图并插入文章
- **API对接**：通过微信公众号API实现自动发布

### 完整工作流详解

#### 1. 开始节点
**输入参数**：
- `input`：文章标题或主题
- `appid`：微信公众号的appid
- `secret`：微信公众号的开发者密码

#### 2. 文章封面图片生成模块
包含豆包的三个生图节点：

**2.1 提示词优化节点**
- 输入：文章标题
- 输出：优化后的绘画提示词

**2.2 封面图像生成节点**
- 输入：优化后的提示词
- 功能：生成文章封面图片
- 可配置正向/负向提示词增强效果

**2.3 图片缩放节点**
- 功能：调整图片尺寸
- 缩放模式：按长边缩放为1，按短边缩放为2

#### 3. 大模型生成节点
**核心功能**：生成文章正文
- 输入：文章标题
- 输出：Markdown格式的文章内容
- **关键点**：必须输出Markdown格式，便于后续转换HTML

#### 4. 代码模块
**功能**：内容分段处理
- 输入：大模型生成的文章内容
- 输出：文章段落数组（Array(string)类型）
- 目的：为每个段落配置相应图片

#### 5. 循环模块（最复杂部分）
**目标**：实现段落1+图片1+段落2+图片2+...的组合

**5.1 循环节点**
- 类型：数组循环
- 输入：文章段落数组

**5.2 循环体内部**
- **5.2.1 选择器节点+变量赋值**
  - 功能：累积拼接循环结果
  - 变量：text（用户变量）
  - 只在index=0时进行初始赋值

- **5.2.2 单独图片生成模块**
  - 大模型生成绘画提示词
  - 图像生成
  - 图片缩放

- **5.2.3 链接转markdown代码节点**
  - 功能：将图片转换为Markdown格式
  - 输出：`![image](url)`格式

- **5.2.4 文本整合模块**
  - 文本处理节点：拼接当前段落+图片+已有内容
  - 变量赋值节点：更新累积变量

**5.3 Markdown转HTML节点**
- 使用专门的转换插件
- 可配置字体、主题、配色方案
- 输出：HTML格式的完整文章

**5.4 写入公众号节点**
使用微信公众号写入插件（需配置白名单）
输入参数：
- `appid`：公众号appid
- `author`：作者名称
- `content`：HTML格式正文
- `digest`：文章摘要
- `image_url`：封面图片
- `secret`：开发者密码
- `title`：文章标题

**5.5 结束节点**
完成整个工作流

## 优化需求

### 当前不足
1. **排版问题**
   - 缺少专业排版样式
   - 段落格式单一
   - 视觉效果一般

2. **功能局限**
   - 配图相对简单
   - 缺少多媒体支持
   - 交互性不足

### 优化方向

#### 排版优化
- **样式模板**：添加多种排版模板
- **格式控制**：
  - 标题层级样式
  - 段落间距调整
  - 引用块样式
  - 代码块格式
- **视觉元素**：
  - 分割线设计
  - 强调文本样式
  - 列表美化
  - 表格样式

#### 内容增强
- **多媒体支持**：
  - 视频嵌入
  - 音频插入
  - GIF动图
  - 交互元素
- **SEO优化**：
  - 关键词布局
  - 标题优化
  - 摘要生成
  - 标签建议

#### 功能扩展
- **批量处理**：支持批量文章生成
- **定时发布**：设定发布时间
- **数据分析**：阅读量预测
- **A/B测试**：多版本对比

## 技术要点

### 关键插件
1. **微信公众号写入插件**：实现API对接
2. **Markdown转HTML插件**：格式转换
3. **图像生成插件**：豆包生图功能

### 循环逻辑示例
假设循环数组是["你好","我是","Liz"]：
- **第一轮循环**（index=0）：将"你好"赋值给text
- **第二轮循环**（index=1）：根据"我是"生成图片，拼接"我是"+图片+text("你好")
- **第三轮循环**（index=2）：根据"Liz"生成图片，拼接"Liz"+图片+text(前面的内容)

### 配置要求
- 微信公众号开发者权限
- API白名单配置
- Coze平台账号

### 注意事项
- **API限制**：注意公众号API的调用限制
- **内容审核**：确保生成内容符合平台规范
- **版权问题**：配图和内容的版权合规
- **白名单配置**：公众号插件需要配置白名单

## 应用场景

### 适用对象
- **内容创作者**：提高创作效率
- **营销团队**：批量内容生产
- **个人博主**：降低创作门槛
- **企业宣传**：自动化内容营销

### 使用价值
- **效率提升**：大幅减少创作时间
- **质量保证**：标准化的内容输出
- **成本降低**：减少人工创作成本
- **规模化**：支持大量内容生产

## 相关资源
- [[3-Resources/AI工具/提示词收集与管理实践]] - 提示词管理方法
- [[3-Resources/AI工具/AI笔记工具选择与实践]] - AI工具使用心得
- [[Coze平台使用指南]] - 待创建
- [[公众号API开发文档]] - 待收集

## 标签
#Coze #智能体 #公众号 #自动化 #内容创作 #排版优化

---
*发现时间：2025-07-17*
*更新时间：2025-07-17（完整技术细节）*
*分类：3-Resources/AI工具/智能体*
*优先级：高*
*状态：技术方案已明确，待实施排版优化*
