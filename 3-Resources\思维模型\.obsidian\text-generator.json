{"packages": [{"packageId": "default", "name": "Default Prompts Package", "version": "0.0.7", "minTextGeneratorVersion": "0.1.0", "description": "This is the main package that comes with Text Generator plugin in Obsidian", "author": "<PERSON><PERSON><PERSON>", "tags": "writing, brainstorming", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/gpt-3-prompt-templates"}, {"packageId": "dalle-2", "name": "Dalle-2 prompts", "version": "0.0.3", "minTextGeneratorVersion": "0.1.0", "description": "The package contains some interessting Dalle-2 prompt templates", "author": "<PERSON><PERSON><PERSON>", "tags": "photo, dalle-2", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/dalle-2"}, {"packageId": "huggingface", "name": "Huggingface Prompts Package", "version": "0.0.3", "minTextGeneratorVersion": "0.1.0", "description": "Huggingface Prompts comes with Text Generator plugin in Obsidian", "author": "<PERSON><PERSON><PERSON>", "tags": "writing, brainstorming, huggingface", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/huggingface"}, {"packageId": "awesomechatgpt", "name": "Awesome ChatGPT", "version": "0.0.1", "minTextGeneratorVersion": "0.1.0", "description": "This repo includes ChatGPT prompt curation to use ChatGPT better.", "author": "f", "tags": "writing, brainstorming", "authorUrl": "https://github.com/f/awesome-chatgpt-prompts", "repo": "nhaoua<PERSON>/awesome-chatgpt-textgenertor"}], "installedPackages": []}