{"title": "如何获得一个干净的AI情报系统？", "author": "泛函", "publish_time": "2025年07月27日 22:15", "content_html": "<div class=\"rich_media_content js_underline_content autoTypeSetting24psection\" id=\"js_content\" style=\"\"><section class=\"js_darkmode__0\" data-mpa-md-key=\"heading-2\" data-pm-slice=\"0 0 []\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 22px; margin-top: 0px; margin-bottom: 16px; font-weight: 500; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">我最近很着迷一款叫「语鲸」的产品，现在『语鲸』已经成为了我的第一信息入口了。</span></span></section><section class=\"js_darkmode__1\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">作为一个 AI 行业的资深从业者，同时因为经常写东西而认识了很多厉害朋友，我目前已经有着非常固定的信息源了。</span></span></section><section class=\"js_darkmode__2\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">这些信息源有值得信赖的媒体，但更多的是那些我见过面的、在行业里有声望且成长速度很快的人。</span></span></section><section class=\"js_darkmode__3\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">能认识这些人、能关注 ta 的个人账号是非常幸运的，因为你可以只关注很少的几个号，还不会遇到信息茧房。</span></span></section><section class=\"js_darkmode__4\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">因为这些人成长速度很快，他们的眼界、涉足的领域在不断拓宽。</span></span></section><section class=\"js_darkmode__5\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">他们中的很多人，一开始可能只是简单分享一下最新的 AI 新闻、各种创始人的深度访谈什么的。</span></span></section><section class=\"js_darkmode__6\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">慢慢地，他们在自己的领域有所成就之后，也会很愿意分享做自己手上这摊事的一手经验。</span></span></section><section class=\"js_darkmode__7\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">当他们因为在行业里的一个个小成就，而加速跨越了一个个人生节点时，也会写写自己对财富、对亲密关系、对人生愿景这些千年不变的议题的最新理解。</span></span></section><section class=\"js_darkmode__8\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">意识到这一点之后，我开始有意识地在自己摄入信息的信息源上做了不少减法。</span></span></section><section class=\"js_darkmode__9\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">最近感觉颇有成效，读得更少了，懂得却更多了；</span></span></section><section class=\"js_darkmode__10\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 24px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">受到的情绪撩拨、感受到的焦虑越来越少了，从“知道”到“做到”的速度越来越快了。</span></span></section><section class=\"js_darkmode__11\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 32px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">所以，想来和你分享一下我的最佳实践，同时也向你安利一下，在这个过程中帮了我大忙的 AI 产品——语鲸。</span></span></section><section class=\"js_darkmode__12\" data-mpa-md-key=\"heading-2\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 22px; margin-top: 22px; margin-bottom: 8px; font-weight: 500; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 20px; color: rgb(61, 167, 66); visibility: visible;\" textstyle=\"\">能够清晰描述目标时，很适合问问 AI 怎么落地</span></span></section><section class=\"js_darkmode__13\" style='font-style: normal; font-variant-ligatures: no-common-ligatures; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: break-spaces; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; color: rgb(31, 35, 41); text-align: justify; font-size: 16px; line-height: 26px; word-break: break-all; margin-top: 8px; margin-bottom: 8px; min-height: 20px; visibility: visible;'><span leaf=\"\" style=\"visibility: visible;\"><span style=\"font-size: 16px; visibility: visible;\" textstyle=\"\">当我一开始有这个想法时，还没有行动的思路，所以我去 flowith（一个画布式 AI Agent 聚合平台）问了问我的老朋友 OpenAI o3。</span></span></section><section class=\"js_darkmode__14\" style='font-style: normal;font-variant-ligatures: no-common-ligatures;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";color: rgb(31, 35, 41);text-align: justify;font-size: 16px;line-height: 26px;word-break: break-all;margin-top: 8px;margin-bottom: 8px;min-height: 20px;'><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">自从推理模型成熟后，我就很少“指挥 AI 替我干活”了，而是谦逊了不少，都是让 AI “教我做事”。</span></span></section><section class=\"js_darkmode__15\" style='font-style: normal;font-variant-ligatures: no-common-ligatures;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";color: rgb(31, 35, 41);text-align: justify;font-size: 16px;line-height: 26px;word-break: break-all;margin-top: 8px;margin-bottom: 8px;min-height: 20px;'><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">它常常能给我很好的答案，于是这一次，我同样选择了这么问它：</span></span></section><section data-mpa-action-id=\"mdl3kg3o2jp\" data-mpa-template=\"t\" mpa-data-temp-power-by=\"yiban.io\" mpa-data-temp-type=\"body\" mpa-from-tpl=\"t\"><section data-mid=\"\" mpa-from-tpl=\"t\" style=\"width: 100%;padding: 0px 14px 0px 14px;\"><section data-mid=\"\" mpa-from-tpl=\"t\" style=\"width: 100%;\"><section data-mid=\"\" mpa-from-tpl=\"t\" nodeleaf=\"\" style=\"width: 26px;display: flex;justify-content: center;align-items: center;margin-right: auto;margin-bottom: 16px;text-align: justify;\"></section><section data-mid=\"\" mpa-from-tpl=\"t\" style=\"display: grid;grid-template-columns: 100.1% 100.1%;\"><section data-mid=\"\" mpa-from-tpl=\"t\" style=\"width: 100%;transform: translateX(-100.1%);padding: 14px;\"><pre data-pm-slice=\"0 0 []\" style=\"white-space:pre-wrap;\"><code data-lark-language=\"Plain Text\" data-wrap=\"false\"><p style=\"text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">我最近希望重构自己的信息输入系统。</span></span></p><p style=\"text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">目标是：只靠定期刷我关注的领域的少量优质信息源、主动搜索和项目实践来获得优质信息和知识，而不是靠被动地被推荐算法投喂信息。</span></span><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">但是我现在没有实践思路，请你告诉我，该怎么一步步实现这个目标？</span></span></p></code></pre></section></section></section></section></section><section class=\"js_darkmode__18\" style='font-style: normal;font-variant-ligatures: no-common-ligatures;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";color: rgb(31, 35, 41);text-align: justify;font-size: 16px;line-height: 26px;word-break: break-all;margin-top: 8px;margin-bottom: 8px;min-height: 20px;'><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这是一个我常用的提问结构，很受用，帮你简单归纳一下：</span></span></section><section data-mpa-action-id=\"mdl3q5epty4\" data-mpa-template=\"t\" mpa-data-temp-power-by=\"yiban.io\" mpa-from-tpl=\"t\"><section data-mid=\"\" mpa-from-tpl=\"t\" style=\"width: 100%;padding: 0px 14px 0px 14px;\"><section data-mid=\"\" mpa-from-tpl=\"t\" style=\"width: 100%;\"><section data-mid=\"\" mpa-from-tpl=\"t\" nodeleaf=\"\" style=\"width: 26px;display: flex;justify-content: center;align-items: center;margin-right: auto;margin-bottom: -15px;text-align: justify;\"></section><section data-mid=\"\" mpa-from-tpl=\"t\" style=\"display: grid;grid-template-columns: 100.1% 100.1%;\"><section data-mid=\"\" mpa-from-tpl=\"t\" style=\"width: 100%;transform: translateX(-100.1%);padding: 14px;\"><pre data-pm-slice=\"0 0 []\" style=\"margin-bottom: 16px;\"><code data-lark-language=\"Plain Text\" data-wrap=\"false\"><p style=\"text-align: justify;\"><span data-mpa-action-id=\"mdl3qly043b\" data-pm-slice=\"0 0 []\" leaf=\"\" mpa-font-style=\"mdl3qlxpg1c\" style=\"font-size: 16px;\">我的目标是 xxx，但我现在没有实现思路，请你告诉我，该怎么一步步实现这个目标？</span></p></code></pre></section></section></section></section></section><pre data-pm-slice=\"0 0 []\" style=\"margin-bottom: 16px;\"><code data-lark-language=\"Plain Text\" data-wrap=\"false\"><p style=\"margin-bottom: 32px;text-align: justify;\"><span data-mpa-action-id=\"mdl3qly043b\" data-pm-slice='1 1 [\"para\",{\"tagName\":\"section\",\"attributes\":{\"data-mpa-template\":\"t\",\"mpa-data-temp-power-by\":\"yiban.io\",\"mpa-from-tpl\":\"t\",\"data-mpa-action-id\":\"mdl3q5epty4\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"section\",\"attributes\":{\"style\":\"width: 100%;padding: 0px 14px 0px 14px;\",\"data-mid\":\"\",\"mpa-from-tpl\":\"t\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"section\",\"attributes\":{\"style\":\"width: 100%;\",\"data-mid\":\"\",\"mpa-from-tpl\":\"t\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"section\",\"attributes\":{\"style\":\"display: grid;grid-template-columns: 100.1% 100.1%;\",\"data-mid\":\"\",\"mpa-from-tpl\":\"t\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"section\",\"attributes\":{\"style\":\"width: 100%;transform: translateX(-100.1%);padding: 14px;\",\"data-mid\":\"\",\"mpa-from-tpl\":\"t\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"pre\",\"attributes\":{\"data-pm-slice\":\"0 0 []\",\"style\":\"margin-bottom: 16px;\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"node\",{\"tagName\":\"code\",\"attributes\":{\"data-lark-language\":\"Plain Text\",\"data-wrap\":\"false\"},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"},\"para\",{\"tagName\":\"p\",\"attributes\":{},\"namespaceURI\":\"http://www.w3.org/1999/xhtml\"}]' leaf=\"\" mpa-font-style=\"mdl3qlxpg1c\" style=\"font-size: 16px;\"><span style=\"font-size: 16px;font-weight: normal;\" textstyle=\"\">它依旧发挥稳定，给出了我很受用的建议。</span></span></p></code></pre><section nodeleaf=\"\" style=\"text-align: justify;margin-bottom: 32px;\"></section><p data-pm-slice=\"0 0 []\" style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">不过它这人关心我得很，每次都给我说一大堆东西，如果全展示出来会影响你阅读，我就给你挑挑重点，一段段给你展示一下。</span></span></p><h2 style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 20px;color: rgb(61, 167, 66);font-weight: bold;\" textstyle=\"\">写出目标与限制</span></span></h2><h2 style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">它给我的第一个建议是，细化你的目标，并且设置好限制。</span></span></h2><section nodeleaf=\"\" style=\"text-align: justify;margin-bottom: 32px;\"></section><p data-pm-slice=\"0 0 []\" style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这个建议很好，我之前有阵子习惯把屋子弄得特别乱，经常被女朋友批评教育，后来专门研究了一下「收纳整理」这门学问，学到的第一个道理和这个很像。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">在开始收纳之前，先想明白两个问题：</span></span></p><p style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">同样地，对于「重构信息输入系统」也是，你也值得好好想明白这两个问题：</span></span></p><p style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">经过一番思索，我的答案是：</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">当然还有限制，限制很简单，就是少刷短视频……</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">很多写文出身的人会看不起短视频这个媒介，认为它“低级”，但我是真心喜欢短视频，我是真觉得很多短视频作者拍的东西真好看。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">但短视频确实有害，所以值得好好努力克制自己看着玩意……</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">短视频的从业者也这么觉得……</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;\"></section><p data-pm-slice=\"0 0 []\" style=\"margin-bottom: 24px;text-align: justify;margin-top: 32px;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">但几乎所有媒体平台都做了短视频功能，所以我们需要一个新的信息入口。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">在这个需求下，我找到了</span><span style=\"font-size: 16px;font-weight: bold;\" textstyle=\"\">语鲸</span><span style=\"font-size: 16px;\" textstyle=\"\">这款产品。</span></span></p><h2 style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 20px;color: rgb(61, 167, 66);font-weight: bold;\" textstyle=\"\">语鲸：AI 时代的个人情报系统</span></span></h2><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">语鲸是一款信息订阅 +  AI 总结类的产品，你可以在上面订阅各种公众号、网站等多平台信息源的信息。</span></span></p><section nodeleaf=\"\" style=\"margin-bottom: 32px;text-align: justify;\"></section><section nodeleaf=\"\" style=\"text-align: justify;\"></section><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">对扣子和 Dify 比较熟的朋友，一定曾想过为自己搭建一条这样的工作流：</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">当自己关注的某几个信息源有更新时，爬取其内容，用 AI 总结后，推送给你。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">在社媒上，这一类的教程和模板流量都挺大；在各种 AI 课里，搭建这样的工作流的教程板块热度也很高。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">语鲸帮你把这件事干了，现在你只需要下载名为「语鲸」的 APP，或者登录他们的网站就能用上。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">不过，AI 总结类的产品已经有很多了，那语鲸的差异化在哪里呢？</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">总结下来是两个点：方便、准。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">曾经的各种 AI 总结类的产品，还得你手动上传、粘贴、转发之后才能生成总结；</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">而语鲸能主动干活，当你配置好订阅源之后，内容一更新，就能自动推送到你面前，总结就生成好了。</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;\"></section><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">而且，语鲸更准，我和他们团队聊过，令人震惊的是，他们为了提高总结的准确度，专门训了个自己的模型。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">所以，在调研完这款产品之后，我确定这款产品能够「信息源管理」+『克制的信息输入』这两个核心需求，开始琢磨起了它。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">还总结了一套自己的使用流程，也在这里继续分享给你。</span></span></p><h2 style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 20px;color: rgb(61, 167, 66);font-weight: bold;\" textstyle=\"\">配置专属订阅频道</span></span></h2><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">语鲸最令人舒适的功能，不是 AI 总结，而是「频道订阅」。</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;\"></section><p style=\"margin-bottom: 16px;margin-top: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这里的频道大多不是某个具体的账号，而是语鲸团队去精选的一些热门话题，并且把追踪讨论这些话题的信息源做了一轮聚合整理后的汇总。</span></span></p><p style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这些频道都挺有意思的，“中国AI初创企业最新进展”、“AI营销产业全景图谱更新了”等等都是圈内圈外都很关心的 AI 话题。</span></span></p><p style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">在这些频道里，我最喜欢的是这个——我那些开“生产力外挂”的 AI 写作工具。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这个频道很务实，全在推荐各种能帮到你创作的 AI 产品，场景拆解得很细，功能讲解的很全，非常易懂，每篇文章都能学到新技巧。</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;margin-bottom: 32px;\"></section><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">除了能订阅别人的频道，你还能上手配置自己的频道。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这功能对于有固定信息源的人来说很友好，于是我自己上手配了一个，还挺方便的，在【订阅】那个标签里，点击右上角的【三】，再点击创建频道就行。</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;margin-bottom: 32px;\"></section><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">我创建的频道叫【泛函の AI 信息源】，目前已经设置成公开可见了，你应该可以在这里查看。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">有意思的是，在选择了一个自己喜欢的频道进行订阅，并且创建好了自己的频道之后，我居然不知不觉完成了 o3 给我的建议。</span></span></p><section nodeleaf=\"\" style=\"margin-bottom: 32px;text-align: justify;\"></section><h2 style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\">目前，我在语鲸上配置了三个频道，这里面几乎涵盖了所有我刷社交媒体爱看的东西，分享给你：</span></h2><h2 style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\">1. 泛函の AI 信息源：AI世界的新情报。</span></h2><h2 style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\">2. 创业者心法：成为一个创业者所需要的内功心法。</span></h2><h2 style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\">3. 社媒动向：社交媒体上最新的舆情风向。</span></h2><h2 style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\">如果你对这些主题也感兴趣，可以复制这段口令后打开语鲸 APP，你就能关联上了👇🏻</span></h2><p data-pm-slice=\"0 0 []\"><span data-eleid=\"64599167\" style=\"white-space:pre-wrap;\"><span leaf=\"\"><span style=\"font-size: 11px;color: rgb(136, 136, 136);\" textstyle=\"\">3g:/ 05/03 n@Y.j3 长按复制此条消息，打开语鲸 app，查看订阅的频道列表##LWLD3orCH##wx[语鲸口令]</span></span></span></p><h2 style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 20px;color: rgb(61, 167, 66);font-weight: bold;\" textstyle=\"\">深度阅读工作流程</span></span></h2><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">现在，我已经拥有了一个很干净的『个人 AI 情报系统』了，我所关心的这些行业信息，都在一条时间线上。</span></span></p><section nodeleaf=\"\" style=\"margin-bottom: 32px;text-align: justify;\"></section><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">一般我有两种阅读场景：用手机刷，用电脑读。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">本人对自己的注意力水平有一定的自知之明，所以当我用手机消费内容时，确实并不要求自己一定得深度阅读，只用来筛选文章，以及记个大概。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">所以，在工作日的碎片时间，我的使用方式是在信息时间线上快速看每篇文章的摘要，并且将感兴趣的文章点上方的星号进行收藏。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">感兴趣的文章我会点进去，先根据总结浏览全篇文章的大概，看看这篇文章我是否感兴趣。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">如果感兴趣，我会点击右上角的星号对这篇文章进行收藏。</span></span></p><section nodeleaf=\"\" style=\"margin-bottom: 32px;text-align: justify;\"></section><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">如果不感兴趣的话，直接向刷 Tinder 一样右滑，就能划到下一篇文章。</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;\"><span class=\"video_iframe rich_pages\" data-cover=\"http%3A%2F%2Fmmbiz.qpic.cn%2Fmmbiz_jpg%2Frj7ia3RcHDWI3lNc0qtxOCicaPhzhpOm2BicjzUnUiabib5ryNiaJHcCnQz9U7jIvwJh0SzszBaB884agoYibsz0bL6nA%2F0%3Fwx_fmt%3Djpeg\" data-mpvid=\"wxv_4094152109095124998\" data-src=\"https://mp.weixin.qq.com/mp/readtemplate?t=pages/video_player_tmpl&amp;auto=0&amp;vid=wxv_4094152109095124998\" data-vh=\"378.5625\" data-vidtype=\"2\" data-vw=\"673\" height=\"395\" id=\"js_mp_video_container_0\" scrolling=\"no\" style=\"width: 677px !important; height: 395px !important; overflow: hidden;\" vid=\"wxv_4094152109095124998\" width=\"677\"><div id=\"page-content\"> <!--S 全屏播放 full_screen_mv--> <div id=\"js_mpvedio_wrapper_wxv_4094152109095124998\" style=\"position:relative;height:100%\">  <div class=\"feed-wrapper\"><div aria-hidden=\"true\" aria-modal=\"true\" class=\"wx_bottom_modal_wrp player_relate_video_dialog weui-half-screen-dialog_fold\" hidewhensideslip=\"\" role=\"dialog\" style=\"visibility: hidden; display: none;\" tabindex=\"0\"><div class=\"weui-half-screen-dialog wx_bottom_modal\" style=\"max-height: none;\"><div class=\"wx_bottom_modal_group_container\" style=\"transform: translateX(calc(0% + 0px)); max-height: none;\"><div aria-hidden=\"false\" class=\"wx_bottom_modal_group\" style=\"left: 0%; max-height: none;\"><div class=\"weui-half-screen-dialog__hd__wrp\"><div class=\"weui-half-screen-dialog__hd\"><div class=\"weui-half-screen-dialog__hd__side\"><button class=\"weui-btn_icon weui-wa-hotarea\">关闭</button></div><div class=\"weui-half-screen-dialog__hd__main\"><strong class=\"weui-half-screen-dialog__title\">观看更多</strong></div><div class=\"weui-half-screen-dialog__hd__side\"><!-- --><button class=\"weui-btn_icon weui-wa-hotarea\" style=\"display: none;\">更多</button></div></div></div><!-- --></div></div></div></div><div class=\"infinity-list__wrapper\" style=\"height: 379px;\"><div class=\"\" style=\"height: 379px; overflow: visible;\"><div class=\"infinity-list__page destory-enter-to\" data-key=\"wxv_4094152109095124998\" infinity-idx=\"0\" style=\"height: 379px; position: absolute; top: 0px; opacity: 1;\"><div class=\"mp-video-player\" data-v-1639cf84=\"\" style=\"height: 100%;\"><div class=\"js_mpvedio page_video_wrapper\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" id=\"js_mpvedio_1753678393105_1715224599687\"><div class=\"js_page_video page_video ratio_primary align_upper_center page_video_without-control page_video_skin-normal\" data-v-823ba74e=\"\" style=\"display: block; width: 100%; height: 379px;\"><div class=\"full_screen_opr wx_video_play_opr\" data-v-823ba74e=\"\" style=\"\"><button class=\"mid_play_box reset_btn\" data-v-823ba74e=\"\" type=\"button\"><span class=\"aria_hidden_abs\" data-v-823ba74e=\"\">，时长</span><span class=\"video_length\" data-v-823ba74e=\"\">00:30</span></button></div><!-- --><div class=\"mid_opr fast_pre_next\" data-v-823ba74e=\"\" style=\"display: none;\"><p class=\"video_length\" data-v-823ba74e=\"\"><span class=\"played_time js_forward_play_time\" data-v-823ba74e=\"\">0</span><span class=\"js_forward_seperator\" data-v-823ba74e=\"\">/</span><span class=\"total_time js_forward_total_time\" data-v-823ba74e=\"\">0</span></p></div><div class=\"wx_video_progress_msg full_screen_opr\" data-v-823ba74e=\"\" role=\"alert\" style=\"display: none;\"><div class=\"wx_video_progress_msg_inner\" data-v-823ba74e=\"\"><span class=\"wx_video_progress_current\" data-v-823ba74e=\"\">00:00</span><span class=\"wx_video_progress_gap\" data-v-823ba74e=\"\">/</span><span class=\"wx_video_progress_total\" data-v-823ba74e=\"\">00:30</span></div></div><div class=\"video_screen_mode_switch\" data-v-823ba74e=\"\" style=\"bottom: calc(50% - 336px); display: none;\"><button class=\"reset_btn video_screen_mode_switch_btn weui-wa-hotarea\" data-v-823ba74e=\"\" type=\"button\"> 切换到横屏模式 </button></div><div class=\"full_screen_opr wx_video_pause_full_mod\" data-v-823ba74e=\"\" style=\"display: none;\"><button class=\"reset_btn wx_video_pause_full_btn\" data-v-823ba74e=\"\" type=\"button\">继续播放</button></div><div class=\"js_control video_opr video_opr_sns\" data-v-823ba74e=\"\" style=\"bottom: 0px; display: none;\"><div class=\"opr_inner\" data-v-823ba74e=\"\"><div class=\"opr_inner_fl\" data-v-823ba74e=\"\"><div class=\"js_switch weui-wa-hotarea switch switch_on\" data-v-823ba74e=\"\"><a class=\"btn_opr\" data-v-823ba74e=\"\" href=\"javascript:;\" role=\"button\">播放</a></div><div data-v-823ba74e=\"\" role=\"option\"><div class=\"played_time js_now_play_time\" data-v-823ba74e=\"\">00:00</div><span data-v-823ba74e=\"\">/</span><div class=\"total_time js_total_time\" data-v-823ba74e=\"\">00:30</div></div><!-- --><div class=\"total_time js_total_time\" data-v-823ba74e=\"\" role=\"option\" style=\"display: none;\">00:30</div></div><div class=\"opr_inner_fr\" data-v-823ba74e=\"\"><!-- --><!-- --><!-- --><div class=\"weui-wa-hotarea js_full_screen_control screenSize_control full\" data-v-823ba74e=\"\" role=\"button\"><i class=\"icon_control\" data-v-823ba74e=\"\">全屏</i></div></div></div></div><div class=\"full_screen_opr video_quick_play_context\" data-v-823ba74e=\"\" role=\"alert\" style=\"display: none;\"><div class=\"video_quick_play_msg\" data-v-823ba74e=\"\"> 倍速播放中 </div></div><div class=\"js_sub_setting video_full-screen__footer video_full-screen__footer__sub-setting hide\" data-v-823ba74e=\"\"><div class=\"video_full-screen__sub-setting video_full-screen__sub-setting__speed js_playback_mode_select\" data-v-823ba74e=\"\" style=\"display: none;\"><a class=\"video_full-screen__sub-setting__item js_playback_0\" data-v-823ba74e=\"\" href=\"javascript:;\"> 0.5倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_1\" data-v-823ba74e=\"\" href=\"javascript:;\"> 0.75倍 </a><a class=\"video_full-screen__sub-setting__item current js_playback_2\" data-v-823ba74e=\"\" href=\"javascript:;\"> 1.0倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_3\" data-v-823ba74e=\"\" href=\"javascript:;\"> 1.5倍 </a><a class=\"video_full-screen__sub-setting__item js_playback_4\" data-v-823ba74e=\"\" href=\"javascript:;\"> 2.0倍 </a></div><div class=\"video_full-screen__sub-setting video_full-screen__sub-setting__ratio js_play_mode_select\" data-v-823ba74e=\"\" style=\"display: none;\"><a class=\"video_full-screen__sub-setting__item current js_resolution_0\" data-v-823ba74e=\"\" href=\"javascript:;\"> 超清 </a><a class=\"video_full-screen__sub-setting__item js_resolution_1\" data-v-823ba74e=\"\" href=\"javascript:;\"> 流畅 </a></div></div><div class=\"js_inner inner not_fullscreen\" data-v-823ba74e=\"\"><div class=\"js_video_poster video_poster\" data-v-823ba74e=\"\" style=\"display: none;\"><video class=\"\" controlslist=\"nodownload\" crossorigin=\"anonymous\" data-v-823ba74e=\"\" playsinline=\"isiPhoneShowPlaysinline\" poster=\"http://mmbiz.qpic.cn/mmbiz_jpg/rj7ia3RcHDWI3lNc0qtxOCicaPhzhpOm2BicjzUnUiabib5ryNiaJHcCnQz9U7jIvwJh0SzszBaB884agoYibsz0bL6nA/0?wx_fmt=jpeg&amp;wxfrom=16\" preload=\"metadata\" src=\"https://mpvideo.qpic.cn/0b2esebkmaacoaai4x4etvuffeodu2iqfjqa.f10002.mp4?dis_k=06deb4bf9e2b39a86c6398235c818ee8&amp;dis_t=1753678330&amp;play_scene=10120&amp;auth_info=efbMvKlZFnoVptbJlz5pTk8IEChwaVZuSF5tVWQybQJAMTsFCRNpAGQ9QjQFBWVt&amp;auth_key=48829dfeacf7d55d98cfb425b3b72f69&amp;vid=wxv_4094152109095124998&amp;format_id=10002&amp;support_redirect=0&amp;mmversion=false\" webkit-playsinline=\"isiPhoneShowPlaysinline\"> 您的浏览器不支持 video 标签 </video></div><div class=\"video_poster__info\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"display: none;\"><p class=\"video_poster__info__title\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"font-size: 17px;\">继续观看</p><p class=\"video_poster__info__desc\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"font-size: 12px;\"> 如何获得一个干净的AI情报系统？ </p></div></div><div class=\"video_profile_area\" data-v-823ba74e=\"\" style=\"display: none;\"><div data-v-823ba74e=\"\"><button class=\"reset_btn video_profile_relate_video_btn js_wx_tap_highlight wx_tap_link\" data-v-1639cf84=\"\" data-v-823ba74e=\"\" style=\"display: none;\">观看更多</button></div><div data-v-823ba74e=\"\" role=\"link\" style=\"width: fit-content; max-width: 100%;\" tabindex=\"0\"><div class=\"weui-wa-hotarea video_profile_desc_wrp\" data-v-823ba74e=\"\" role=\"option\"><div class=\"weui-hidden_abs\" data-v-823ba74e=\"\">,</div><div class=\"video_profile_desc\" data-v-823ba74e=\"\">如何获得一个干净的AI情报系统？</div></div></div><div class=\"video_profile_wrp weui-flex\" data-v-823ba74e=\"\"><div class=\"video_profile weui-flex weui-flex__item\" data-v-823ba74e=\"\"><span class=\"video_profile_nickname weui-wa-hotarea\" data-v-823ba74e=\"\">泛函</span><button class=\"reset_btn video_profile_follow_btn weui-wa-hotarea\" data-v-823ba74e=\"\" style=\"display: none;\" type=\"button\">已关注</button></div><div class=\"video_sns_context\" data-v-823ba74e=\"\" style=\"display: none;\"><button class=\"video_sns_btn video_sns_btn_praise\" data-v-823ba74e=\"\" title=\"\" type=\"button\"><span class=\"video_sns_num\" data-v-823ba74e=\"\">点赞</span></button><button class=\"video_sns_btn video_sns_btn_love\" data-v-823ba74e=\"\" title=\"\" type=\"button\"><span class=\"video_sns_num\" data-v-823ba74e=\"\">在看</span></button></div></div></div></div></div></div></div></div></div><!-- --></div> </div> <!--E 视频播放器--> <!-- S 视频社交--> <div class=\"interact_video\" id=\"bottom_bar\" style=\"display:none;height: 35px;\"> <div class=\"inter_opr\"> <a class=\"access_original\" href=\"javascript:;\" id=\"video_detail_btn\" target=\"_blank\">         视频详情       </a> </div> </div> </div></span></section><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这样一篇篇浏览，一篇篇滑动，感觉很像在批奏折……</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;margin-bottom: 32px;\"></section><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这么刷着刷着，很多行业里有哪些值得关注的新闻，就都大概有个印象了；</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这么划着划着，值得深度的好文章，就一下能顺利地找到了。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">等到晚上时，再用网页端结合着智能目录、分段总结和 AI 提问一起来深度研究这篇文章。</span></span></p><section nodeleaf=\"\" style=\"text-align: center;\"></section><p style=\"margin-bottom: 24px;text-align: justify;margin-top: 32px;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">手机端筛选出了值得读的内容，并且看了个大概，相当于“预习”；</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">在电脑端更大的屏幕上、用多维度的互动方式进行学习，相当于“复习”。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这么一来一回，看过的内容都能印象深刻，再也不会出现“眼睛在动，脑子没动”的情况了。</span></span></p><h2 style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 20px;color: rgb(61, 167, 66);font-weight: bold;\" textstyle=\"\">创作是最好的知识管理</span></span></h2><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">我自己有一个小生意，经营着一个小社群，名为「轻熵」。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">在这个群里，我会每天精选出一篇我读过的深度内容，并且写一段推荐语告诉你为什么这篇内容值得被你看完，并推送到微信群里。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">我的这个小生意里推送的内容，大多都是在语鲸里发现的。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这个 Personal 的信息订阅服务，不仅服务了我的用户，也给了我动力筛选和整理自己读过的好内容，对知识管理大有帮助。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这些从语鲸里发现的好内容，不仅帮我在 AI 领域做出了更好的职业选择、在业务中做出了更高质量的决策，也实际地变成了我写下的一篇篇文章、在外面做的一次次分享的素材。</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;\"></section><section nodeleaf=\"\" style=\"text-align: justify;\"></section><section nodeleaf=\"\" style=\"text-align: justify;margin-bottom: 32px;\"></section><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">AI 创作工具 Youmind 的创始人玉伯分享过一个让我印象很深刻的观点——</span><span style=\"font-size: 16px;font-weight: bold;\" textstyle=\"\">创作才是最好的知识管理。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">光消费内容会很难让人印象深刻，要想真正理解其中奥义，还得将这些对你有益的内容，变成你向世界输出的秩序。</span></span></p><section nodeleaf=\"\" style=\"margin-bottom: 32px;text-align: justify;\"></section><h2 style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 20px;color: rgb(61, 167, 66);font-weight: bold;\" textstyle=\"\">提升输入质量，是一件值得努力一辈子的事情</span></span></h2><p style=\"margin-bottom: 16px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">「价值阅读」社群的主理人崇旭老师，在社群的介绍文档里写个这么一段话：</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">“</span></span><strong><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">输入决定输出。</span></span></strong></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">一个人如果每天往脑子里灌垃圾，他不可能有见识。没有见识，生活就不可能有质量。这与金钱无关，不愁衣食对许多人来说已是常态，但是，「不自由」也是常态。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">财富自由、时间自由、心灵自由看似遥不可及，我却觉得见识可以改变这一切。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">好，怎么做？ </span></span><strong><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">最重要的一步，是学会放弃。</span></span></strong></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">放弃不好的，好的就留下来了。见过好的，乃至更好的，筛选标准自然也就提高了。很多生活里的东西，够用（能解决问题）就行，但输入脑子里的，却是越优质越好，没有上限。每一天，都要让最优质的信息穿过身体。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><strong><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">选择就是放弃。</span></span></strong></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">选择之所以如此重要，是因为每个选择都有「机会成本」。我们时间有限，这一个小时如果看了点价值有限的八卦新闻，就意味着放弃了更优质的信息。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">人跟人的差距不是突然拉大的，而是由每一次选择累积起来的。”</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">这段话我特别认可，提升输入质量，确实是一件值得努力一辈子的事情。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">读到这里，你大概已经发现，「重构信息输入系统」其实只靠三件极其朴素的事就能完成：</span></span></p><section nodeleaf=\"\" style=\"margin-bottom: 32px;text-align: justify;\"></section><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">而语鲸恰恰把这三步打包做完了，一键订阅和主题聚合让主动选择只需动一次手；</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">自研模型把九成九的条目级摘要做到了可回溯、可验证，你可以心无旁骛地把时间交给 AI；</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">多文档聚合、沉浸式阅读器、划线摘录再加 AI 问答，则把阅读这件事从“刷信息”变成“留白后的深思”。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">当你能够用两百字判断一篇万字研报值不值得精读，能够把灵感随手划线后在任何场景中瞬间调取，信息焦虑的那层雾其实薄得很，一推就散。</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">所以，如果你也想用百分之十的时间掌握百分之百的关键信息，把注意力从无穷无尽的信息流里抽出来投向真正的学习、写作或项目实践；</span></span></p><p style=\"margin-bottom: 24px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;\" textstyle=\"\">如果你也想在这个 AI 时代拥有一套属于自己的个人情报系统，那么就去试试语鲸吧。</span></span></p><p style=\"margin-bottom: 32px;text-align: justify;\"><span leaf=\"\"><span style=\"font-size: 16px;font-weight: bold;\" textstyle=\"\">让它带你潜入信息深海，再带着洞见上岸，把阅读的每一分钟，都变成看得见、摸得着的生产力。</span></span></p><section nodeleaf=\"\" style=\"text-align: justify;margin-bottom: 32px;\"></section><section class=\"js_darkmode__21\" style='font-style: normal;font-variant-ligatures: no-common-ligatures;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";color: rgb(31, 35, 41);text-align: justify;font-size: 16px;line-height: 26px;word-break: break-all;margin-top: 8px;margin-bottom: 8px;min-height: 20px;'><span leaf=\"\">文中提到的链接汇总：</span></section><section class=\"js_darkmode__22\" style='font-style: normal;font-variant-ligatures: no-common-ligatures;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";color: rgb(31, 35, 41);text-align: justify;font-size: 16px;line-height: 26px;word-break: break-all;margin-top: 8px;margin-bottom: 8px;min-height: 20px;'><span leaf=\"\">1. Joey 乔伊《</span><span class=\"js_darkmode__23\" data-pm-slice=\"0 0 []\" style='color: rgb(51, 51, 51);font-family: \"Source Han Sans VF\", \"Noto Color Emoji\", system-ui, sans-serif;font-size: medium;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;letter-spacing: 0.25px;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;display: inline !important;float: none;'><span leaf=\"\">要做好短视频，必须少刷短视频》：</span><span leaf=\"\">https://web-next.okjike.com/u/A46A8281-62B1-438A-A52F-A7C22A3E7F9E/post/687610904f7d5a315ed86a5d</span></span></section><section class=\"js_darkmode__24\" style='font-style: normal;font-variant-ligatures: no-common-ligatures;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";color: rgb(31, 35, 41);text-align: justify;font-size: 16px;line-height: 26px;word-break: break-all;margin-top: 8px;margin-bottom: 8px;min-height: 20px;'><span leaf=\"\">2. 刘飞&amp;玉伯《</span><span leaf=\"\">对谈玉伯：告别大厂，我在AI荒野里「养小孩」</span><span leaf=\"\">》：</span><span leaf=\"\">https://www.xiaoyuzhoufm.com/episode/68598b1b2a38b4d979294357</span></section><section class=\"js_darkmode__25\" style='font-style: normal;font-variant-ligatures: no-common-ligatures;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";color: rgb(31, 35, 41);text-align: justify;font-size: 16px;line-height: 26px;word-break: break-all;margin-top: 8px;margin-bottom: 8px;min-height: 20px;'><span leaf=\"\">3. 崇旭《价值阅读：提高输入质量，值得努力一辈子》：</span><span leaf=\"\">https://rtw1bnq45a.feishu.cn/docx/SKj3dmzJFoyTc9xZ7TxcrYp6nVh</span></section><section class=\"js_darkmode__26\" style='font-style: normal;font-variant-ligatures: no-common-ligatures;font-variant-caps: normal;font-weight: 400;letter-spacing: normal;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: break-spaces;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Tahoma, \"PingFang SC\", \"Microsoft Yahei\", Arial, \"Hiragino Sans GB\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";color: rgb(31, 35, 41);text-align: justify;font-size: 16px;line-height: 26px;word-break: break-all;margin-top: 8px;margin-bottom: 8px;min-height: 20px;'><span leaf=\"\">4. 语鲸官网：点击下方【阅读原文】即可跳转。</span></section></div>", "content_text": "我最近很着迷一款叫「语鲸」的产品，现在『语鲸』已经成为了我的第一信息入口了。作为一个 AI 行业的资深从业者，同时因为经常写东西而认识了很多厉害朋友，我目前已经有着非常固定的信息源了。这些信息源有值得信赖的媒体，但更多的是那些我见过面的、在行业里有声望且成长速度很快的人。能认识这些人、能关注 ta 的个人账号是非常幸运的，因为你可以只关注很少的几个号，还不会遇到信息茧房。因为这些人成长速度很快，他们的眼界、涉足的领域在不断拓宽。他们中的很多人，一开始可能只是简单分享一下最新的 AI 新闻、各种创始人的深度访谈什么的。慢慢地，他们在自己的领域有所成就之后，也会很愿意分享做自己手上这摊事的一手经验。当他们因为在行业里的一个个小成就，而加速跨越了一个个人生节点时，也会写写自己对财富、对亲密关系、对人生愿景这些千年不变的议题的最新理解。意识到这一点之后，我开始有意识地在自己摄入信息的信息源上做了不少减法。最近感觉颇有成效，读得更少了，懂得却更多了；受到的情绪撩拨、感受到的焦虑越来越少了，从“知道”到“做到”的速度越来越快了。所以，想来和你分享一下我的最佳实践，同时也向你安利一下，在这个过程中帮了我大忙的 AI 产品——语鲸。能够清晰描述目标时，很适合问问 AI 怎么落地当我一开始有这个想法时，还没有行动的思路，所以我去 flowith（一个画布式 AI Agent 聚合平台）问了问我的老朋友 OpenAI o3。自从推理模型成熟后，我就很少“指挥 AI 替我干活”了，而是谦逊了不少，都是让 AI “教我做事”。它常常能给我很好的答案，于是这一次，我同样选择了这么问它：我最近希望重构自己的信息输入系统。目标是：只靠定期刷我关注的领域的少量优质信息源、主动搜索和项目实践来获得优质信息和知识，而不是靠被动地被推荐算法投喂信息。但是我现在没有实践思路，请你告诉我，该怎么一步步实现这个目标？这是一个我常用的提问结构，很受用，帮你简单归纳一下：我的目标是 xxx，但我现在没有实现思路，请你告诉我，该怎么一步步实现这个目标？它依旧发挥稳定，给出了我很受用的建议。不过它这人关心我得很，每次都给我说一大堆东西，如果全展示出来会影响你阅读，我就给你挑挑重点，一段段给你展示一下。写出目标与限制它给我的第一个建议是，细化你的目标，并且设置好限制。这个建议很好，我之前有阵子习惯把屋子弄得特别乱，经常被女朋友批评教育，后来专门研究了一下「收纳整理」这门学问，学到的第一个道理和这个很像。在开始收纳之前，先想明白两个问题：同样地，对于「重构信息输入系统」也是，你也值得好好想明白这两个问题：经过一番思索，我的答案是：当然还有限制，限制很简单，就是少刷短视频……很多写文出身的人会看不起短视频这个媒介，认为它“低级”，但我是真心喜欢短视频，我是真觉得很多短视频作者拍的东西真好看。但短视频确实有害，所以值得好好努力克制自己看着玩意……短视频的从业者也这么觉得……但几乎所有媒体平台都做了短视频功能，所以我们需要一个新的信息入口。在这个需求下，我找到了语鲸这款产品。语鲸：AI 时代的个人情报系统语鲸是一款信息订阅 +  AI 总结类的产品，你可以在上面订阅各种公众号、网站等多平台信息源的信息。对扣子和 Dify 比较熟的朋友，一定曾想过为自己搭建一条这样的工作流：当自己关注的某几个信息源有更新时，爬取其内容，用 AI 总结后，推送给你。在社媒上，这一类的教程和模板流量都挺大；在各种 AI 课里，搭建这样的工作流的教程板块热度也很高。语鲸帮你把这件事干了，现在你只需要下载名为「语鲸」的 APP，或者登录他们的网站就能用上。不过，AI 总结类的产品已经有很多了，那语鲸的差异化在哪里呢？总结下来是两个点：方便、准。曾经的各种 AI 总结类的产品，还得你手动上传、粘贴、转发之后才能生成总结；而语鲸能主动干活，当你配置好订阅源之后，内容一更新，就能自动推送到你面前，总结就生成好了。而且，语鲸更准，我和他们团队聊过，令人震惊的是，他们为了提高总结的准确度，专门训了个自己的模型。所以，在调研完这款产品之后，我确定这款产品能够「信息源管理」+『克制的信息输入』这两个核心需求，开始琢磨起了它。还总结了一套自己的使用流程，也在这里继续分享给你。配置专属订阅频道语鲸最令人舒适的功能，不是 AI 总结，而是「频道订阅」。这里的频道大多不是某个具体的账号，而是语鲸团队去精选的一些热门话题，并且把追踪讨论这些话题的信息源做了一轮聚合整理后的汇总。这些频道都挺有意思的，“中国AI初创企业最新进展”、“AI营销产业全景图谱更新了”等等都是圈内圈外都很关心的 AI 话题。在这些频道里，我最喜欢的是这个——我那些开“生产力外挂”的 AI 写作工具。这个频道很务实，全在推荐各种能帮到你创作的 AI 产品，场景拆解得很细，功能讲解的很全，非常易懂，每篇文章都能学到新技巧。除了能订阅别人的频道，你还能上手配置自己的频道。这功能对于有固定信息源的人来说很友好，于是我自己上手配了一个，还挺方便的，在【订阅】那个标签里，点击右上角的【三】，再点击创建频道就行。我创建的频道叫【泛函の AI 信息源】，目前已经设置成公开可见了，你应该可以在这里查看。有意思的是，在选择了一个自己喜欢的频道进行订阅，并且创建好了自己的频道之后，我居然不知不觉完成了 o3 给我的建议。目前，我在语鲸上配置了三个频道，这里面几乎涵盖了所有我刷社交媒体爱看的东西，分享给你：1. 泛函の AI 信息源：AI世界的新情报。2. 创业者心法：成为一个创业者所需要的内功心法。3. 社媒动向：社交媒体上最新的舆情风向。如果你对这些主题也感兴趣，可以复制这段口令后打开语鲸 APP，你就能关联上了👇🏻3g:/ 05/03 n@Y.j3 长按复制此条消息，打开语鲸 app，查看订阅的频道列表##LWLD3orCH##wx[语鲸口令]深度阅读工作流程现在，我已经拥有了一个很干净的『个人 AI 情报系统』了，我所关心的这些行业信息，都在一条时间线上。一般我有两种阅读场景：用手机刷，用电脑读。本人对自己的注意力水平有一定的自知之明，所以当我用手机消费内容时，确实并不要求自己一定得深度阅读，只用来筛选文章，以及记个大概。所以，在工作日的碎片时间，我的使用方式是在信息时间线上快速看每篇文章的摘要，并且将感兴趣的文章点上方的星号进行收藏。感兴趣的文章我会点进去，先根据总结浏览全篇文章的大概，看看这篇文章我是否感兴趣。如果感兴趣，我会点击右上角的星号对这篇文章进行收藏。如果不感兴趣的话，直接向刷 Tinder 一样右滑，就能划到下一篇文章。    关闭观看更多更多，时长00:300/000:00/00:30 切换到横屏模式 继续播放播放00:00/00:3000:30全屏 倍速播放中  0.5倍  0.75倍  1.0倍  1.5倍  2.0倍  超清  流畅  您的浏览器不支持 video 标签 继续观看 如何获得一个干净的AI情报系统？ 观看更多,如何获得一个干净的AI情报系统？泛函已关注点赞在看               视频详情          这样一篇篇浏览，一篇篇滑动，感觉很像在批奏折……这么刷着刷着，很多行业里有哪些值得关注的新闻，就都大概有个印象了；这么划着划着，值得深度的好文章，就一下能顺利地找到了。等到晚上时，再用网页端结合着智能目录、分段总结和 AI 提问一起来深度研究这篇文章。手机端筛选出了值得读的内容，并且看了个大概，相当于“预习”；在电脑端更大的屏幕上、用多维度的互动方式进行学习，相当于“复习”。这么一来一回，看过的内容都能印象深刻，再也不会出现“眼睛在动，脑子没动”的情况了。创作是最好的知识管理我自己有一个小生意，经营着一个小社群，名为「轻熵」。在这个群里，我会每天精选出一篇我读过的深度内容，并且写一段推荐语告诉你为什么这篇内容值得被你看完，并推送到微信群里。我的这个小生意里推送的内容，大多都是在语鲸里发现的。这个 Personal 的信息订阅服务，不仅服务了我的用户，也给了我动力筛选和整理自己读过的好内容，对知识管理大有帮助。这些从语鲸里发现的好内容，不仅帮我在 AI 领域做出了更好的职业选择、在业务中做出了更高质量的决策，也实际地变成了我写下的一篇篇文章、在外面做的一次次分享的素材。AI 创作工具 Youmind 的创始人玉伯分享过一个让我印象很深刻的观点——创作才是最好的知识管理。光消费内容会很难让人印象深刻，要想真正理解其中奥义，还得将这些对你有益的内容，变成你向世界输出的秩序。提升输入质量，是一件值得努力一辈子的事情「价值阅读」社群的主理人崇旭老师，在社群的介绍文档里写个这么一段话：“输入决定输出。一个人如果每天往脑子里灌垃圾，他不可能有见识。没有见识，生活就不可能有质量。这与金钱无关，不愁衣食对许多人来说已是常态，但是，「不自由」也是常态。财富自由、时间自由、心灵自由看似遥不可及，我却觉得见识可以改变这一切。好，怎么做？ 最重要的一步，是学会放弃。放弃不好的，好的就留下来了。见过好的，乃至更好的，筛选标准自然也就提高了。很多生活里的东西，够用（能解决问题）就行，但输入脑子里的，却是越优质越好，没有上限。每一天，都要让最优质的信息穿过身体。选择就是放弃。选择之所以如此重要，是因为每个选择都有「机会成本」。我们时间有限，这一个小时如果看了点价值有限的八卦新闻，就意味着放弃了更优质的信息。人跟人的差距不是突然拉大的，而是由每一次选择累积起来的。”这段话我特别认可，提升输入质量，确实是一件值得努力一辈子的事情。读到这里，你大概已经发现，「重构信息输入系统」其实只靠三件极其朴素的事就能完成：而语鲸恰恰把这三步打包做完了，一键订阅和主题聚合让主动选择只需动一次手；自研模型把九成九的条目级摘要做到了可回溯、可验证，你可以心无旁骛地把时间交给 AI；多文档聚合、沉浸式阅读器、划线摘录再加 AI 问答，则把阅读这件事从“刷信息”变成“留白后的深思”。当你能够用两百字判断一篇万字研报值不值得精读，能够把灵感随手划线后在任何场景中瞬间调取，信息焦虑的那层雾其实薄得很，一推就散。所以，如果你也想用百分之十的时间掌握百分之百的关键信息，把注意力从无穷无尽的信息流里抽出来投向真正的学习、写作或项目实践；如果你也想在这个 AI 时代拥有一套属于自己的个人情报系统，那么就去试试语鲸吧。让它带你潜入信息深海，再带着洞见上岸，把阅读的每一分钟，都变成看得见、摸得着的生产力。文中提到的链接汇总：1. Joey 乔伊《要做好短视频，必须少刷短视频》：https://web-next.okjike.com/u/A46A8281-62B1-438A-A52F-A7C22A3E7F9E/post/687610904f7d5a315ed86a5d2. 刘飞&玉伯《对谈玉伯：告别大厂，我在AI荒野里「养小孩」》：https://www.xiaoyuzhoufm.com/episode/68598b1b2a38b4d9792943573. 崇旭《价值阅读：提高输入质量，值得努力一辈子》：https://rtw1bnq45a.feishu.cn/docx/SKj3dmzJFoyTc9xZ7TxcrYp6nVh4. 语鲸官网：点击下方【阅读原文】即可跳转。", "images": [], "word_count": 4549, "url": "https://mp.weixin.qq.com/s/rEeqhItwrFdvbZprKaZx9g", "site_type": "微信公众号", "extracted_at": "2025-07-28T12:54:37.560302"}