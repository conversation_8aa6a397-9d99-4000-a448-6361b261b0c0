---
type: "manual"
---

# Obsidian Knowledge Base Rules

## File Structure
- Daily notes follow format: YYYY-MM-DD.md
- Weekly notes follow format: YYYY-[W]WW.md  
- Monthly notes follow format: YYYY-MM.md
- Quarterly notes follow format: YYYY-[Q]Q.md
- Yearly notes follow format: YYYY.md

## Markdown Conventions
- Use `[[wikilinks]]` for internal references
- Tags use `#tag` format
- Tasks use `- [ ]` for incomplete, `- [x]` for complete
- Templates use `{{date}}`, `{{time}}`, `{{title}}` placeholders

## Plugin Integration
- Templater functions available as `tp.user.*` and `tp.system.*`
- Calendar plugin tracks daily/weekly metadata and task counts
- Periodic notes auto-create based on date patterns
- Text generator supports AI completion with configurable models

## Content Guidelines
- Keep atomic notes focused on single concepts
- Use consistent frontmatter for metadata
- Link related concepts bidirectionally
- Maintain MOCs (Maps of Content) for navigation

## Folder Organization
- Templates in dedicated template folder
- Daily/weekly/monthly notes in separate folders
- User scripts in configured scripts folder
- Attachments in dedicated media folder

## AI Assistant Behavior for Content Management

### When User Provides Input for Knowledge Integration:
1. **Analyze existing files** to find related concepts and potential merge targets
2. **Create new files** when content represents distinct new concepts
3. **Update existing files** when content extends or refines existing knowledge
4. **Establish connections** using wikilinks between related concepts
5. **Suggest file locations** based on folder structure and content type

### Content Processing Rules:
- Extract key concepts and create appropriate wikilinks
- Add relevant tags based on content themes
- Suggest frontmatter metadata (creation date, tags, aliases)
- Identify opportunities for bidirectional linking
- Recommend MOC updates when new concepts are added

### File Creation Criteria:
- Create new file if concept is sufficiently distinct
- Merge into existing file if content is complementary
- Split existing file if it becomes too broad
- Maintain atomic note principle (one main concept per note)

### Knowledge Integration Workflow:
1. Search for existing related content
2. Determine best integration approach (new/merge/update)
3. Create or modify files with proper linking
4. Update relevant MOCs and index files
5. Suggest additional connections and cross-references

当访问微信文章时，使用项目中的智能文章读取器.py 来读取文章