{"main": {"id": "72c159602d9c55b6", "type": "split", "children": [{"id": "f27ec89d5935179b", "type": "leaf", "state": {"type": "graph", "state": {}}}], "direction": "vertical"}, "left": {"id": "fbcfb2e2ace9e252", "type": "split", "children": [{"id": "fbc30aeb5958ca01", "type": "tabs", "children": [{"id": "778469bddfcec18b", "type": "leaf", "state": {"type": "file-explorer", "state": {}}}, {"id": "9b1edf330b9ba673", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "4570e7dcdcee497a", "type": "split", "children": [{"id": "5656c3357f893d42", "type": "tabs", "children": [{"id": "8bb58793d3d14d97", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}}]}], "direction": "horizontal", "width": 300}, "active": "f27ec89d5935179b", "lastOpenFiles": ["产品待办事项列表梳理活动.md", "项目/项目成本.md", "项目/项目.md", "项目/Scrum框架.md", "项目/项目生命周期类型-适应型（敏捷开发）.md", "项目/敏捷适用性评估Rader Map.md", "项目/项目生命周期阶段.md", "项目/项目生命周期类型.md", "项目/产品生命周期.md", "项目/项目管理金字塔.md"]}