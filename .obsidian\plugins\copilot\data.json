{"userId": "10b845dc-a602-46e1-9d9e-40ee6d216875", "isPlusUser": false, "plusLicenseKey": "", "openAIApiKey": "", "openAIOrgId": "", "huggingfaceApiKey": "", "cohereApiKey": "", "anthropicApiKey": "", "azureOpenAIApiKey": "", "azureOpenAIApiInstanceName": "", "azureOpenAIApiDeploymentName": "", "azureOpenAIApiVersion": "", "azureOpenAIApiEmbeddingDeploymentName": "", "googleApiKey": "", "openRouterAiApiKey": "", "xaiApiKey": "", "mistralApiKey": "", "deepseekApiKey": "", "defaultChainType": "llm_chain", "defaultModelKey": "gpt-4.1|openai", "embeddingModelKey": "text-embedding-3-small|openai", "temperature": 0.1, "maxTokens": 6000, "contextTurns": 15, "userSystemPrompt": "", "openAIProxyBaseUrl": "", "openAIEmbeddingProxyBaseUrl": "", "stream": true, "defaultSaveFolder": "copilot-conversations", "defaultConversationTag": "copilot-conversation", "autosaveChat": false, "includeActiveNoteAsContext": true, "defaultOpenArea": "view", "customPromptsFolder": "copilot-custom-prompts", "indexVaultToVectorStore": "ON MODE SWITCH", "qaExclusions": "copilot-conversations,copilot-custom-prompts", "qaInclusions": "", "chatNoteContextPath": "", "chatNoteContextTags": [], "enableIndexSync": true, "debug": false, "enableEncryption": false, "maxSourceChunks": 3, "groqApiKey": "", "activeModels": [{"name": "copilot-plus-flash", "provider": "copilot-plus", "enabled": false, "isBuiltIn": true, "core": true, "plusExclusive": true, "projectEnabled": false, "capabilities": ["vision"]}, {"name": "google/gemini-2.5-flash-lite-preview-06-17", "provider": "openrouterai", "enabled": false, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "google/gemini-2.5-flash", "provider": "openrouterai", "enabled": false, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "google/gemini-2.5-pro", "provider": "openrouterai", "enabled": false, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "gpt-4.1", "provider": "openai", "enabled": false, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "gpt-4.1-mini", "provider": "openai", "enabled": false, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "gpt-4.1-nano", "provider": "openai", "enabled": false, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "o4-mini", "provider": "openai", "enabled": false, "isBuiltIn": true, "core": true, "capabilities": ["reasoning"]}, {"name": "claude-3-5-sonnet-latest", "provider": "anthropic", "enabled": false, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "claude-sonnet-4-20250514", "provider": "anthropic", "enabled": false, "isBuiltIn": true, "capabilities": ["reasoning", "vision"]}, {"name": "claude-3-7-sonnet-latest", "provider": "anthropic", "enabled": false, "isBuiltIn": true, "capabilities": ["reasoning", "vision"]}, {"name": "claude-3-5-haiku-latest", "provider": "anthropic", "enabled": false, "isBuiltIn": true}, {"name": "grok-3-beta", "provider": "xai", "enabled": false, "isBuiltIn": true}, {"name": "grok-3-mini-beta", "provider": "xai", "enabled": false, "isBuiltIn": true}, {"name": "gemini-2.5-flash", "provider": "google", "enabled": false, "isBuiltIn": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "gemini-2.5-pro", "provider": "google", "enabled": false, "isBuiltIn": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "command-r", "provider": "cohereai", "enabled": false, "isBuiltIn": true}, {"name": "command-r-plus", "provider": "cohereai", "enabled": false, "isBuiltIn": true}, {"name": "azure-openai", "provider": "azure openai", "enabled": false, "isBuiltIn": true}, {"name": "deepseek-chat", "provider": "deepseek", "enabled": true, "isBuiltIn": true, "apiKey": "sk-3QrEVpTGDYQYYG9HVhil0ZSy8AyWmu0mlwmYKIllmXdoOCbd", "baseUrl": "https://api.gptgod.online/v1"}, {"name": "deepseek-reasoner", "provider": "deepseek", "enabled": false, "isBuiltIn": true, "capabilities": ["reasoning"]}], "activeEmbeddingModels": [{"name": "copilot-plus-small", "provider": "copilot-plus", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true}, {"name": "copilot-plus-large", "provider": "copilot-plus-jina", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true, "believerExclusive": true, "dimensions": 1024}, {"name": "copilot-plus-multilingual", "provider": "copilot-plus-jina", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true, "dimensions": 512}, {"name": "text-embedding-3-small", "provider": "openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true}, {"name": "text-embedding-3-large", "provider": "openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "embed-multilingual-light-v3.0", "provider": "cohereai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "text-embedding-004", "provider": "google", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "azure-openai", "provider": "azure openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}], "embeddingRequestsPerMin": 90, "embeddingBatchSize": 16, "disableIndexOnMobile": true, "showSuggestedPrompts": true, "showRelevantNotes": true, "numPartitions": 1, "promptUsageTimestamps": {}, "promptSortStrategy": "timestamp", "defaultConversationNoteName": "{$topic}@{$date}_{$time}", "inlineEditCommands": [], "projectList": [], "enableAutocomplete": false, "autocompleteAcceptKey": "Tab", "allowAdditionalContext": true, "enableWordCompletion": false, "lastDismissedVersion": null, "passMarkdownImages": true, "enableCustomPromptTemplating": true, "suggestedDefaultCommands": true}