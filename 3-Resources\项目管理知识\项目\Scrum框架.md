工作流程
---
产品负责人将产品特征写成用户故事放入产品待办事项列表中，紧接着进行[[产品待办事项列表梳理活动]]，接下来由参与人开展Sprint讨论会共同讨论用户故事的优先级，并且决定当前Sprint要做的内容，并详细讨论生成Sprint Backlog（送代待办事项列表），会后参与人都必须对每个用户故事有准确并深刻地理解，才能开始进行执行，团队根据Sprint Backlog中的内容进行开发和测试，	并通过Sprint每日站会的方式，让团队成员交流了解完成了什么内容以及遇到了什么问题，Sprint 产出的成果成为潜在可发布的增量包（产品增量），在Sprint结束时举行Sprint评审会议，由开发团队向产品负责人进行Demo演示，产品负责人可接受或拒绝交付成果，根据实际情况决定是否对外发布，Sprint评审会议结束后召开Sprint回顾会议，Scrum团队一起反思工作中可改进的地方，并将行动的措施记录下来，在下一个sprint当中实施，从而持续地改进提高