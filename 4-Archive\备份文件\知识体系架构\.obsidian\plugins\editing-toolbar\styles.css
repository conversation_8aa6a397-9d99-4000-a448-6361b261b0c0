#cMenuToolbarModalBar {
  width: auto;
  height: auto;
  padding: 3px;
  display: grid;
  user-select: none;
  border-radius: 6px;
  position: absolute;
  transition: 100ms cubic-bezier(0.92, -0.53, 0.65, 1.21);
  -webkit-transition: 100ms cubic-bezier(0.92, -0.53, 0.65, 1.21);
  min-width: fit-content;
  justify-content: space-around;
  z-index: var(--layer-status-bar);

}

#cMenuToolbarModalBar.cMenuToolbarFlex {
  display: flex;
}

#cMenuToolbarModalBar.cMenuToolbarFlex :is(.cMenuToolbarCommandItem, button[class^=cMenuToolbarCommandsubItem]) {
  min-width: 20px;
}

#cMenuToolbarModalBar .cMenuToolbarCommandItem {
  margin: 2px;
  border: none;
  display: flex;
  cursor: pointer;
  padding: 5px 6px;
  box-shadow: none;
  margin-left: 3px;
  margin-right: 3px;
  position: relative;
  border-radius: 3px;
  font-size: initial !important;
  background-color: var(--background-primary-alt);
  height: auto;
}

#cMenuToolbarModalBar button.cMenuToolbarCommandItem:hover {
  background-color: var(--background-secondary);
}

/* #cMenuToolbarModalBar button.cMenuToolbarCommandItem svg {
  width: 1.3em;
  height: 1.3em;
} */

/*----------------------------------------------------------------
cMenuToolbar SETTINGS BUTTONS
----------------------------------------------------------------*/

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsButton.cMenuToolbarSettingsButtonAdd,
button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsButton.cMenuToolbarSettingsButtonAdd {
  background-color: var(--interactive-accent);
}

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsButton.cMenuToolbarSettingsButtonDelete,
button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsButton.cMenuToolbarSettingsButtonDelete {
  background-color: #989cab;
}

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsButton.cMenuToolbarSettingsButtonRefresh,
button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsButton.cMenuToolbarSettingsButtonRefresh {
  background-color: var(--text-accent);
}

button.cMenuToolbarSettingsButton {
  padding: 4px 14px;
  border-radius: 6px;
}



/*----------------------------------------------------------------
cMenuToolbar SETTING ITEMS
----------------------------------------------------------------*/
.setting-item.cMenuToolbarCommandItem:first-child {
  padding-top: 18px;
}

.cMenuToolbarCommandItem {
  cursor: grab;
  padding: 18px 0 18px 0;
}

.cMenuToolbarSettingsTabsContainer .sortable-fallback {
  cursor: grabbing;
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
}

.cMenuToolbarSettingsTabsContainer .sortable-grab {
  cursor: grabbing !important;
}

.cMenuToolbarSettingsTabsContainer .sortable-ghost {
  opacity: 0.4;
  cursor: grabbing;
}

.cMenuToolbarSettingsTabsContainer .sortable-chosen {
  cursor: grabbing;
  padding: 18px 0 18px 18px;
  background-color: var(--color-base-10, --background-primary);
}

.cMenuToolbarSettingsTabsContainer .sortable-drag {
  cursor: grabbing;
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
}

.cMenuToolbarSettingsTabsContainer {
  border-top: 1px solid var(--background-modifier-border);
  border-bottom: 1px solid var(--background-modifier-border);
}

/*----------------------------------------------------------------
cMenuToolbar CLASS CHANGES
----------------------------------------------------------------*/

#cMenuToolbarModalBar.cMenuToolbarDefaultAesthetic {
  border: 1px solid var(--background-modifier-border);
}

#cMenuToolbarModalBar.cMenuToolbarDefaultAesthetic:not(.top) :is(.cMenuToolbarCommandItem, button[class^=cMenuToolbarCommandsubItem]) {
  min-height: 28px;
}

#cMenuToolbarModalBar.cMenuToolbarDefaultAesthetic:not(.top) button[class^=cMenuToolbarCommandsubItem] {
  margin: auto;
  padding: 6px;
  box-shadow: none;
  border: none;
  background-color: transparent;

  place-items: center;

}

.cMenuToolbarDefaultAesthetic {
  background-color: var(--color-base-10, --background-primary);
}


#cMenuToolbarModalBar.cMenuToolbarGlassAesthetic,
#cMenuToolbarModalBar.cMenuToolbarGlassAesthetic~#cMenuToolbarPopoverBar {
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  box-shadow: none;
  background-color: transparent;
}

#cMenuToolbarModalBar.cMenuToolbarGlassAesthetic:not(.top) button[class^=cMenuToolbarCommandsubItem] {
  margin: auto;
  padding: 0px;
  box-shadow: none;
  border: none;
  display: flex;
  border-radius: 3px;
  font-size: 10px;
  margin-right: 0px;
  position: relative;
  background-color: transparent;
  place-items: center;
  clear: both;
  max-width: 28px;
  max-height: 23px;
}

/*----------------------------------------------------------------
cMenuToolbar ICONS
----------------------------------------------------------------*/

.cMenuToolbarIconPick {
  line-height: normal;
  vertical-align: middle;
  margin-right: 8px;
}

.cMenuToolbarIconPick svg {
  width: 17px;
  height: 17px;
}

/*----------------------------------------------------------------
cMenuToolbar STATUS BAR MENU
----------------------------------------------------------------*/

.cMenuToolbar-statusbar-menu {
  text-align: center;
  width: 200px;
}


.cMenuToolbar-statusbar-menu .menu-item {
  display: flex;
  align-items: center;
  flex-direction: row;
}

.cMenuToolbar-statusbar-menu .menu-item.settings-item {
  font-size: 12px;
  text-align: center;
  line-height: 1;
  border-radius: 5px;
  height: auto;
  padding: 8px 5px 0px 5px;
  margin: 0 auto;
  width: fit-content;
  color: var(--text-faint);
}

.cMenuToolbar-statusbar-menu .menu-item.settings-item .menu-item-title {
  text-align: center;
}

.cMenuToolbar-statusbar-menu .menu-item:hover,
.cMenuToolbar-statusbar-menu .menu-item .selected:hover,
.cMenuToolbar-statusbar-menu .menu-item.selected:not(.is-disabled):not(.is-label) {
  background-color: transparent;
}

.cMenuToolbar-statusbar-menu .menu-item-title {
  margin-right: 10px;
}

.cMenuToolbar-statusbar-menu .slider {
  width: 100%;
}

.cMenuToolbar-statusbar-menu .menu-item.buttonitem {
  padding-top: 10px;
  padding-bottom: 4px;
  height: fit-content;
}

.cMenuToolbar-statusbar-menu .menu-item.buttonitem button.cMenuToolbarSettingsButton {
  margin: 0;
}

.cMenuToolbar-statusbar-menu .menu-item-icon svg path {
  fill: var(--text-muted);
}

.cMenuToolbar-statusbar-menu .menu-item-icon svg {
  stroke: var(--text-muted);
}

.cMenuToolbar-statusbar-menu>.menu-item:is([data-section="ButtonAdd"]) {
  display: inline-flex;
  padding: 0px 0px 5px 5px;
  align-items: center;
}

.cMenuToolbar-statusbar-menu>.menu-item {
  display: inline-flex;
  padding: 0px 0px 5px 5px;
  align-items: center;
}

.cMenuToolbar-statusbar-menu>.menu-item:is([data-section="ButtonAdd"]) .menu-item-icon {
  text-align: center;
}


/*----------------------------------------------------------------
cMenuToolbar STATUS BAR BUTTONS
----------------------------------------------------------------*/

.cMenuToolbar-statusbar-button {
  cursor: pointer;
  display: flex;
  align-items: center;
  line-height: 1;
}

.cMenuToolbar-statusbar-button svg {
  display: flex;
  width: 1.3em;
  height: 1.3em;
}

/*----------------------------------------------------------------
cMenuToolbar SUPPORT
---------------------------------------------`-------------------*/

.cDonationSection {
  width: 60%;
  height: 50vh;
  margin: 0 auto;
  text-align: center;
  color: var(--text-normal);
}


#cMenuToolbarModalBar {
  align-items: center;
  justify-items: center;
  border: none;
  backdrop-filter: none;
}

#cMenuToolbarModalBar.cMenuToolbarGlassAesthetic .cMenuToolbarCommandItem {

  background-color: #ffffff00;
}

#cMenuToolbarModalBar.cMenuToolbarGlassAesthetic [class^=cMenuToolbarCommandsubItem] {

  background-color: #ffffff00;
}

#cMenuToolbarModalBar .cMenuToolbarCommandItem {
  justify-content: center;
  align-content: center;
  place-items: center;

}


/*  #cMenuToolbarModalBar.cMenuToolbarTinyAesthetic .cMenuToolbarCommandItem svg{
     width: 1em;
     height: 1em;
 } */



div.modal-container.cMenuToolbar-Modal:not(.changename) .modal-bg {
  background-color: transparent !important;
  backdrop-filter: none !important;
  position: relative;
}

.modal-container.cMenuToolbar-Modal:not(.changename) .modal {
  padding: 10px 30px;
  min-width: 130px;
  position: absolute;
  bottom: 2em;
  right: 0.5em;
  background-color: rgb(var(--mono-rgb-0), 0.8);
  backdrop-filter: blur(4px);
}

.modal-container.cMenuToolbar-Modal .modal-title {
  display: none;
}

.modal-container.cMenuToolbar-Modal .modal input[type='range'] {
  width: 90%;

}

body.theme-dark .modal-container.cMenuToolbar-Modal .modal input[type='range'] {
  background-color: var(--background-secondary);
}

/*tiny 样式*/
#cMenuToolbarModalBar.cMenuToolbarTinyAesthetic {
  align-items: center;
  justify-items: center;
  border: none;
  backdrop-filter: none;
  background-color: var(--color-base-10, --background-primary);
 
}


#cMenuToolbarModalBar.cMenuToolbarTinyAesthetic .cMenuToolbarCommandItem {
  margin: auto;
  padding: 0px;
  box-shadow: none;
  margin-left: 0px;
  margin-right: 0px;
  position: relative;
  background-color: transparent;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

}

#cMenuToolbarModalBar .cMenuToolbarCommandItem {
  margin: auto;

  padding: 0px;
  box-shadow: none;

  margin-left: 4px;
  margin-right: 0px;
  position: relative;
  background-color: transparent;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

}


:is(#cMenuToolbarModalBar).cMenuToolbarTinyAesthetic:not(.top) button[class^=cMenuToolbarCommandsubItem] {
  margin: auto;
  padding: 0px;
  box-shadow: none;
  border: none;
  display: flex;
  border-radius: 3px;
  font-size: 10px;
  margin-right: 0px;
  position: relative;
  background-color: transparent;

  place-items: center;
  clear: both;
  max-width: 28px;
  max-height: 23px;

}

button[class^=cMenuToolbarCommandsubItem]::after {

  content: url("data:image/svg+xml,%3Csvg width='4' height='4' version='1.1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1024 1024' enable-background='new 0 0 1024 1024' xml:space='preserve'%3E%3Cpath fill='%23666' d='M13.24 80.11 l461.75 560.8 q14.56 16.02 36.41 16.02 q21.85 0 36.42 -16.02 l463.2 -560.8 q10.2 -10.19 12.38 -24.75 q2.18 -14.57 -3.64 -27.68 q-5.82 -13.11 -18.21 -20.39 q-12.39 -7.29 -26.95 -7.29 l-924.95 0 q-20.4 0 -34.23 13.11 q-13.84 13.11 -15.29 32.77 q-1.46 19.66 13.11 34.23 Z'/%3E%3C/svg%3E");

  margin-left: 1px;
  margin-top: 6px;

}


:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button[class^=cMenuToolbarCommandsubItem]>.subitem {
  background-color: var(--color-base-10, --background-primary);
  border-radius: 4px;
  border: 1px solid var(--background-modifier-border);
  
  position: absolute;
  z-index: var(--layer-menu);
  user-select: none;
  transform: translateY(90%) translateX(0%);
  -webkit-transform: translateY(90%) translateX(0%);
  display: flex;

}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button[class^=cMenuToolbarCommandsubItem]>.subitem svg {
  max-width: 1.3em;
  max-height: 1.3em;
}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button[class^=cMenuToolbarCommandsubItem]>.subitem button.menu-item {
  background-color: var(--background-secondary);
  line-height: 2em;
  display: inline-flex;
  box-shadow: none;
  align-items: center;
}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button[class^=cMenuToolbarCommandsubItem]>.subitem button:hover {
  background-color: var(--interactive-hover);
  box-shadow: var(--input-shadow-hover);
}


:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button[class^=cMenuToolbarCommandsubItem]>.subitem .menu-item {
  margin-left: 2px;
  margin-right: 2px;
  padding: 0px 4px 0px 4px;
}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button[class^=cMenuToolbarCommandsubItem]>.subitem {

  visibility: hidden;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
}





:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button.cMenuToolbarCommandsubItem-font-color .triangle-icon {
  position: absolute;
  margin-left: 18px;
  bottom: 8%;
  background-size: 4px 4px;
  background-image: url("data:image/svg+xml,%3Csvg width='4' height='4' version='1.1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1024 1024' enable-background='new 0 0 1024 1024' xml:space='preserve'%3E%3Cpath fill='%23666' d='M13.24 80.11 l461.75 560.8 q14.56 16.02 36.41 16.02 q21.85 0 36.42 -16.02 l463.2 -560.8 q10.2 -10.19 12.38 -24.75 q2.18 -14.57 -3.64 -27.68 q-5.82 -13.11 -18.21 -20.39 q-12.39 -7.29 -26.95 -7.29 l-924.95 0 q-20.4 0 -34.23 13.11 q-13.84 13.11 -15.29 32.77 q-1.46 19.66 13.11 34.23 Z'/%3E%3C/svg%3E");

  width: 16px;
  height: 20px;
  background-position: center;

  background-repeat: no-repeat;
  min-width: unset;
  border-left: 2px solid transparent;
}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar):not(.top) button.cMenuToolbarCommandsubItem-font-color .triangle-icon {
  margin-left: 16px;

}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar):not(.top) button.cMenuToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  top: auto;
  bottom: calc(100%);
}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar):not(.top) button[class^=cMenuToolbarCommandsubItem]:not(.cMenuToolbarSecond)>.subitem {
  bottom: calc(100% + 2.8em);
}


:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button.cMenuToolbarCommandsubItem-font-color .subitem {
  visibility: hidden;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button.cMenuToolbarCommandsubItem-font-color .subitem:hover {
  visibility: visible;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
}


:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button[class^=cMenuToolbarCommandsubItem]:hover>.subitem {

  visibility: visible;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
}

/* :is(#cMenuToolbarModalBar,#cMenuToolbarPopoverBar) button[class^=cMenuToolbarCommandsubItem] >.subitem:hover  {
  visibility:visible;
  transition: all 0.3s linear;
  -webkit-transition:  all 0.3s linear;
 } */

.cMenuToolbarCommandsubItem-font-color button {
  background-color: transparent;
}

.cMenuToolbarCommandsubItem-font-color button:hover {
  background-color: var(--interactive-normal);
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbarCommandItem .setting-item-info {

  flex: 30%;
  margin: 0;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbarSettingsTabsContainer_sub {
  border-left: 1px solid var(--background-modifier-border);
  flex-flow: column;
  min-height: 45px;
  display: flex;
  padding: 0;
  margin-left: 10px;
  flex: 70%;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbarSettingsTabsContainer_sub:empty {
  border: 2px dashed rgba(var(--interactive-accent-rgb), 0.5);
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbarSettingsTabsContainer_sub:empty::before {
  content: "✖️Drag it here";
  margin: auto;
  font-size: 12px;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbarSettingsTabsContainer_sub .cMenuToolbarCommandItem {
  flex: auto;
  margin-left: 2em;
  ;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbarSettingsTabsContainer_sub .setting-item-control {
  flex: 0;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbarCommandSubItem>.setting-item-info {
  flex: 70px;
}

.cMenuToolbarCommandSubItem>.setting-item-control {

  justify-content: flex-start;
}

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsButton.cMenuToolbarSettingsButtonaddsub {
  background-color: var(--background-secondary-alt);
}


.setting-item button.cMenuToolbarSettingsIcon {
  display: block;
  transform: translateX(-30%);
  -webkit-transform: translateX(-30%);
}

.setting-item button.cMenuToolbarSettingsIcon:empty::before {
  content: "❗";
}

.setting-item button.cMenuToolbarSettingsIcon svg {
  max-width: 1.5em;
  max-height: 1.5em;
}

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsIcon:hover {
  background-color: var(--interactive-accent-hover);
}

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).cMenuToolbarSettingsIcon {
  background-color: transparent;
  box-shadow: 0 1px 1px 0px var(--background-modifier-border);
}

@media screen and (min-width: 781px) {
  .cMenuToolbar-Modal .wideInputPromptInputEl {
    width: 40rem;
    max-width: 100%;
    height: 20rem;
    background-color: rgb(var(--mono-rgb-0), 0.8);
  }
}

.cMenuToolbarcustomIcon svg {
  max-width: 1.3em;
  max-height: 1.3em;
  display: flex;
}


.cMenuToolbarSettingsButton svg {
  max-width: 1.3em;
  max-height: 1.3em;
  display: flex;
}

.cmdr-page-header {
  min-width: 1em;
  ;
}


.x-color-picker-wrapper {
  right: 0px;
  top: 1.8em;
  min-width: 1px;
  padding: 10px;
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14);
  position: absolute;
  width: fit-content;
  font-weight: 400;
  font-family: Source Sans Pro, sans-serif;
  border-radius: 4px;
  background-color: var(--color-base-10, --background-primary);
}

.markdown-source-view.mod-cm6 .x-color-picker-wrapper table.x-color-picker-table#x-color-picker-table {
  width: unset;
  border-collapse: separate;
  border-spacing: 6px;
  margin: auto !important;
}

.x-color-picker-wrapper table.x-color-picker-table#x-backgroundcolor-picker-table {
  width: unset;
  border-collapse: separate;
  border-spacing: 6px;
  margin: auto !important;
}

.x-color-picker-wrapper .x-color-picker-table th {
  border: 0;
  text-align: left;
  font-weight: normal;
  background: transparent !important;
  color: #718096;
}

.x-color-picker-wrapper #x-color-picker-table td {
  font-size: 1px;
  padding: 9px;
  cursor: pointer;
  border: solid 1px var(--background-modifier-border);
}

.x-color-picker-wrapper #x-backgroundcolor-picker-table td {
  font-size: 1px;
  border-radius: 50%;
  padding: 9px;
  cursor: pointer;
  border: solid 1px var(--background-modifier-border);
}

.x-color-picker-wrapper .x-color-picker-table tr td:hover {
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05);
}

.x-color-picker-wrapper tbody>tr:hover {
  background-color: transparent !important;
}

/**top**/
#cMenuToolbarModalBar.top {
  display: flex;
  flex-wrap: nowrap;
  position: relative;
  height: 38px;
  align-items: center;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;

}

#cMenuToolbarModalBar.top.autohide {
  opacity: 0;
}


#cMenuToolbarModalBar.top.autohide:hover {
  opacity: 1;
  transition: all 0.5s linear;
}


#cMenuToolbarModalBar.top :is(.cMenuToolbarCommandItem, button[class^=cMenuToolbarCommandsubItem]):not(.cMenuToolbar-Divider-Line) {
  font-size: 10px;
  margin-right: 0px;
  clear: both;
  opacity: 1;
  flex-shrink: 0;
  height: 26px;
  padding: 0;
  border: none;
  background: transparent;
  cursor: pointer;
  outline: none;
  box-shadow: none;
  border-radius: 2px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;




}

#cMenuToolbarModalBar.top button.cMenuToolbarCommandItem:hover {
  background-color: var(--interactive-hover);
}



#cMenuToolbarPopoverBar {

  padding: 0 10px;
  display: inline-flex;
  align-items: center;
  width: fit-content;
  z-index: var(--layer-status-bar);
  background-color: var(--color-base-10, --background-primary);
  background-clip: padding-box;
  border-radius: 6px;

  margin-left: auto;
  margin-right: 25px;
  transition: all 0.1s linear;
  -webkit-transition: all 0.1s linear;
  margin-top: 32px;
  position: absolute;
  right: 0;
}

#cMenuToolbarPopoverBar :is(.cMenuToolbarCommandItem, button[class^=cMenuToolbarCommandsubItem]) {

  height: 26px;
  margin-left: 4px;
  font-size: 10px;
  margin-right: 4px;
  clear: both;
  opacity: 1;
  flex-shrink: 0;
  height: 26px;
  padding: 0;
  border: none;
  background: transparent;
  cursor: pointer;
  outline: none;
  box-shadow: none;
  border-radius: 2px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;

}

#cMenuToolbarPopoverBar :is(.cMenuToolbarCommandItem, button[class^=cMenuToolbarCommandsubItem]):hover {
  background-color: var(--interactive-hover);
}

#cMenuToolbarModalBar .more-menu {
  display: flex;
  align-items: center;
  box-shadow: none;
  margin-left: 4px;
  border-left: 1px inset var(--background-modifier-form-field);
  height: 24px;
  opacity: 0.8;
}

/*Divider-Line**/

.cMenuToolbarSettingsTabsContainer .cMenuToolbar-Divider-Line {
  padding: 0;

  line-height: 0px;
  border-left: 200px solid rgba(var(--interactive-accent-rgb), 0.05);
  border-right: 200px solid rgba(var(--interactive-accent-rgb), 0.05);
  text-align: center;
  background: rgba(var(--interactive-accent-rgb), 0.2);
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbar-Divider-Line .setting-item-control button:not(:last-child) {
  display: none;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbar-Divider-Line .setting-item-info {
  flex: 1 1 auto;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbar-Divider-Line .setting-item-control {
  justify-content: flex-start;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbar-Divider-Line .setting-item-info .setting-item-name {
  font-size: 12px;
  text-align: right;

}

.cMenuToolbarSettingsTabsContainer .cMenuToolbar-Divider-Line .setting-item-control button:last-child {
  padding: 0;
  background-color: transparent !important;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbar-Divider-Line .setting-item-control button:last-child svg {
  display: none;
}

.cMenuToolbarSettingsTabsContainer .cMenuToolbar-Divider-Line .setting-item-control button:last-child::before {
  content: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M511.674077 66.707284c-246.52265 0-446.347744 199.835328-446.347744 446.347744s199.825095 446.356954 446.347744 446.356954c246.50423 0 446.348768-199.844537 446.348768-446.356954S758.177284 66.707284 511.674077 66.707284zM744.967424 667.159826c21.8701 21.8701 21.8701 57.310264 0 79.199807-21.8701 21.851681-57.30924 21.851681-79.198783-0.019443L511.674077 592.264045 357.56007 746.359632c-21.8701 21.8701-57.30924 21.851681-79.17934-0.019443s-21.8701-57.290821 0-79.160921L432.493713 513.065262 278.379707 358.950232c-21.8701-21.86089-21.8701-57.328683 0-79.18855 21.8701-21.87931 57.30924-21.87931 79.17934 0l154.114007 154.104797 154.095587-154.104797c21.889543-21.87931 57.32766-21.87931 79.198783-0.010233 21.8701 21.8701 21.8701 57.348126 0 79.207993L590.89128 513.065262 744.967424 667.159826z' fill='%23666666'/%3E%3C/svg%3E");
}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button.cMenuToolbarCommandItem.cMenuToolbar-Divider-Line {
  min-width: unset;
  flex-shrink: 0;
  display: inline-flex;
  width: 0.6px;
  background-color: var(--background-modifier-border);
  height: 22px;
  opacity: 0.8;
  margin: 0;
}

.theme-dark :is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button.cMenuToolbarCommandItem.cMenuToolbar-Divider-Line {

  background-color: #4f4f5188;

}

:is(#cMenuToolbarModalBar, #cMenuToolbarPopoverBar) button.cMenuToolbarCommandItem.cMenuToolbar-Divider-Line svg {
  display: none;
}

.workspace-tabs.mod-stacked .workspace-tab-header:not(.is-active)+.workspace-leaf #cMenuToolbarModalBar {
  opacity: 0;
}

:is(.cm-line, p) span[style^="background:rgba"] {
  color: var(--text-normal);
}

:is(.cm-line, p) span[style^="background:#"] {
  color: black;
}

.setting-item.toolbar-cta:after {
  content: "";
  position: absolute;
  top: -10%;
  width: 104%;
  left: -2%;
  height: 120%;
  outline: 2px solid var(--text-accent);
  border-radius: 1em;
  pointer-events: none;
}

.setting-item.toolbar-cta {
  position: relative;
}