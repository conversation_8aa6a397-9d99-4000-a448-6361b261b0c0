# AI-Gist：个人提示词本地管理AI工具

## 工具概述
AI-Gist是一款**本地优先**的AI提示词管理工具，致力于让个人收藏的AI提示词发挥最大价值。该工具开源免费，支持多平台使用。

## 基本信息
- **开发者**：yarin-zhang
- **类型**：开源免费
- **平台**：Windows / macOS / Linux
- **语言**：简体中文、繁体中文、英文、日语
- **官网**：[getaigist.com](https://getaigist.com)
- **GitHub**：[yarin-zhang/AI-Gist](https://github.com/yarin-zhang/AI-Gist)
- **发现时间**：2025-07-27

## 核心特性

### 🔧 基础管理功能
- **变量填充**：调用模板时动态填入变量，结构清晰灵活，支持Jinja模板
- **多视图管理**：集中管理提示词模板，支持卡片视图、表格视图、分类视图
- **筛选分类**：快速筛选、查找、组织提示词，支持标签、分类、评分、收藏等功能
- **历史记录**：便于重复调用与持续优化，事后可回溯

### 🤖 AI集成功能
- **接入多种AI模型**：支持本地模型（Ollama、LM Studio）和在线模型（OpenAI等）
- **AI生成**：使用AI快捷生成提示词，支持自定义系统提示词
- **AI调优**：使用AI改写提示词，快速让提示词更具体、更丰富
- **AI提取变量**：无需手动挖空，使用AI自动提取可能的变量

### 🔒 隐私与数据安全
- **本地优先**：所有数据存储在本地，默认情况无需联网，确保隐私和安全
- **掌控数据**：拥有完整的数据控制，支持完整导出和导入，通用格式CSV导出
- **云端备份**：支持WebDAV、iCloud备份与恢复，方便在多设备间共享数据

## 主要功能详解

### 变量模板系统
- 支持Jinja模板语法
- 动态变量填充，避免手动修改长提示词
- 可设置变量类型和默认值
- 支持全局变量（建议功能）

### AI辅助功能
- **AI生成提示词**：输入简单描述，AI自动生成完整提示词
- **AI优化提示词**：改写现有提示词，使其更具体、更丰富
- **AI提取变量**：自动识别提示词中可变部分，生成模板

### 数据管理
- **历史版本记录**：每次更新自动保存历史版本
- **备注系统**：支持为每个提示词添加备注信息
- **导入导出**：支持CSV格式的批量导入导出
- **云端同步**：WebDAV、iCloud等多种备份方式

## 使用场景

### 个人用户
1. **提示词收藏管理**：替代传统的Obsidian等工具，专门管理AI提示词
2. **变量化模板**：避免手动修改长提示词中的特定内容
3. **AI辅助创作**：懒得想提示词时，让AI帮助生成和优化
4. **版本管理**：跟踪提示词的优化历程

### 团队协作
1. **多设备同步**：通过云端备份在多设备间同步提示词库
2. **标准化管理**：统一团队的提示词格式和标准
3. **知识积累**：建立团队的提示词知识库

## 开发背景与痛点解决

### 开发者遇到的问题
1. **管理困难**：网上收藏的优质提示词难以管理和筛选
2. **格式不统一**：不同AI的提示词格式不统一
3. **变量替换繁琐**：长提示词中的变量替换操作繁琐
4. **隐私担忧**：担心将精调过的提示词放到在线工具中

### AI-Gist的解决方案
1. **专业化管理**：专门针对提示词的管理工具
2. **模板化处理**：通过变量模板统一格式
3. **自动化操作**：AI辅助生成和优化
4. **本地优先**：确保数据隐私和安全

## 用户反馈与改进建议

### 用户建议
1. **UI优化**：界面留白过大，建议更紧凑的设计
2. **快捷操作**：
   - 关闭动画选项，提升操作速度
   - 默认光标定位到第一个变量框
   - 支持快捷键操作
3. **功能增强**：
   - 全局变量支持
   - 系统托盘快速访问
   - 文本直接上屏功能

### 已知问题
1. 编辑补充信息后，Jinja变量的类型和默认值可能丢失
2. Gemini API连接测试可能存在问题

## 竞品对比

### 优势
- **开源免费**：相比国外收费产品，完全免费
- **本地优先**：数据安全性更高
- **中文支持**：针对中文用户优化
- **AI集成**：内置AI辅助功能

### 适用人群
- 经常使用AI的个人用户
- 需要管理大量提示词的专业用户
- 注重数据隐私的用户
- 希望提升AI使用效率的用户

## 安装与使用

### 下载安装
1. 访问[GitHub Releases](https://github.com/yarin-zhang/AI-Gist/releases)
2. 下载对应平台的安装包
3. 按照说明进行安装

### 基本使用流程
1. **创建提示词**：新建 → 填写内容 → 设置变量
2. **使用模板**：选择提示词 → 填入变量 → 生成最终文本
3. **AI辅助**：使用AI生成、优化或提取变量功能
4. **管理组织**：通过标签、分类、评分等方式组织提示词

## 总结评价

AI-Gist是一款非常实用的提示词管理工具，特别适合经常使用AI的用户。其本地优先的设计理念、丰富的管理功能和AI辅助特性，很好地解决了提示词管理的痛点。虽然在UI设计和一些细节功能上还有改进空间，但作为一个开源免费的工具，已经具备了很高的实用价值。

**推荐指数**：⭐⭐⭐⭐⭐

---

*记录时间：2025-07-27*  
*信息来源：小众软件论坛、GitHub*  
*更新状态：持续关注*
