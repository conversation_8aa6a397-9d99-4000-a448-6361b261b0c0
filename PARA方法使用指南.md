# PARA方法使用指南

## 什么是PARA方法

PARA是一种数字化知识管理系统，由Tiago Forte创建。它将所有信息分为四个类别：

- **P**rojects（项目）
- **A**reas（领域）  
- **R**esources（资源）
- **A**rchive（归档）

## 四个分类的定义

### 1-Projects（项目）
**定义**：有明确截止日期和具体结果的一系列任务
**特点**：
- 有明确的开始和结束时间
- 有具体的可交付成果
- 需要主动推进

**示例**：
- 赚钱项目：具体的商业项目
- 学习项目：某个技能的学习计划
- 写作项目：文章、书籍等创作项目

### 2-Areas（领域）
**定义**：需要持续维护但没有结束日期的生活和工作领域
**特点**：
- 持续性的责任和标准
- 需要定期维护
- 没有明确的完成时间

**示例**：
- 健康管理：健身、饮食、医疗
- 财务管理：预算、投资、理财
- 职业发展：技能提升、人脉建设
- 个人成长：习惯养成、思维提升

### 3-Resources（资源）
**定义**：未来可能有用的主题或兴趣点
**特点**：
- 参考性质的信息
- 可能在未来的项目或领域中使用
- 按主题组织

**示例**：
- AI工具：各种AI工具的使用方法
- 方法论：思维模型、工作方法
- 项目管理知识：理论、最佳实践
- 参考资料：行业报告、研究资料

### 4-Archive（归档）
**定义**：来自其他三个类别的非活跃项目
**特点**：
- 已完成或暂停的内容
- 不再需要定期查看
- 保留以备将来参考

**示例**：
- 已完成项目：完成的学习项目、商业项目
- 过期内容：过时的信息、旧版本文件
- 备份文件：重要文件的备份

## 使用原则

### 1. 按可操作性排序
Projects > Areas > Resources > Archive
优先处理项目，然后是领域维护，最后是资源学习

### 2. 定期回顾和整理
- **每周回顾**：检查项目进展，更新任务状态
- **每月整理**：清理完成的项目到归档，评估领域状态
- **每季度优化**：重新评估分类，调整结构

### 3. 灵活迁移
内容会在四个分类间流动：
- Resources → Projects：资源变成具体项目
- Projects → Archive：项目完成后归档
- Areas → Projects：领域中产生具体项目

## 文件命名规范

### 项目文件
- 格式：`[状态] 项目名称 - 截止日期`
- 示例：`[进行中] 小红书运营项目 - 2025-08-15`

### 领域文件
- 格式：`领域名称 - 子分类`
- 示例：`健康管理 - 运动计划`

### 资源文件
- 格式：`主题 - 具体内容`
- 示例：`AI工具 - ChatGPT使用技巧`

## 标签系统

配合PARA使用标签：
- `#项目/进行中` `#项目/已完成` `#项目/暂停`
- `#领域/健康` `#领域/财务` `#领域/职业`
- `#资源/工具` `#资源/方法` `#资源/参考`
- `#归档/2025` `#归档/已完成`

## 维护建议

1. **收集箱机制**：新信息先放入临时收集区，定期分类
2. **定期清理**：避免信息堆积，保持系统整洁
3. **链接建立**：在相关笔记间建立双向链接
4. **模板使用**：为不同类型的内容创建标准模板

---
*创建时间：2025-07-17*
*用途：PARA方法实施指南*
